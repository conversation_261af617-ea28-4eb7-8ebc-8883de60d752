// Program 2: Monitor users and system_messages tables and send messages to users

const { Telegraf } = require('telegraf');
const { createClient } = require('@supabase/supabase-js');

const botToken = '7148756337:AAHYnQGq18_D9GVhWfj9pzSELIUJxdPtQxA';
const supabaseUrl = 'https://wjanjmsywbydjbfrdkaz.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6IndqYW5qbXN5d2J5ZGpiZnJka2F6Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3MjAxMjc1OTEsImV4cCI6MjAzNTcwMzU5MX0.ny-61EeDBsVRUr6BdWiamt9oV-6z2TC_f01FJff-9fE';

const bot = new Telegraf(botToken);
const supabase = createClient(supabaseUrl, supabaseKey);

// 获取所有用户的 user_id
const fetchAllUserIds = async () => {
  const { data, error } = await supabase.from('users').select('user_id');

  if (error) {
    console.error('Error fetching all user IDs:', error);
    return [];
  }

  return data.map((item) => item.user_id);
};

// 处理 users 表更新的函数
const onUserFieldChange = async (payload) => {
  const newRecord = payload.new;
  const oldRecord = payload.old;

  const userId = newRecord.user_id; // 使用 'user_id' 字段

  // 检查 sublink 字段是否发生变化
  if (oldRecord.sublink !== newRecord.sublink) {
    const sublink = newRecord.sublink;

    // 发送 'click to pay' 的可点击链接，并在20秒后删除
    try {
      const message = await bot.telegram.sendMessage(
        userId,
        'Click to pay',
        {
          reply_markup: {
            inline_keyboard: [[{ text: 'Click to pay', url: sublink }]],
          },
        }
      );
      console.log(`Sent sublink to user ${userId}`);

      // 在20秒后删除消息
      setTimeout(() => {
        bot.telegram.deleteMessage(userId, message.message_id).catch((error) => {
          console.error(`Failed to delete message for user ${userId}:`, error);
        });
      }, 20000);
    } catch (error) {
      console.error(`Failed to send sublink to user ${userId}:`, error);
    }
  }

  // 检查 billing 字段是否发生变化
  if (oldRecord.billing !== newRecord.billing) {
    const billingLink = newRecord.billing;

    // 发送 'click to manage' 的可点击链接，并在20秒后删除
    try {
      const message = await bot.telegram.sendMessage(
        userId,
        'Click to manage',
        {
          reply_markup: {
            inline_keyboard: [[{ text: 'Click to manage', url: billingLink }]],
          },
        }
      );
      console.log(`Sent billing link to user ${userId}`);

      // 在20秒后删除消息
      setTimeout(() => {
        bot.telegram.deleteMessage(userId, message.message_id).catch((error) => {
          console.error(`Failed to delete message for user ${userId}:`, error);
        });
      }, 20000);
    } catch (error) {
      console.error(`Failed to send billing link to user ${userId}:`, error);
    }
  }

  // 检查 system 字段是否发生变化
  if (oldRecord.system !== newRecord.system) {
    const systemMessage = newRecord.system;

    // 发送消息给用户，不删除
    try {
      await bot.telegram.sendMessage(userId, systemMessage);
      console.log(`Sent system message to user ${userId}`);
    } catch (error) {
      console.error(`Failed to send system message to user ${userId}:`, error);
    }
  }
};

// 处理 system_messages 表更新的函数
const onAllFieldChange = async (payload) => {
  const newRecord = payload.new;
  const oldRecord = payload.old;

  // 检查 all 字段是否发生变化
  if (oldRecord.all !== newRecord.all) {
    const allMessage = newRecord.all;

    // 获取所有用户的 user_id
    const userIds = await fetchAllUserIds();

    // 发送消息给所有用户，不删除
    const sendPromises = userIds.map(async (userId) => {
      try {
        await bot.telegram.sendMessage(userId, allMessage);
        console.log(`Sent 'all' message to user ${userId}`);
      } catch (error) {
        console.error(`Failed to send 'all' message to user ${userId}:`, error);
      }
    });

    await Promise.all(sendPromises);
  }
};

// 创建一个频道，用于订阅 users 和 system_messages 表的更新
const channel = supabase.channel('users-and-system-messages-channel');

// 订阅 users 表的更新
channel.on(
  'postgres_changes',
  {
    event: 'UPDATE',
    schema: 'public',
    table: 'users',
  },
  onUserFieldChange
);

// 订阅 system_messages 表的更新
channel.on(
  'postgres_changes',
  {
    event: 'UPDATE',
    schema: 'public',
    table: 'system_messages',
  },
  onAllFieldChange
);

// 开始订阅
channel.subscribe();

// 启动 bot
bot.launch();
console.log('Bot is running and waiting for events (Program 2)...');

// 优雅停止
process.once('SIGINT', () => bot.stop('SIGINT'));
process.once('SIGTERM', () => bot.stop('SIGTERM'));

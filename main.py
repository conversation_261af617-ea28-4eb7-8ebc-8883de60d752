import random
import os
import sys
import urllib.parse
import urllib.request
import configparser
import subprocess
import threading
import datetime
import time
import json
import re
import shutil
import signal
import psycopg2
from psycopg2 import pool
from typing import Any, Union
import concurrent.futures
import requests
import logging

last_anchors_count = 0

from spider import (
    get_douyin_stream_data,
    get_tiktok_stream_data,
)

from web_rid import (
    get_live_room_id,
    get_sec_user_id
)
from utils import (
    logger, check_md5,
    trace_error_decorator
)

def read_config_value(config_parser: configparser.RawConfigParser, section: str, option: str, default_value: Any) -> Union[str, int, bool]:
    try:
        return config_parser.get(section, option)
    except (configparser.NoSectionError, configparser.NoOptionError):
        return default_value

# 创建数据库连接池
pool = pool.SimpleConnectionPool(
    minconn=2,
    maxconn=30,
    host='aws-0-ap-northeast-1.pooler.supabase.com',
    port=5432,
    dbname='postgres',
    user='postgres.nathkbbkyfjthxpdqocu',
    password='4gyp2MJp84zxW.F',
    connect_timeout=60
)

def get_db_connection():
    """
    获取数据库连接
    :return: 数据库连接
    """
    return pool.getconn()

def release_db_connection(connection):
    """
    释放数据库连接
    :param connection: 要释放的数据库连接
    """
    pool.putconn(connection)

def get_anchors():
    global last_anchors_count
    retries = 5
    delay = 30
    for i in range(retries):
        try:
            connection = get_db_connection()
            cursor = connection.cursor()
            cursor.execute("SELECT anchor_id, anchor_name, live_url, new_url, status, tsid, is_recording, mp4id FROM anchors WHERE anchor_name IS DISTINCT FROM 'NoLongerExisted'")
            anchors = cursor.fetchall()
            current_anchors_count = len(anchors)
            if current_anchors_count > last_anchors_count:
                last_anchors_count = current_anchors_count
                cursor.close()
                release_db_connection(connection)
                return True, [(anchor_id, live_url, new_url, anchor_name, status, is_recording) for anchor_id, live_url, new_url, anchor_name, status, _, is_recording, _ in anchors]
            else:
                last_anchors_count = current_anchors_count
                cursor.close()
                release_db_connection(connection)
                return False, [(anchor_id, live_url, new_url, anchor_name, status, is_recording) for anchor_id, live_url, new_url, anchor_name, status, _, is_recording, _ in anchors]
        except psycopg2.Error as e:
            logger.warning(f"数据库操作失败(尝试 {i+1}/{retries}): {str(e)}")
            if i < retries - 1:
                time.sleep(delay)
            else:
                raise e
            
def update_anchor_info(anchor_id, live_url, new_url, anchor_name):
    connection = get_db_connection()
    cursor = connection.cursor()
    logger.debug(f"{anchor_name}, new_url: {new_url}, anchor_name: {anchor_name}")
    
    # 更新主播信息
    cursor.execute("UPDATE anchors SET new_url=%s, anchor_name=%s WHERE anchor_id=%s",
                (new_url, anchor_name, anchor_id))
    
    connection.commit()
    cursor.close()
    release_db_connection(connection)
    
def update_anchor_status(anchor_id, live_status):
    connection = get_db_connection()
    cursor = connection.cursor()
    logger.debug(f"{anchor_id}, status: {live_status}")
    
    # 更新主播状态
    cursor.execute("UPDATE anchors SET status=%s WHERE anchor_id=%s",
                (live_status, anchor_id))
    
    connection.commit()
    cursor.close()
    release_db_connection(connection)

@trace_error_decorator
def parse_douyin(douyin_url):
    """
    解析抖音直播链接
    :param douyin_url: 抖音直播链接
    :return: 主播名字
    """
    json_data = get_douyin_stream_data(douyin_url)
    
    if isinstance(json_data, dict):
        anchor_name = json_data.get('anchor_name', '')
        return anchor_name, json_data.get('status') == 2
        
    return '', False

@trace_error_decorator
def parse_douyin_short(short_url):
    """
    解析抖音短链接
    :param short_url: 抖音短链接
    :return: 解析后的链接、主播名字和直播状态
    """
    try:
        room_id, sec_user_id = get_sec_user_id(short_url)
        web_rid = get_live_room_id(room_id, sec_user_id)
        douyin_url = "https://live.douyin.com/" + str(web_rid)
        anchor_name, live_status = parse_douyin(douyin_url)
        return douyin_url, anchor_name, live_status
    except Exception as e:
        logger.warning(f"解析抖音短链接失败: {short_url}, 错误信息: {e}")
        return '', '', False

@trace_error_decorator
def parse_tiktok(tiktok_url):
    """
    解析TikTok直播链接
    :param tiktok_url: TikTok直播链接
    :return: 解析后的链接、主播名字和直播状态
    """
    try:
        json_data = get_tiktok_stream_data(url=tiktok_url)
        
        if isinstance(json_data, dict) and 'LiveRoom' in json_data:
            live_room = json_data['LiveRoom']['liveRoomUserInfo']
            unique_id = live_room['user']['uniqueId'] if 'user' in live_room else ''
            anchor_name = live_room['user']['nickname'] if 'user' in live_room else ''
            live_status = live_room['user']['status'] == 2 if 'user' in live_room else False
            tiktok_url = f"https://www.tiktok.com/@{unique_id}/live"
            return tiktok_url, anchor_name, live_status
            
        return tiktok_url, '', False
        
    except Exception as e:
        logger.error(f"解析TikTok链接失败: {tiktok_url}, 错误信息: {e}")
        raise

def parse_url(live_url):
    """
    解析直播链接
    :param live_url: 直播链接
    :return: 解析后的链接、主播名字和直播状态
    """
    if live_url.startswith('https://live.douyin.com/'):
        anchor_name, live_status = parse_douyin(live_url)
        return live_url, anchor_name, live_status
    elif live_url.startswith('https://v.douyin.com/'):
        return parse_douyin_short(live_url)
    elif live_url.startswith('https://www.tiktok.com/'):
        return parse_tiktok(live_url)
    elif live_url.startswith('https://vt.tiktok.com/'):
        return parse_tiktok(live_url)
    else:
        raise ValueError(f"不支持的链接类型: {live_url}")

def cleanup_database():
    connection = get_db_connection()
    cursor = connection.cursor()

    try:
        # 查询具有相同anchor_name的条目
        cursor.execute("SELECT anchor_id, anchor_name, new_url, live_url FROM anchors ORDER BY anchor_name")
        anchors = cursor.fetchall()

        processed_names = set()
        for i in range(len(anchors)):
            anchor_id, anchor_name, new_url, live_url = anchors[i]

            if anchor_name in processed_names:
                continue

            processed_names.add(anchor_name)

            # 找到具有相同anchor_name的所有条目
            same_name_anchors = [anchors[i]]
            for j in range(i + 1, len(anchors)):
                if anchors[j][1] == anchor_name:
                    same_name_anchors.append(anchors[j])
                else:
                    break

            # 比较URL并确定要删除的条目
            unique_urls = set()
            deleted_anchor_ids = []
            remaining_anchor_id = None
            for anchor in same_name_anchors:
                if anchor[2] in unique_urls or anchor[3] in unique_urls:
                    deleted_anchor_ids.append(anchor[0])
                else:
                    unique_urls.add(anchor[2])
                    unique_urls.add(anchor[3])
                    remaining_anchor_id = anchor[0]

            deleted_subscriptions = []  # 在 if 语句块之前定义和初始化变量

            # 删除user_subscriptions表中的相关记录
            if deleted_anchor_ids:
                for deleted_id in deleted_anchor_ids:
                    cursor.execute("SELECT user_id, anchor_id FROM user_subscriptions WHERE anchor_id = %s", (deleted_id,))
                    deleted_subscriptions.extend(cursor.fetchall())
                    cursor.execute("DELETE FROM user_subscriptions WHERE anchor_id = %s", (deleted_id,))

            # 删除anchors表中的重复条目
            for deleted_id in deleted_anchor_ids:
                cursor.execute("DELETE FROM anchors WHERE anchor_id = %s", (deleted_id,))

            # 将删除的user_subscriptions记录重新插入,并处理重复记录
            for user_id, _ in deleted_subscriptions:
                cursor.execute("SELECT COUNT(*) FROM user_subscriptions WHERE user_id = %s AND anchor_id = %s",
                               (user_id, remaining_anchor_id))
                count = cursor.fetchone()[0]
                if count == 0:
                    cursor.execute("INSERT INTO user_subscriptions (user_id, anchor_id) VALUES (%s, %s)",
                                   (user_id, remaining_anchor_id))

        connection.commit()
        print("数据库清理完成")

    except psycopg2.Error as e:
        connection.rollback()
        print(f"数据库操作失败: {str(e)}")

    finally:
        cursor.close()
        release_db_connection(connection)

def process_anchor(anchor):
    anchor_id, anchor_name, live_url, _,  _, _ = anchor
    try:
        # 使用live_url解析主播信息
        parsed_url, parsed_anchor_name, live_status = parse_url(live_url)
        
        # 如果解析成功,则更新主播信息到数据库
        if parsed_url and parsed_anchor_name:
            update_anchor_info(anchor_id, live_url, parsed_url, parsed_anchor_name)
            update_anchor_status(anchor_id, live_status)  # 更新主播状态
            logger.debug(f"解析成功: {anchor_name}, {live_url}, {parsed_anchor_name}")
        else:
            logger.warning(f"解析失败: {anchor_name}, {live_url}")
    except ValueError as e:
        if "not enough values to unpack" in str(e):
            logger.warning(f"主播信息解析失败,可能已注销: {live_url}, 错误信息: {e}")
            unpack_failed_anchors.append((anchor_id, live_url))  # 将失败的主播添加到列表中
        else:
            logger.warning(f"处理主播信息失败: {live_url}, 错误信息: {e}")
    except Exception as e:
        logger.warning(f"处理主播信息失败: {live_url}, 错误信息: {e}")

def process_anchor_status(anchor):
    anchor_id, _, _, new_url, _, _ = anchor
    try:
        if new_url:
            _, _, live_status = parse_url(new_url)
            update_anchor_status(anchor_id, live_status)
        else:
            logger.warning(f"主播 {anchor_id} 的 new_url 为空,跳过状态检查")
            
    except ValueError as e:
        if "不支持的链接类型" in str(e):
            logger.warning(f"不支持的链接类型: {new_url}")
        else:
            logger.warning(f"处理主播状态失败: {new_url}, 错误信息: {e}")
    except Exception as e:
        logger.warning(f"处理主播状态失败: {new_url}, 错误信息: {e}")

if __name__ == '__main__':
    unpack_failed_anchors = []
    hourly_update_minute = 0  # 每小时的0分更新
    last_hourly_update = None
    
    try:
        while True:
            has_new_anchors, anchors = get_anchors()  # 每次循环都从数据库获取最新的主播信息
            current_time = datetime.datetime.now()

            if has_new_anchors:
                with concurrent.futures.ThreadPoolExecutor() as executor:
                    futures = [executor.submit(process_anchor, anchor) for anchor in anchors]
                    concurrent.futures.wait(futures)

            # 每小时执行一次数据库清理和更新操作
            if last_hourly_update is None or current_time.minute == hourly_update_minute and current_time.timestamp() > last_hourly_update.timestamp() + 3600:
                # 执行数据库清理操作
                cleanup_database()

                # 更新主播信息
                with concurrent.futures.ThreadPoolExecutor() as executor:
                    futures = [executor.submit(process_anchor, anchor) for anchor in anchors]
                    concurrent.futures.wait(futures)

                last_hourly_update = current_time
                
                # 如果有主播解析失败,且"not enough values to unpack"错误不超过1次,更新数据库
                if 0 < len(unpack_failed_anchors) <= 1:
                    for anchor_id, live_url in unpack_failed_anchors:
                        try:
                            update_anchor_info(anchor_id, live_url, live_url, 'NoLongerExisted')
                        except Exception as e:
                            logger.error(f"更新主播信息失败: {live_url}, 错误信息: {e}")
                            
                # 如果"not enough values to unpack"错误超过1次,则认为可能是网络问题,不更新数据库
                elif len(unpack_failed_anchors) > 1:
                    logger.warning(f"本轮检查中有 {len(unpack_failed_anchors)} 个主播解析失败,出现了'not enough values to unpack'错误,但数量超过1,不进行 NoLongerExisted 标记。")
                
                unpack_failed_anchors = []  # 清空列表,为下一轮检查做准备

            # 每分钟更新一次直播状态
            with concurrent.futures.ThreadPoolExecutor() as executor:
                futures = [executor.submit(process_anchor_status, anchor) for anchor in anchors]
                concurrent.futures.wait(futures)
            
            time.sleep(60)  # 每次循环后等待60秒
            
    except KeyboardInterrupt:
        print("程序已被用户中断,正在清理并退出...")
        # 在这里添加清理代码,例如关闭数据库连接等
        print("清理完成,程序退出。")
{"metadata": {"comparison_time": "2025-08-29 02:09:14", "original_api": {"total_authors": 181, "total_works": 5542}, "tikhub_api": {"total_authors": 181, "total_works": 2908}, "differences_summary": {"authors_with_differences": 107, "total_authors_compared": 156}}, "detailed_comparison": [{"author_name": "亭湖刘昊然", "sec_uid": "MS4wLjABAAAAqlvdCNKIRWy3NAyduem3jKpXcV9wJ1PCP2gM9dfW8eE", "original_api": {"works_count": 43, "top10_ids": ["7215973607963495732", "6863027998300376332", "6824902482662001924", "7539564786943937851", "7538848318094429497", "7537327651255635259", "7536207527446007099", "7534714847490313531", "7534003155316985146", "7532195064781933884"]}, "tikhub_api": {"works_count": 23, "top10_ids": ["7215973607963495732", "6863027998300376332", "6824902482662001924", "7539564786943937851", "7538848318094429497", "7537327651255635259", "7536207527446007099", "7534714847490313531", "7534003155316985146", "7532195064781933884"]}, "differences": {"missing_in_original": [], "missing_in_tikhub": [], "count_diff": -20}}, {"author_name": "帅帅发光发亮", "sec_uid": "MS4wLjABAAAAL7Jb9ozOUWUWYd566NGmVxWQFVSm3hVB1KuN9IxYvdI", "original_api": {"works_count": 40, "top10_ids": ["7541338799038532921", "7540976863041604922", "7540562936100867388", "7540189769679572284", "7539850939059309883", "7538601689881185593", "7538346523123305786", "7538036803468430650", "7537265082045484348", "7536554448550776124"]}, "tikhub_api": {"works_count": 20, "top10_ids": ["7543573040047787322", "7543194547405868322", "7542825469742976271", "7542447446794800424", "7542094308720004367", "7541705061257678080", "7541338799038532921", "7540976863041604922", "7540562936100867388", "7540189769679572284"]}, "differences": {"missing_in_original": [{"position": 1, "aweme_id": "7543573040047787322", "title": "兄弟们，吃过的也算好的吗？#明天就是七夕了 #好兄弟就要一辈子在一起 #一定要看到最后"}, {"position": 2, "aweme_id": "7543194547405868322", "title": "千万别背着兄弟就睡觉！你跟你兄弟干过哪些离谱的事，今天的评论区是故事版，走不出去系列！#好喜欢和朋友在一起的状态 #内容过于真实 #好兄弟就要一辈子在一起 #一定要看到最后"}, {"position": 3, "aweme_id": "7542825469742976271", "title": "兄弟的脸都被你丢尽了，你有没有这种挂壁式兄弟#好喜欢和朋友在一起的状态 #七夕吃的不是菜是爱情的保鲜剂 #我有一个古的idea #好兄弟就要一辈子在一起"}, {"position": 4, "aweme_id": "7542447446794800424", "title": "在好兄弟面前是可以畅所欲言的，反正他会跟我一起！#好喜欢和朋友在一起的状态 #好兄弟就要一辈子在一起"}, {"position": 5, "aweme_id": "7542094308720004367", "title": "每个人都有自己独特的美丽！#每个人都有自己独特的美丽 #户外 #爱笑的男孩 #好兄弟就要一辈子在一起"}, {"position": 6, "aweme_id": "7541705061257678080", "title": "大学作业，各位老师请评判！#胆小慎入 #好兄弟就要一辈子在一起 #看到最后 #上热门🔥上热门"}], "missing_in_tikhub": [{"position": 5, "aweme_id": "7539850939059309883", "title": "你问我喝不喝咖啡，我刚要张嘴，当你说生椰拿铁的时候，我就知道，你懂我跟我懂你一样#东北轻工业 #好兄弟就要一辈子在一起"}, {"position": 6, "aweme_id": "7538601689881185593", "title": "口味可以换，但换口味之前该办的事也得办！#万万没想到 #好兄弟就要一辈子在一起 #红人直播 #意想不到的结局"}, {"position": 7, "aweme_id": "7538346523123305786", "title": "这次果然没骗我，好兄弟在心中！#dou是演技派 #是个狠人 #骚操作 #好兄弟就要一辈子在一起"}, {"position": 8, "aweme_id": "7538036803468430650", "title": "敬自己愚蠢但怀念的曾经！#敬还有更好的未来等着我们 #敬自己一杯 #敬可爱的小时候 #好兄弟就要一辈子在一起"}, {"position": 9, "aweme_id": "7537265082045484348", "title": "把你不听话的兄弟@在评论区#骚操作 #万万没想到 #好兄弟就要一辈子在一起"}, {"position": 10, "aweme_id": "7536554448550776124", "title": "有这么懂事的兄弟你几点回家？#反转 #看到最后 #万万没想到 #好兄弟就要一辈子在一起"}], "count_diff": -20}}, {"author_name": "风川😁", "sec_uid": "MS4wLjABAAAAbicaD5XZqXS8tpx1huEYWrAYFBdwacQsTXAnmBYIpLk", "original_api": {"works_count": 39, "top10_ids": ["7540224986520472842", "7536107718273797386", "7535425528796286259", "7534689521993600294", "7533854356933987610", "7531707485943041290", "7531332406223424777", "7530906301402287406", "7529852709017767218", "7529161519058914569"]}, "tikhub_api": {"works_count": 20, "top10_ids": ["7543552980735282478", "7543228218721406254", "7543168374131510537", "7542827284547620123", "7542466064081849638", "7542101544779533595", "7541705542457543982", "7540224986520472842", "7536107718273797386", "7535425528796286259"]}, "differences": {"missing_in_original": [{"position": 1, "aweme_id": "7543552980735282478", "title": "(ง •_•)ง 吃好喝好，长生不老\n\n(ง •_•)ง 白白胖胖，充满希望\n\n🍔🍟🍱🌯🥗🍕🍡🍣🍤#抖音ai创作"}, {"position": 2, "aweme_id": "7543228218721406254", "title": "天天减脂天天狂吃😁#变瘦 #健身 #肌肉 #减脂期 #減肥"}, {"position": 3, "aweme_id": "7543168374131510537", "title": "这特效真好看☺️#气质这一块拿捏死死的 #有点上头 #抖音特效"}, {"position": 4, "aweme_id": "7542827284547620123", "title": "好好锻炼，以后少划水😺#健身 #肌肉 #有肉感的小胖子 #荷尔蒙 #你的叔"}, {"position": 5, "aweme_id": "7542466064081849638", "title": "算匀称吗？练不大啊#肌肉线条 #男性魅力 #要做个猛男 #保持身材 #荷尔蒙"}, {"position": 6, "aweme_id": "7542101544779533595", "title": "嘿嘿嘿，不忍看第二遍🤣#热爱运动 #减脂 #自信魅力 #活力四射"}, {"position": 7, "aweme_id": "7541705542457543982", "title": "睡了一整天#保持身材 #健身 #自拍自拍 #荷尔蒙 #寸头"}], "missing_in_tikhub": [{"position": 4, "aweme_id": "7534689521993600294", "title": "🥲#你的叔 #小胖子 #毫无训练痕迹 #健身日常"}, {"position": 5, "aweme_id": "7533854356933987610", "title": "老实的摆拍，幅度大点就被限😅#小肚腩 #寸头 #没有腹肌 #健身 #壮熊"}, {"position": 6, "aweme_id": "7531707485943041290", "title": "下雨加周末，双重buff.还是在家睡觉吧#健身 #有肉感的胖子 #减脂 #自拍"}, {"position": 7, "aweme_id": "7531332406223424777", "title": "理了个发#小肚腩 #壮熊 #健身 #毫无训练痕迹"}, {"position": 8, "aweme_id": "7530906301402287406", "title": "来跟个风，ai配上不同的西装了😁 #型男"}, {"position": 9, "aweme_id": "7529852709017767218", "title": "偷懒一天，躺了一天，哈哈#无条件爱自己 #童心未泯的大朋友 #有肉感的胖子"}, {"position": 10, "aweme_id": "7529161519058914569", "title": "违规一个，补上一个😜#健身打卡"}], "count_diff": -19}}, {"author_name": "洗洁精过敏🫙", "sec_uid": "MS4wLjABAAAA0Kqx6WzHqB-9LFL-VGj992Z4IckB8HDPm5U3WwS2s4s", "original_api": {"works_count": 40, "top10_ids": ["7540600850420583720", "7539449835309894927", "7535432379125468450", "7524200067512175912", "7523411025497345320", "7517678546417241378", "7516568345124146466", "7513223099912162594", "7506825853679947042", "7497970423620799744"]}, "tikhub_api": {"works_count": 20, "top10_ids": ["7543617608979336483", "7542863390441917696", "7540600850420583720", "7539449835309894927", "7535432379125468450", "7524200067512175912", "7523411025497345320", "7517678546417241378", "7516568345124146466", "7513223099912162594"]}, "differences": {"missing_in_original": [{"position": 1, "aweme_id": "7543617608979336483", "title": "🙂‍↕️"}, {"position": 2, "aweme_id": "7542863390441917696", "title": "🙂‍↔️ #脂包肌 #反差"}], "missing_in_tikhub": [{"position": 9, "aweme_id": "7506825853679947042", "title": "🤪 #单眼皮 #憨憨男友"}, {"position": 10, "aweme_id": "7497970423620799744", "title": "面包吃吗 #反差 #单眼皮"}], "count_diff": -20}}, {"author_name": "韩煜麒Kylin🇨🇳", "sec_uid": "MS4wLjABAAAAyZQQuKilAZlwskAeKsoQGEoYNenxy1OSwJP1-4NmWHqh4_akB2-Iwb_VhiFXIV-i", "original_api": {"works_count": 1, "top10_ids": ["7533271198395272484"]}, "tikhub_api": {"works_count": 4, "top10_ids": ["7543457179195870506", "7543104584585547047", "7542817657017011495", "7533271198395272484"]}, "differences": {"missing_in_original": [{"position": 1, "aweme_id": "7543457179195870506", "title": "各位早，两周减脂期结束🔚每天叫醒我的不是闹钟而且自律，开启新阶段训练计划🫡你好新世界❤️❤️\n#健身 #肌肉 #减脂 #荷尔蒙 #逆袭之路"}, {"position": 2, "aweme_id": "7543104584585547047", "title": "按照要求没打电话没敲门，直接放到了门口，就不用投诉了吧\n#京东 #京东快递 #京东快递小哥 #京东物流 #京东服务"}, {"position": 3, "aweme_id": "7542817657017011495", "title": "路程虽远但一定必达！还请您放心…\n#京东快递 #京东快递小哥 #京东 #土直男 #寸头"}], "missing_in_tikhub": [], "count_diff": 3}}, {"author_name": "怕死的肉盾", "sec_uid": "MS4wLjABAAAA8fr-q1GHT7_jskZel0jU1-GezPTV1uGcdR6YH6H7H3IPR1xL_9didFfGNpvcaW4p", "original_api": {"works_count": 41, "top10_ids": ["7433421269233716518", "7500379777073696038", "7495922913980550450", "7540687349937950003", "7540301385935129907", "7539192867652193545", "7538848206248643850", "7538096************", "7536614915240070409", "7535115574016429363"]}, "tikhub_api": {"works_count": 23, "top10_ids": ["7433421269233716518", "7500379777073696038", "7495922913980550450", "7543661524650347822", "7543245349550460187", "7542899949521456411", "7542168646593383718", "7542156302706412810", "7541790813745007918", "7540687349937950003"]}, "differences": {"missing_in_original": [{"position": 4, "aweme_id": "7543661524650347822", "title": "#圆寸 为什么最迷人的最危险，为什么爱会让人变残缺，"}, {"position": 5, "aweme_id": "7543245349550460187", "title": "#抖音ai创作 今日份敷衍"}, {"position": 6, "aweme_id": "7542899949521456411", "title": "#躺在床上拍抖音 哎呀哎呀能量不足，自我充电模式开启"}, {"position": 7, "aweme_id": "7542168646593383718", "title": "不是我过不去，只是只为受委屈的从来不是你"}, {"position": 8, "aweme_id": "7542156302706412810", "title": "#真实的样子"}, {"position": 9, "aweme_id": "7541790813745007918", "title": "#圆寸 #精神内耗严重怎么调理 #小叔叔 #络腮胡 晚安💤听说正缘强的人需要一直等，不知道是不是真的"}], "missing_in_tikhub": [{"position": 5, "aweme_id": "7540301385935129907", "title": "日常敷衍"}, {"position": 6, "aweme_id": "7539192867652193545", "title": "#那些不明白就不要明白 #圆寸 #小叔叔 #络腮胡 #胖熊 有些人笑着笑着就哭了，有些人爱着爱着就散了"}, {"position": 7, "aweme_id": "7538848206248643850", "title": "#小叔叔 #叔圈 #你的男朋友 现在这个年纪再说想认真的谈恋爱会不会被笑话"}, {"position": 8, "aweme_id": "7538096************", "title": "#小叔叔 #寸头 #络腮胡 #圆寸 #大叔 我有一个朋友，在一片荒漠中迷失了方向"}, {"position": 9, "aweme_id": "7536614915240070409", "title": "#络腮胡 #胖熊 #宠物店 #小叔叔 #你的男朋友 最终过成了最怕的生活"}, {"position": 10, "aweme_id": "7535115574016429363", "title": "找对象啊，怎么可能不找，人生这路太坎坷了，我需要扶一下，不然我怕走不动"}], "count_diff": -18}}, {"author_name": "叫我大昀", "sec_uid": "MS4wLjABAAAA1zuIoTAPnZ9YjKeQKm36SMkdfykOdMSSeYc0i435oSo", "original_api": {"works_count": 40, "top10_ids": ["7540981767432752435", "7540238805611236654", "7539092093500788019", "7538814932777176347", "7536447318510767406", "7534278545239280934", "7533974373654285594", "7533454165046365467", "7532868443339492617", "7531333060308307209"]}, "tikhub_api": {"works_count": 20, "top10_ids": ["7540981767432752435", "7540238805611236654", "7539092093500788019", "7538814932777176347", "7536447318510767406", "7534278545239280934", "7533974373654285594", "7533454165046365467", "7532868443339492617", "7531333060308307209"]}, "differences": {"missing_in_original": [], "missing_in_tikhub": [], "count_diff": -20}}, {"author_name": "Everyday One Cat", "sec_uid": "MS4wLjABAAAAbQtEMZwP8QQffGonncs5fLXtGE2JhZVzOaSiuneBECE", "original_api": {"works_count": 40, "top10_ids": ["7539137925640916224", "7517908825999936802", "7510539404969102607", "7499794222870957362", "7489759358029516044", "7471466582863105318", "7469738659642641723", "7432243834697895205", "7394022807706799375", "7385841142752906511"]}, "tikhub_api": {"works_count": 20, "top10_ids": ["7539137925640916224", "7517908825999936802", "7510539404969102607", "7499794222870957362", "7489759358029516044", "7471466582863105318", "7469738659642641723", "7432243834697895205", "7394022807706799375", "7385841142752906511"]}, "differences": {"missing_in_original": [], "missing_in_tikhub": [], "count_diff": -20}}, {"author_name": "十五叔叔", "sec_uid": "MS4wLjABAAAAa04qwaySkjP-Dlus2myz5-7CO_Y43YS9_xjh0V-Y-lJDo-9VWWEh4wo8KCoY8PYO", "original_api": {"works_count": 18, "top10_ids": ["7540641666903887163", "7539171746549828921", "7536568388430630204", "7532105434531761468", "7530637246472621369", "7529509555055791417", "7526906165152206139", "7521371937635355962", "7518770347022568761", "7515813176567811386"]}, "tikhub_api": {"works_count": 18, "top10_ids": ["7540641666903887163", "7539171746549828921", "7536568388430630204", "7532105434531761468", "7530637246472621369", "7529509555055791417", "7526906165152206139", "7521371937635355962", "7518770347022568761", "7515813176567811386"]}, "differences": {"missing_in_original": [], "missing_in_tikhub": [], "count_diff": 0}}, {"author_name": "遇见金鸡湖", "sec_uid": "MS4wLjABAAAAxPsv7x7kCIEU-cgtDhJA7zWO-LgnC8tksmqv0M2QFgI", "original_api": {"works_count": 40, "top10_ids": ["7540911626335440187", "7539180699741195579", "7538403875708652859", "7536511531589602618", "7535780446866853179", "7533988478751132988", "7533810898965712185", "7531927885504220451", "7531031859971853620", "7530611023893581056"]}, "tikhub_api": {"works_count": 20, "top10_ids": ["7543539349788626235", "7540911626335440187", "7539180699741195579", "7538403875708652859", "7536511531589602618", "7535780446866853179", "7533988478751132988", "7533810898965712185", "7531927885504220451", "7531031859971853620"]}, "differences": {"missing_in_original": [{"position": 1, "aweme_id": "7543539349788626235", "title": "📍苏州金鸡湖|环湖各大音乐类活动合集。🎵\n金鸡湖作为苏州的时尚地标，\n不仅有波澜壮阔的环湖美景🎤还汇聚了丰富多彩小的音乐活动#苏州 #金鸡湖音乐#苏州游玩推荐 #金鸡湖#遇见金鸡湖"}], "missing_in_tikhub": [{"position": 10, "aweme_id": "7530611023893581056", "title": "“写意心象--任艳明雕塑艺术展”开幕啦～欢迎来李公堤文创街区青年雕塑家交流中心看展！#苏州 #雕塑 #遇见金鸡湖"}], "count_diff": -20}}, {"author_name": "发现宿迁", "sec_uid": "MS4wLjABAAAAxp0pG-bNIuY4UYOzWvhy4ZAjvzuIQhMg6XBTRg-m0sD-qPOKJBRn2jkmadWGsfW6", "original_api": {"works_count": 43, "top10_ids": ["7502355385245273384", "7504141946715000123", "7499058900857507091", "7540320265541061945", "7539186935940549947", "7539160405642759483", "7539020842283207993", "7535104085025623355", "7534713549050940729", "7534691424513068346"]}, "tikhub_api": {"works_count": 23, "top10_ids": ["7502355385245273384", "7504141946715000123", "7499058900857507091", "7543280899509456188", "7540320265541061945", "7539186935940549947", "7539160405642759483", "7539020842283207993", "7535104085025623355", "7534713549050940729"]}, "differences": {"missing_in_original": [{"position": 4, "aweme_id": "7543280899509456188", "title": "推广宿迁抖音挑战赛——发现宿城美颁奖典礼暨七夕文艺活动#记录精彩瞬间#发现宿城美#看苏超游宿迁#宿迁城市印记#快来围观@宿城融媒@宿迁零距离@宿迁文旅（畅游宿迁） @唐光灿美食作者 @走进宿迁 @吃遍宿迁"}], "missing_in_tikhub": [{"position": 10, "aweme_id": "7534691424513068346", "title": "今日我虽输，我还是西楚霸王，斗志仍燃宿迁再战#宿迁队加油#苏超联赛#宿迁城市印记  #上场就要全力以赴 #看苏超游宿迁"}], "count_diff": -20}}, {"author_name": "何以解忧唯有杜康……", "sec_uid": "MS4wLjABAAAAeYcRBpPUtzJw7du-GcUkEpipiNr0lVCxP7UYWFoWvVX_-3iGU2YgL22PVxWNqbVR", "original_api": {"works_count": 40, "top10_ids": ["7541207575736618303", "7540835840177818921", "7540479929759501610", "7540478925735464211", "7540139228291960127", "7540138685586885929", "7540104386979515684", "7539869646511394087", "7539869257808432438", "7539815201740229907"]}, "tikhub_api": {"works_count": 20, "top10_ids": ["7543682751720131894", "7543682212777725220", "7543632929348603175", "7543600644441312548", "7543463961556290852", "7543308560332442899", "7543306721012616467", "7542931511125921087", "7542930269679832359", "7542713025263422774"]}, "differences": {"missing_in_original": [{"position": 1, "aweme_id": "7543682751720131894", "title": "#逆袭"}, {"position": 2, "aweme_id": "7543682212777725220", "title": "哎呀这肚子无敌了#小肚腩"}, {"position": 3, "aweme_id": "7543632929348603175", "title": "广东是没有爱情的只有吃不完的炒米粉"}, {"position": 4, "aweme_id": "7543600644441312548", "title": "#一秒沦陷 可以不帅但不能不骚"}, {"position": 5, "aweme_id": "7543463961556290852", "title": "#致亲爱的孤独者 年纪大了真上不了夜班一天只能睡两三小时还做梦#小叔叔"}, {"position": 6, "aweme_id": "7543308560332442899", "title": "#回忆 #有点上头 你在时一人一箱不够喝，你不在时我一瓶喝不下#这是怎么了"}, {"position": 7, "aweme_id": "7543306721012616467", "title": "其实我真的很能喝就算你能喝一箱在我这里也是小弟#有点上头 喝到眼神迷离#痛并快乐着"}, {"position": 8, "aweme_id": "7542931511125921087", "title": "如果你恋爱了那么你必须承受它带来的喜怒哀乐和酸甜苦辣#酸甜苦辣喜怒哀乐"}, {"position": 9, "aweme_id": "7542930269679832359", "title": "#何以解忧 何以解忧唯有杜康"}, {"position": 10, "aweme_id": "7542713025263422774", "title": "#恰似春风知晚意"}], "missing_in_tikhub": [{"position": 1, "aweme_id": "7541207575736618303", "title": "大清早的都被盘包浆了给我吃#盘它"}, {"position": 2, "aweme_id": "7540835840177818921", "title": "只要能吃苦就会🈶️吃不完的苦#只要能吃苦就有吃不完的苦"}, {"position": 3, "aweme_id": "7540479929759501610", "title": "我坦白了就算跟光头在一起喝酒吃剩饭剩菜也是开心的这下你们满意了吧#回忆 #回忆经典"}, {"position": 4, "aweme_id": "7540478925735464211", "title": "宝贝们吃香蕉了啦啦啦啦好好吃好美味啦啦啦啦啦#香蕉"}, {"position": 5, "aweme_id": "7540139228291960127", "title": "来来来谁要看酒搭子的#在雨中 #酒搭子"}, {"position": 6, "aweme_id": "7540138685586885929", "title": "不要问最近过得好不好了就这样的日子挺好的了说实话好累真的好累不想努力了#不想努力了 #记录生活"}, {"position": 7, "aweme_id": "7540104386979515684", "title": "#酒搭子 #回亿 就连喝酒也只能回忆了"}, {"position": 8, "aweme_id": "7539869646511394087", "title": "#抖音ai创作 #这也太好看了"}, {"position": 9, "aweme_id": "7539869257808432438", "title": "#抖音ai创作 #万能的抖音 #这也太好看了 这个可以"}, {"position": 10, "aweme_id": "7539815201740229907", "title": "#结果出乎意料 #我服了 大家说一下这人说的是真的吗？我不想努力了，想靠脸吃饭的我差点没饿死#总感觉哪里不对"}], "count_diff": -20}}, {"author_name": "塞尔达秋红叶", "sec_uid": "MS4wLjABAAAAbPj8ftJJq_9ONp74ENm_DEjCAl7o_WIu1tEiJJqaXxd8pnOifbtv9BLWl8G9ZhzX", "original_api": {"works_count": 40, "top10_ids": ["7522420224072240430", "7522282429219065114", "7522284210712300851", "7540510775807560986", "7539753012227607862", "7539604468170771758", "7538857664581061938", "7538707840295947530", "7537281234889870642", "7531599993440177458"]}, "tikhub_api": {"works_count": 23, "top10_ids": ["7522420224072240430", "7522282429219065114", "7522284210712300851", "7543584577504742706", "7543291588248685851", "7540510775807560986", "7539753012227607862", "7539604468170771758", "7538857664581061938", "7538707840295947530"]}, "differences": {"missing_in_original": [{"position": 4, "aweme_id": "7543584577504742706", "title": "NS2旷野之息全神庙DLC速通，新的纪录。还能在快，冲！！！#速通 #塞尔达旷野之息"}, {"position": 5, "aweme_id": "7543291588248685851", "title": "沉默援手稳定解法#塞尔达旷野之息"}], "missing_in_tikhub": [{"position": 9, "aweme_id": "7537281234889870642", "title": "NS2高画质版100%ex纪录，过审了。#速通#塞尔达旷野之息"}, {"position": 10, "aweme_id": "7531599993440177458", "title": "割稻谷#稻谷熟了"}], "count_diff": -17}}, {"author_name": "王小远（外卖小哥）", "sec_uid": "MS4wLjABAAAAtMIKh6ar69--8X4MgjAzP3xtrBW0It7RvB9WOTaPx6PgpV94DfUtjzgfZ3Ss_DMw", "original_api": {"works_count": 41, "top10_ids": ["7529520140113071402", "7526541228839570727", "7519961370034113846", "7541361417288994089", "7540913717283523879", "7540655909652532516", "7539890509806112043", "7539401871439269183", "7539122563109391659", "7538873922826538282"]}, "tikhub_api": {"works_count": 22, "top10_ids": ["7529520140113071402", "7526541228839570727", "7519961370034113846", "7543574844194442559", "7543203297038011687", "7542339644241104147", "7541747420405861673", "7541361417288994089", "7540913717283523879", "7540655909652532516"]}, "differences": {"missing_in_original": [{"position": 4, "aweme_id": "7543574844194442559", "title": "你们拍拍屁股走啦，留下烂摊子给我们处理，像话嘛#外卖小哥 #外卖员"}, {"position": 5, "aweme_id": "7543203297038011687", "title": "我想跟你们说，姊妹们，我也是个要脸面的人啊，别整我啦#外卖小哥#外卖员 #万万没想到"}, {"position": 6, "aweme_id": "7542339644241104147", "title": "依旧体验送外卖哦，不过我感觉我我有点亏啦#vlog日常 #外卖员 #原创视频 #外卖小哥"}, {"position": 7, "aweme_id": "7541747420405861673", "title": "兄弟们，这种局该如何应对啊#外卖员 #外卖小哥 #送外卖"}], "missing_in_tikhub": [{"position": 7, "aweme_id": "7539890509806112043", "title": "专业外卖电动车黑骑士e10#黑骑士e10 #黑骑士更专业的外卖车 #送外卖"}, {"position": 8, "aweme_id": "7539401871439269183", "title": "晚上这些人类可真奇怪，总是爱生病😷，#外卖小哥 #送外卖"}, {"position": 9, "aweme_id": "7539122563109391659", "title": "换电品牌，我选择智租换电，十秒就能换好，超级省时间#智租换电 #外卖小哥 #送外卖"}, {"position": 10, "aweme_id": "7538873922826538282", "title": "家里就三间瓦房，不拼咋弄啊，我也想下班回家躺在床玩手机刷视频，但是我不敢停下赚钱的脚步啊，我没得选，我也不想输，始终相信自己会有出头那天#外卖小哥 #送外卖"}], "count_diff": -19}}, {"author_name": "武林外传", "sec_uid": "MS4wLjABAAAAnImAVMMYGMHaDFcZAdgiTgg-QrhNh-PT5BSle9CXpE0vXcJGuiQlwJjVfvwp_z_T", "original_api": {"works_count": 40, "top10_ids": ["7539891135817305378", "7538749652386336040", "7538400645176347956", "7537517312129617192", "7534680980343262498", "7534351007356210484", "7533251237966286114", "7528419031235267875", "7528360024134520115", "7527229989016882447"]}, "tikhub_api": {"works_count": 20, "top10_ids": ["7542140660179029248", "7539891135817305378", "7538749652386336040", "7538400645176347956", "7537517312129617192", "7534680980343262498", "7534351007356210484", "7533251237966286114", "7528419031235267875", "7528360024134520115"]}, "differences": {"missing_in_original": [{"position": 1, "aweme_id": "7542140660179029248", "title": "武林外传高清剧照（图文版）。#武林外传"}], "missing_in_tikhub": [{"position": 10, "aweme_id": "7527229989016882447", "title": "重新制作了打地铺冰箱贴的小视频~ 南方潮湿气候地区的腐竹慎购木质冰箱贴#武林外传 #冰箱贴 #ar视频"}], "count_diff": -20}}, {"author_name": "会摔跤的学弟", "sec_uid": "MS4wLjABAAAAoSc3zEmMp2vwE8Bvmdy4uD9bsGQ3JAP5L8VjYp0T0WYvadK4gT-bs6siIa7FnRqE", "original_api": {"works_count": 40, "top10_ids": ["7537998292580338980", "7537998148132900132", "7537998039412264231", "7537997930842705215", "7537997787330350372", "7537997624473816374", "7537997478851824935", "7537997355316989220", "7537997236769213750", "7537997135279557931"]}, "tikhub_api": {"works_count": 20, "top10_ids": ["7542300885439827215", "7542300698793250083", "7542300650017656104", "7542300450352106786", "7542300275772542242", "7542300251042942243", "7542300078376013108", "7542300025829739828", "7542299929725635855", "7542299763375492404"]}, "differences": {"missing_in_original": [{"position": 1, "aweme_id": "7542300885439827215", "title": "2025年全国U20国际式摔跤锦标赛 #体育生#运动员#自由跤#古典跤#大学生"}, {"position": 2, "aweme_id": "7542300698793250083", "title": "2025年全国U20国际式摔跤锦标赛 #体育生#运动员#自由跤#古典跤#大学生"}, {"position": 3, "aweme_id": "7542300650017656104", "title": "2025年全国U20国际式摔跤锦标赛 #体育生#运动员#自由跤#古典跤#大学生"}, {"position": 4, "aweme_id": "7542300450352106786", "title": "2025年全国U20国际式摔跤锦标赛 #体育生#运动员#自由跤#古典跤#大学生"}, {"position": 5, "aweme_id": "7542300275772542242", "title": "2025年全国U20国际式摔跤锦标赛 #体育生#运动员#自由跤#古典跤#大学生"}, {"position": 6, "aweme_id": "7542300251042942243", "title": "2025年全国U20国际式摔跤锦标赛 #体育生#运动员#自由跤#古典跤#大学生"}, {"position": 7, "aweme_id": "7542300078376013108", "title": "2025年全国U20国际式摔跤锦标赛 #体育生#运动员#自由跤#古典跤#大学生"}, {"position": 8, "aweme_id": "7542300025829739828", "title": "2025年全国U20国际式摔跤锦标赛 #体育生#运动员#自由跤#古典跤#大学生"}, {"position": 9, "aweme_id": "7542299929725635855", "title": "2025年全国U20国际式摔跤锦标赛 #体育生#运动员#自由跤#古典跤#大学生"}, {"position": 10, "aweme_id": "7542299763375492404", "title": "2025年全国U20国际式摔跤锦标赛 #体育生#运动员#自由跤#古典跤#大学生"}], "missing_in_tikhub": [{"position": 1, "aweme_id": "7537998292580338980", "title": "2025年全国U20国际式摔跤锦标赛 #体育生#运动员#自由跤#古典跤#大学生"}, {"position": 2, "aweme_id": "7537998148132900132", "title": "2025年全国U20国际式摔跤锦标赛 #体育生#运动员#自由跤#古典跤#大学生"}, {"position": 3, "aweme_id": "7537998039412264231", "title": "2025年全国U20国际式摔跤锦标赛 #体育生#运动员#自由跤#古典跤#大学生"}, {"position": 4, "aweme_id": "7537997930842705215", "title": "2025年全国U20国际式摔跤锦标赛 #体育生#运动员#自由跤#古典跤#大学生"}, {"position": 5, "aweme_id": "7537997787330350372", "title": "2025年全国U20国际式摔跤锦标赛 #体育生#运动员#自由跤#古典跤#大学生"}, {"position": 6, "aweme_id": "7537997624473816374", "title": "2025年全国U20国际式摔跤锦标赛 #体育生#运动员#自由跤#古典跤#大学生"}, {"position": 7, "aweme_id": "7537997478851824935", "title": "2025年全国U20国际式摔跤锦标赛 #体育生#运动员#自由跤#古典跤#大学生"}, {"position": 8, "aweme_id": "7537997355316989220", "title": "2025年全国U20国际式摔跤锦标赛 #体育生#运动员#自由跤#古典跤#大学生"}, {"position": 9, "aweme_id": "7537997236769213750", "title": "2025年全国U20国际式摔跤锦标赛 #体育生#运动员#自由跤#古典跤#大学生"}, {"position": 10, "aweme_id": "7537997135279557931", "title": "2025年全国U20国际式摔跤锦标赛 #体育生#运动员#自由跤#古典跤#大学生"}], "count_diff": -20}}, {"author_name": "铜山区《尝香聚》", "sec_uid": "MS4wLjABAAAAHZdGfbkNoKBi3wRohzwLKon1QBwhdRA9PTj7xlb2wOA", "original_api": {"works_count": 42, "top10_ids": ["7483052793092148490", "7096255921915055373", "7098283945095220494", "7536245918242540839", "7524980973315673383", "7524179857954327862", "7522514321621912871", "7521999654520507684", "7518979344782494988", "7517190940294434060"]}, "tikhub_api": {"works_count": 23, "top10_ids": ["7483052793092148490", "7096255921915055373", "7098283945095220494", "7536245918242540839", "7524980973315673383", "7524179857954327862", "7522514321621912871", "7521999654520507684", "7518979344782494988", "7517190940294434060"]}, "differences": {"missing_in_original": [], "missing_in_tikhub": [], "count_diff": -19}}, {"author_name": "侃老板", "sec_uid": "MS4wLjABAAAA6f_sgEJNAMnJ_DnWYh2YihYBiZ_8McuLsF6GIl8_zUs", "original_api": {"works_count": 40, "top10_ids": ["7502778698799205690", "7541338995651071291", "7539909474896809274", "7537104415292132666", "7535639424793742652", "7533213476531146042", "7532196558672252219", "7528025907413699899", "7525820461256691003", "7525439979536289081"]}, "tikhub_api": {"works_count": 21, "top10_ids": ["7502778698799205690", "7541338995651071291", "7539909474896809274", "7537104415292132666", "7535639424793742652", "7533213476531146042", "7532196558672252219", "7528025907413699899", "7525820461256691003", "7525439979536289081"]}, "differences": {"missing_in_original": [], "missing_in_tikhub": [], "count_diff": -19}}, {"author_name": "浮木", "sec_uid": "MS4wLjABAAAAoN0GM96gaMkf3SzU9G2IbjK7hcZlzIuEYEK5NoP2FkgBv6LGq_Tb3kpls4a9bHsA", "original_api": {"works_count": 13, "top10_ids": ["7532840982196686121", "7501733879809576219", "7473536507417021746", "7452236165182278939", "7451584441291443506", "7451497166339591434", "7449633413545397531", "7448851774800219419", "7448499109603904778", "7448496565930528010"]}, "tikhub_api": {"works_count": 13, "top10_ids": ["7532840982196686121", "7501733879809576219", "7473536507417021746", "7452236165182278939", "7451584441291443506", "7451497166339591434", "7449633413545397531", "7448851774800219419", "7448499109603904778", "7448496565930528010"]}, "differences": {"missing_in_original": [], "missing_in_tikhub": [], "count_diff": 0}}, {"author_name": "付憨憨呀", "sec_uid": "MS4wLjABAAAAJCy2s7kbg7iuSUuNbasLlX11iduPUNGACrfZwgkVMhI", "original_api": {"works_count": 41, "top10_ids": ["7501604472067984655", "6775129177793318155", "7537736774522572084", "7524204854220721460", "7514348756876299560", "7504933064997653800", "7503764808739769635", "7503075220292226319", "7502291559241747747", "7501707030718926080"]}, "tikhub_api": {"works_count": 21, "top10_ids": ["7501604472067984655", "6775129177793318155", "7537736774522572084", "7524204854220721460", "7514348756876299560", "7504933064997653800", "7503764808739769635", "7503075220292226319", "7502291559241747747", "7501707030718926080"]}, "differences": {"missing_in_original": [], "missing_in_tikhub": [], "count_diff": -20}}, {"author_name": "你还是回地球吧", "sec_uid": "MS4wLjABAAAAe1SxkkKkqwiUmouGiGXow6hsFuF_xDiy873V78a1fGCExUqiU2yh0tbyA-Inuyy2", "original_api": {"works_count": 41, "top10_ids": ["7454498429067218226", "7541059797123976467", "7540307595388783914", "7539108756802915623", "7538449103924088100", "7537684620152114471", "7536952501826030891", "7535730077171584275", "7535089205928578347", "7534354348073258279"]}, "tikhub_api": {"works_count": 21, "top10_ids": ["7454498429067218226", "7542868073410841875", "7542124516814703908", "7541771562446884137", "7541059797123976467", "7540307595388783914", "7539108756802915623", "7538449103924088100", "7537684620152114471", "7536952501826030891"]}, "differences": {"missing_in_original": [{"position": 2, "aweme_id": "7542868073410841875", "title": ""}, {"position": 3, "aweme_id": "7542124516814703908", "title": ""}, {"position": 4, "aweme_id": "7541771562446884137", "title": ""}], "missing_in_tikhub": [{"position": 8, "aweme_id": "7535730077171584275", "title": ""}, {"position": 9, "aweme_id": "7535089205928578347", "title": ""}, {"position": 10, "aweme_id": "7534354348073258279", "title": "#真实还原 #内容太过真实"}], "count_diff": -20}}, {"author_name": "十五块乐冰", "sec_uid": "MS4wLjABAAAAI5StkcDsru99Vtk1PAOk6VQ2zCS99HtGXkBJmpH7kTw", "original_api": {"works_count": 21, "top10_ids": ["7463866770336566579", "7530278601193082151", "7525787240513965355", "7518763063311469878", "7514669088606768447", "7507189683526143244", "7495752674367163685", "7492395256873241866", "7488335665977953573", "7483138853340073267"]}, "tikhub_api": {"works_count": 20, "top10_ids": ["7463866770336566579", "7530278601193082151", "7525787240513965355", "7518763063311469878", "7514669088606768447", "7507189683526143244", "7495752674367163685", "7492395256873241866", "7488335665977953573", "7483138853340073267"]}, "differences": {"missing_in_original": [], "missing_in_tikhub": [], "count_diff": -1}}, {"author_name": "保护小小灬志", "sec_uid": "MS4wLjABAAAAY9VYrIgjyh8JTbiRzC2qxude0hcmlq5Rq25cO2E4cIY", "original_api": {"works_count": 33, "top10_ids": ["7534646449952820543", "7090777747202575656", "7199691004587330851", "7541109057717914916", "7540905446238244159", "7540484381329149225", "7539518661809720639", "7538722214016093459", "7537965514044263723", "7537723211821141291"]}, "tikhub_api": {"works_count": 22, "top10_ids": ["7534646449952820543", "7090777747202575656", "7199691004587330851", "7543693858267565375", "7543325878350105899", "7542864356795518244", "7542690434259766571", "7541756047565851946", "7541109057717914916", "7540905446238244159"]}, "differences": {"missing_in_original": [{"position": 4, "aweme_id": "7543693858267565375", "title": "七夕快乐 晚安😂"}, {"position": 5, "aweme_id": "7543325878350105899", "title": "青春属于表白，阳光属于窗台，\n而我想我属于一个拥有你的未来。\n#圆寸 #小叔叔#胖熊@抖音创作小助手 @抖音小助手"}, {"position": 6, "aweme_id": "7542864356795518244", "title": "打两把不就有手感了嘛，没有人欠我钱我就是这个表情🤦，上号吧……#大肚子 #沧桑大叔 #圆寸#胖大叔@抖音创作小助手 @抖音小助手 #腿毛太长"}, {"position": 7, "aweme_id": "7542690434259766571", "title": "身材不错为啥袜子这么脏啊。#抖音ai创作"}, {"position": 8, "aweme_id": "7541756047565851946", "title": "搞笑男克服社恐的第一百零一次#圆寸#卡点舞 #小叔叔🐻@抖音创作小助手 @抖音小助手"}], "missing_in_tikhub": [{"position": 6, "aweme_id": "7540484381329149225", "title": "早上起来去洗漱洗手间有一个隔壁刚搬来的女士占着，我有点想上大号就在门口等着了，等了一会她出来了，看见我在门口吓得她捂住脸赶紧跑回卧室了，不是大姐你有必要嘛，上次在厨房也是，我一路过赶紧把门关上。别搞笑了好吧，我还没有那么饥渴，对你也不敢兴趣，以为自己是女王嘛？年纪感觉比我大身材都跟我差不多了，感觉每时每刻都在洗手间，你怎么不直接住里面，真是让人无语😓#圆寸 #轻熟穿搭"}, {"position": 7, "aweme_id": "7539518661809720639", "title": "你是冰箱我是雪糕你越冻我越硬#圆寸男孩 #粗腿穿搭 #男生穿搭 #大肚子@抖音创作小助手"}, {"position": 8, "aweme_id": "7538722214016093459", "title": "Day0精神状态都好了#圆寸 #翻唱@抖音创作小助手 @抖音小助手"}, {"position": 9, "aweme_id": "7537965514044263723", "title": "营业时间到了，最近有点频繁啊#正装 #圆寸 #憨憨男孩 #history @抖音创作小助手 @抖音小助手#exo"}, {"position": 10, "aweme_id": "7537723211821141291", "title": "脸好像被谁打了，不管了睡觉吧#圆寸 #小叔叔 #手势舞 @抖音创作小助手 @抖音小助手"}], "count_diff": -11}}, {"author_name": "性感大野狼", "sec_uid": "MS4wLjABAAAAkhFjOGHfLV1J2wh1lkmAEaIXJY6Aa0NEEuj0JI6XSz4", "original_api": {"works_count": 31, "top10_ids": ["7540861563482279227", "7535355238354963771", "7528224706085834043", "7525664944836267323", "7524885055668817212", "7516233955444624651", "7500045399353216293", "7488152431327513894", "7487963542285503794", "7487053701652253962"]}, "tikhub_api": {"works_count": 16, "top10_ids": ["7540861563482279227", "7535355238354963771", "7528224706085834043", "7525664944836267323", "7524885055668817212", "7516233955444624651", "7500045399353216293", "7488152431327513894", "7487963542285503794", "7487053701652253962"]}, "differences": {"missing_in_original": [], "missing_in_tikhub": [], "count_diff": -15}}, {"author_name": "山🦷", "sec_uid": "MS4wLjABAAAAWOBeTZxe3aJnC8Nax-8ZkTjnLc55DMSXxtH4yEendECuWAxl12wutgK5SLvRSdsR", "original_api": {"works_count": 41, "top10_ids": ["7514156967575194940", "7530887486617865532", "7490520479829200186", "7535539604607552825", "7528781256609795388", "7526197421200100667", "7525413733590551866", "7523929581557419324", "7523136167396396346", "7522460891709230394"]}, "tikhub_api": {"works_count": 22, "top10_ids": ["7514156967575194940", "7530887486617865532", "7490520479829200186", "7543223937485294905", "7535539604607552825", "7528781256609795388", "7526197421200100667", "7525413733590551866", "7523929581557419324", "7523136167396396346"]}, "differences": {"missing_in_original": [{"position": 4, "aweme_id": "7543223937485294905", "title": "过去的一个月我没有放弃，180斤👉174斤。#减肥 #健身#蜕变过程"}], "missing_in_tikhub": [{"position": 10, "aweme_id": "7522460891709230394", "title": "减肥番外篇之引体向上#户外运动 #运动健身 #锻炼 #夏天到了"}], "count_diff": -19}}, {"author_name": "刀刀", "sec_uid": "MS4wLjABAAAA6lL3CHy-DmZPymIZfVCIih6CPciMkXqDZz9_HbTNNok", "original_api": {"works_count": 40, "top10_ids": ["7540999211245931833", "7540250491382041916", "7539519174533074235", "7538736642249739578", "7536803376953347385", "7536141948277771578", "7535404418473692474", "7534674842948914491", "7533842126497942844", "7533186262896692540"]}, "tikhub_api": {"works_count": 20, "top10_ids": ["7542349167264730428", "7541739160321084731", "7540999211245931833", "7540250491382041916", "7539519174533074235", "7538736642249739578", "7536803376953347385", "7536141948277771578", "7535404418473692474", "7534674842948914491"]}, "differences": {"missing_in_original": [{"position": 1, "aweme_id": "7542349167264730428", "title": "回复 @石头🌈的评论 擦了你又不关注"}, {"position": 2, "aweme_id": "7541739160321084731", "title": "炫彩原皮 #今天又是勤劳的刀刀"}], "missing_in_tikhub": [{"position": 9, "aweme_id": "7533842126497942844", "title": "相机喜欢镜头前的每一个故事。#今天又是勤劳的刀刀"}, {"position": 10, "aweme_id": "7533186262896692540", "title": "看惯了侧脸 以后看照片就不奇怪了 #今天又是勤劳的刀刀"}], "count_diff": -20}}, {"author_name": ":三轮车", "sec_uid": "MS4wLjABAAAAkH_OcNw-t5mcZUJZdsO6L-Q_Q_Z2RqSBfhr0hV0TH-16YcnQURxJfTmJ5UrR1PXD", "original_api": {"works_count": 40, "top10_ids": ["7541410226701258041", "7541346349179358521", "7541267145498299708", "7541261432228629817", "7541030673085648186", "7541006363654212922", "7540904593519283515", "7540637982816341308", "7540632771665464634", "7540522582295973179"]}, "tikhub_api": {"works_count": 20, "top10_ids": ["7543573294534118715", "7543549604329426235", "7543544095042538809", "7543512645078732090", "7543508407204433210", "7543497119224237372", "7543481765777689914", "7543459687384419643", "7543258237543091516", "7543210005216415036"]}, "differences": {"missing_in_original": [{"position": 1, "aweme_id": "7543573294534118715", "title": "真的好难过"}, {"position": 2, "aweme_id": "7543549604329426235", "title": "啥事没有"}, {"position": 3, "aweme_id": "7543544095042538809", "title": "不过是嘴角微脏"}, {"position": 4, "aweme_id": "7543512645078732090", "title": "兄弟们我第一波我汉就透了"}, {"position": 5, "aweme_id": "7543508407204433210", "title": "出招吧我看看怎么个事"}, {"position": 6, "aweme_id": "7543497119224237372", "title": "有点放不开兄弟们，还是比较腼腆"}, {"position": 7, "aweme_id": "7543481765777689914", "title": "兄弟们一定要保护好自己的牙"}, {"position": 8, "aweme_id": "7543459687384419643", "title": "这边面条还行盖饭差点意思"}, {"position": 9, "aweme_id": "7543258237543091516", "title": "怎么回事到底是"}, {"position": 10, "aweme_id": "7543210005216415036", "title": "我是说一遍"}], "missing_in_tikhub": [{"position": 1, "aweme_id": "7541410226701258041", "title": "不是，暗示我呢让我走奥利给路线啊"}, {"position": 2, "aweme_id": "7541346349179358521", "title": "又沉浸在自己的nt神颜当中了#40岁男人"}, {"position": 3, "aweme_id": "7541267145498299708", "title": "2b青年欢乐多具象化这一块，尽管你们天天举报我骂我但是no careful，我该摇还得摇"}, {"position": 4, "aweme_id": "7541261432228629817", "title": "兄弟们我又含泪囤了十张券"}, {"position": 5, "aweme_id": "7541030673085648186", "title": "我这查尔斯顿舞跳出来的效果怎么不一样呢？哪里错了"}, {"position": 6, "aweme_id": "7541006363654212922", "title": "握不住的沙就要扬了它这一块/."}, {"position": 7, "aweme_id": "7540904593519283515", "title": "清真系饮食这一块/.#冰红茶 #走࿆好自࿆己࿆的࿆路࿆"}, {"position": 8, "aweme_id": "7540637982816341308", "title": "这一路风风雨雨总要自己闯一闯这一块/.#老兵退伍 #爱拼才会赢"}, {"position": 9, "aweme_id": "7540632771665464634", "title": "废了"}, {"position": 10, "aweme_id": "7540522582295973179", "title": "谁给我冰镇佳酿康师傅冰红茶偷了？"}], "count_diff": -20}}, {"author_name": "风风风清扬", "sec_uid": "MS4wLjABAAAApavFEWWHuBcDT3Nr4ozOV0FhmAMm77qLYgcE0fhLfQZWXOzuhaVCSGM5G5z6n1F0", "original_api": {"works_count": 12, "top10_ids": ["7541247797132021049", "7539215563802348838", "7540954425947737394", "7537708191238491451", "7534337216164842761", "7534010767872740635", "7533898072352492851", "7528881910581054780", "7524226559492263177", "7521600034035813642"]}, "tikhub_api": {"works_count": 12, "top10_ids": ["7541247797132021049", "7539215563802348838", "7540954425947737394", "7537708191238491451", "7534337216164842761", "7534010767872740635", "7533898072352492851", "7528881910581054780", "7524226559492263177", "7521600034035813642"]}, "differences": {"missing_in_original": [], "missing_in_tikhub": [], "count_diff": 0}}, {"author_name": "暴躁黄老板", "sec_uid": "MS4wLjABAAAAcSD3VmMQIV9TfQ6nHr8Pi_mQVW4nSfuvActjDyqLoWQ", "original_api": {"works_count": 43, "top10_ids": ["7322401280800869667", "7395795346695179554", "7283793215629610275", "7541253001369914660", "7540595601038216511", "7540247489274858793", "7539775914851241252", "7538753076346293555", "7539047441564323098", "7538661582562217225"]}, "tikhub_api": {"works_count": 23, "top10_ids": ["7322401280800869667", "7395795346695179554", "7283793215629610275", "7543484949450460470", "7543190041938447655", "7542890548479151398", "7542839377915841833", "7542516065977306402", "7541726820778003752", "7541403328559746358"]}, "differences": {"missing_in_original": [{"position": 4, "aweme_id": "7543484949450460470", "title": "会计和法人哪个更危险？#职场 #反转 #纯属虚构"}, {"position": 5, "aweme_id": "7543190041938447655", "title": "销冠这份工作还是太危险了...#反转 #搞笑#偃武  #偃武828上线  #梁朝伟都爱玩的三国"}, {"position": 6, "aweme_id": "7542890548479151398", "title": "《成都江湖传说》#成都 #搞笑 #反转  #李佳航代言  #热血江湖归来"}, {"position": 7, "aweme_id": "7542839377915841833", "title": "翻译的准确吗？#职场#搞笑  #桃花源记×大话西游联动 #回合制 #一生所爱"}, {"position": 8, "aweme_id": "7542516065977306402", "title": "你的体检报告也能去面试吗？#职场 #搞笑 #源星战域  #源星觉醒冲出宇宙  #源星宇宙话事人任达华"}, {"position": 9, "aweme_id": "7541726820778003752", "title": "老板良心最痛的一集#职场#搞笑 #反转 #人生苦短不如倒满 @舍得官方旗舰店"}, {"position": 10, "aweme_id": "7541403328559746358", "title": "总感觉哪里不对呢？#暑假工 #职场 #搞笑"}], "missing_in_tikhub": [{"position": 4, "aweme_id": "7541253001369914660", "title": "这些工作必须狠狠避雷！#就业 #找工作 #职场"}, {"position": 5, "aweme_id": "7540595601038216511", "title": "现在招个财会大学生都这么难了吗？#职场 #搞笑 #高顿ACCA #ACCA #大一财会萌新抢跑攻略"}, {"position": 6, "aweme_id": "7540247489274858793", "title": "（无恶意）你的表属于哪种？#搞笑 #手表 #内容过于真实"}, {"position": 7, "aweme_id": "7539775914851241252", "title": "太阳照常升起！社保没有新规！#社保 #职场 #一定要看到最后"}, {"position": 8, "aweme_id": "7538753076346293555", "title": "你们的头像属于哪种？#搞笑 #头像 #内容过于真实"}, {"position": 9, "aweme_id": "7539047441564323098", "title": "《当代年轻人现状》#职场  #社保  #内容过于真实"}, {"position": 10, "aweme_id": "7538661582562217225", "title": "你们的装修属于哪一类？#内容过于真实 #搞笑 #装修"}], "count_diff": -20}}, {"author_name": "睿10爱看球⚽️", "sec_uid": "MS4wLjABAAAAwYJ4jOvylu6sVzl381s8UApnpQiyECbu89Zlri8YO6w", "original_api": {"works_count": 40, "top10_ids": ["7533285067343498556", "7520377604277800252", "7519276039710625082", "7539856964503964987", "7539281983546707259", "7538936570461556025", "7538861049581636922", "7538817575301860665", "7537806471104187708", "7536210764991860027"]}, "tikhub_api": {"works_count": 22, "top10_ids": ["7533285067343498556", "7520377604277800252", "7519276039710625082", "7539856964503964987", "7539281983546707259", "7538936570461556025", "7538861049581636922", "7538817575301860665", "7537806471104187708", "7536210764991860027"]}, "differences": {"missing_in_original": [], "missing_in_tikhub": [], "count_diff": -18}}, {"author_name": "野猪🐗豪豪", "sec_uid": "MS4wLjABAAAAlZHoxPXyrm-kNsihzlPSI6mgUS_9HHIk7vA96FsRKEk", "original_api": {"works_count": 9, "top10_ids": ["7374265953137265956", "7405548327498042659", "7539188702712384820", "7538007079501221120", "7524279268689415439", "7515468423570312482", "7513794340625304832", "7505344798837902607", "7437754778232605964"]}, "tikhub_api": {"works_count": 11, "top10_ids": ["7374265953137265956", "7405548327498042659", "7541980072892271912", "7541752527021952308", "7539188702712384820", "7538007079501221120", "7524279268689415439", "7515468423570312482", "7513794340625304832", "7505344798837902607"]}, "differences": {"missing_in_original": [{"position": 3, "aweme_id": "7541980072892271912", "title": "hi.白袜焊脚上#体育生的腿 #篮球袜 #0"}, {"position": 4, "aweme_id": "7541752527021952308", "title": "hi 武汉。1️⃣1️⃣1️⃣#黑皮 #1 #脚"}], "missing_in_tikhub": [{"position": 9, "aweme_id": "7437754778232605964", "title": "me.遥遥微光与我同行#黑皮 #熊狒 #1"}], "count_diff": 2}}, {"author_name": "虎子别紧张", "sec_uid": "MS4wLjABAAAAwcKy3p3h5K-_xr7-kKBFpOL4_1-SJbqKd4RgU9RGEJM", "original_api": {"works_count": 40, "top10_ids": ["7540945159833259322", "7538266927169326395", "7535646497867517243", "7533095639304588603", "7530647572420234554", "7528017789778021691", "7524660450921205051", "7522019551363353915", "7519851324536573242", "7517849063145655611"]}, "tikhub_api": {"works_count": 20, "top10_ids": ["7542340995641134396", "7540945159833259322", "7538266927169326395", "7535646497867517243", "7533095639304588603", "7530647572420234554", "7528017789778021691", "7524660450921205051", "7522019551363353915", "7519851324536573242"]}, "differences": {"missing_in_original": [{"position": 1, "aweme_id": "7542340995641134396", "title": "六月不努力 九月上工地"}], "missing_in_tikhub": [{"position": 10, "aweme_id": "7517849063145655611", "title": "一个健忘的人 爱笑的男人 同时也是一个很调皮的人"}], "count_diff": -20}}, {"author_name": "环球军事+", "sec_uid": "MS4wLjABAAAA9hf869jwsyz7nipz8fo3Y0RNAtmYNYz6pa7f_qwm5ml8QA8rbRbhJqWgZIMqizwM", "original_api": {"works_count": 43, "top10_ids": ["7537157517214862619", "7523111085700910386", "7502059053326109962", "7541333745098394926", "7541315518771367219", "7541298934778514738", "7541244881965026606", "7541227250201545993", "7541207716925132082", "7540974523529596179"]}, "tikhub_api": {"works_count": 23, "top10_ids": ["7537157517214862619", "7523111085700910386", "7502059053326109962", "7543563278174276907", "7543560185478909211", "7543553022962437426", "7543542080111381810", "7543518531271396654", "7543475379336465673", "7543466913972735241"]}, "differences": {"missing_in_original": [{"position": 4, "aweme_id": "7543563278174276907", "title": "近距离监控画面！俄军巡航导弹打击基辅市区内目标，导弹高速落地引发剧烈爆炸"}, {"position": 5, "aweme_id": "7543560185478909211", "title": "国防部：空军航空开放活动首次静态展示歼-20 首次安排轰炸机编队通场"}, {"position": 6, "aweme_id": "7543553022962437426", "title": "中国人民解放军仪仗分队将赴越南 参加越南八月革命暨越南国庆80周年纪念活动"}, {"position": 7, "aweme_id": "7543542080111381810", "title": "罕见画面：美军F-35被CH-53K重型运输直升机“拎走” 网友：这是要送进军事博物馆？"}, {"position": 8, "aweme_id": "7543518531271396654", "title": "美太平洋空军报告披露阿拉斯加空军基地F-35坠机事故：根本原因为起落架结冰所致"}, {"position": 9, "aweme_id": "7543475379336465673", "title": "今年“九三阅兵”武器装备初次亮相占比高 结构编成体系化#抗战胜利80周年  #我们的胜利  #媒体精选计划"}, {"position": 10, "aweme_id": "7543466913972735241", "title": "天安门广场布置年号台 为了让更多群众到现场观礼 搭建临时观礼台#抗战胜利80周年 #我们的胜利 #媒体精选计划"}], "missing_in_tikhub": [{"position": 4, "aweme_id": "7541333745098394926", "title": "射程超3000公里 乌克兰新型\"火烈鸟\"巡航导弹发射全记录：细节展示打击能力"}, {"position": 5, "aweme_id": "7541315518771367219", "title": "加沙儿童苦中作乐 一群小孩将空投降落伞当做玩具 拖拽奔跑获得短暂快乐"}, {"position": 6, "aweme_id": "7541298934778514738", "title": "直击联合军乐团训练场：九三阅兵将有多首BGM上新 一起期待住"}, {"position": 7, "aweme_id": "7541244881965026606", "title": "生产线首次公开！记者探访“陆战之王”锻造车间 上百道工序不停生产吨级“猛兽”"}, {"position": 8, "aweme_id": "7541227250201545993", "title": "乌克兰“火烈鸟”导弹车间首次曝光：日产1枚3000公里巡航导弹 预测十月产能翻7倍"}, {"position": 9, "aweme_id": "7541207716925132082", "title": "越南阅兵训练现场：士兵挽起裤腿战高温 使用工具辅助矫正姿势"}, {"position": 10, "aweme_id": "7540974523529596179", "title": "俄军缴获乌军重型多旋翼无人机，修复并重新编程后用来轰炸乌军阵地"}], "count_diff": -20}}, {"author_name": "S先生", "sec_uid": "MS4wLjABAAAAqJIqSxZTu78Uo-Prbrr7s2VZy71KZ3CsPxbKYbIbaAs", "original_api": {"works_count": 39, "top10_ids": ["7541071789519637760", "7538879077462068520", "7535140448566955279", "7534992702556130575", "7533084550709366051", "7532363918065847552", "7530594878335552783", "7530119112560954624", "7530072717904678144", "7530072155934608640"]}, "tikhub_api": {"works_count": 20, "top10_ids": ["7542845243666861352", "7541071789519637760", "7538879077462068520", "7535140448566955279", "7534992702556130575", "7533084550709366051", "7532363918065847552", "7530594878335552783", "7530119112560954624", "7530072717904678144"]}, "differences": {"missing_in_original": [{"position": 1, "aweme_id": "7542845243666861352", "title": "😝"}], "missing_in_tikhub": [{"position": 10, "aweme_id": "7530072155934608640", "title": "哈哈、等他刷到……"}], "count_diff": -19}}, {"author_name": "石头🪨", "sec_uid": "MS4wLjABAAAAFZj25v7NP66V4UYipLFZFx2RO23F2fm5HJm4cEeShAk", "original_api": {"works_count": 43, "top10_ids": ["7309625888641961253", "7457687315704499471", "7401718890306358565", "7541180814034423092", "7540307962867600680", "7540074565385571624", "7539958595191016738", "7539736474053578019", "7538388429047647522", "7537740374699281698"]}, "tikhub_api": {"works_count": 23, "top10_ids": ["7309625888641961253", "7457687315704499471", "7401718890306358565", "7543661718938144000", "7543610049760791860", "7543425439994940707", "7542977470191537460", "7542361406831627554", "7541820698109447424", "7541511063788604672"]}, "differences": {"missing_in_original": [{"position": 4, "aweme_id": "7543661718938144000", "title": "战友说成都是个好地方……不想走了……"}, {"position": 5, "aweme_id": "7543610049760791860", "title": "战友说、他喜欢夏天。@袁小井 #战友"}, {"position": 6, "aweme_id": "7543425439994940707", "title": "今天去做了一天的搬运、干活的男朋友喜欢吗？"}, {"position": 7, "aweme_id": "7542977470191537460", "title": "是不是很胖？"}, {"position": 8, "aweme_id": "7542361406831627554", "title": "撒娇、只对喜欢的人……"}, {"position": 9, "aweme_id": "7541820698109447424", "title": "#肌肉力量"}, {"position": 10, "aweme_id": "7541511063788604672", "title": "我想喜欢的人也喜欢我……"}], "missing_in_tikhub": [{"position": 4, "aweme_id": "7541180814034423092", "title": "下雨天、跟战友避雨的屋檐……@🇨🇳伟小涛🇨🇳"}, {"position": 5, "aweme_id": "7540307962867600680", "title": "今天被人夸了一晚上、真的很开心……"}, {"position": 6, "aweme_id": "7540074565385571624", "title": "去年还没有肌肉……"}, {"position": 7, "aweme_id": "7539958595191016738", "title": "今天锻炼超标了……非常非常累"}, {"position": 8, "aweme_id": "7539736474053578019", "title": "入了心的人，见与不见都思念。"}, {"position": 9, "aweme_id": "7538388429047647522", "title": "我差点就信了……"}, {"position": 10, "aweme_id": "7537740374699281698", "title": "你不在乎的细节 真的 毁了我好多温柔、"}], "count_diff": -20}}, {"author_name": "胡大虫", "sec_uid": "MS4wLjABAAAAfbKsHfWtER8MYZCIIHfn8tzfYZX-OybDnLrZTOvPayw", "original_api": {"works_count": 43, "top10_ids": ["7354097401503829300", "7100384818122231052", "7020398529046744353", "7540607084741496124", "7538010385858317627", "7536747416344890681", "7535625480155155772", "7530783380214058298", "7530218049946537274", "7528366787499789625"]}, "tikhub_api": {"works_count": 23, "top10_ids": ["7354097401503829300", "7100384818122231052", "7020398529046744353", "7543213903335017787", "7542832221192817980", "7540607084741496124", "7538010385858317627", "7536747416344890681", "7535625480155155772", "7530783380214058298"]}, "differences": {"missing_in_original": [{"position": 4, "aweme_id": "7543213903335017787", "title": "买两个小游戏 哈哈、封面越好看的小游戏越呆滞！另外两个小游戏必买，一个比一个好玩，纸人2嘛胆小的就别买了哈哈@抖音创作小助手"}, {"position": 5, "aweme_id": "7542832221192817980", "title": "#mgs3重制版 还没开放，数字版，但是实体版已经偷跑了吧？我还是继续等吧！差不了几天！@抖音创作小助手"}], "missing_in_tikhub": [{"position": 9, "aweme_id": "7530218049946537274", "title": "明末渊虚之羽 #ps5游戏 类魂游戏，菜鸟玩家的噩梦，游戏素质估计一般，不擅长魂类的玩家请谨慎购买，该游戏在xgpu是会免的，xb玩家请随意！马上就要开放游玩了哦！我还是要试试的！@抖音创作小助手"}, {"position": 10, "aweme_id": "7528366787499789625", "title": "不要买就对了 #ps5游戏 #readyornot 太硬核了，一点爽快感都不存在，指挥系统复杂至极并且难以操作！听劝啊、别买就对了！"}], "count_diff": -20}}, {"author_name": "二成", "sec_uid": "MS4wLjABAAAAtA9-Y3_cKBu5VENg7nn5CpQweu_qPMJBvdkref_MK_M", "original_api": {"works_count": 40, "top10_ids": ["7507640751967784249", "6891197031088426247", "7537263929903451450", "7505839884134812987", "7491710455468117305", "7482804374843018554", "7445850800057814332", "7418329470324559104", "7410415343660797218", "7395450823351766312"]}, "tikhub_api": {"works_count": 20, "top10_ids": ["7507640751967784249", "6891197031088426247", "7537263929903451450", "7505839884134812987", "7491710455468117305", "7482804374843018554", "7445850800057814332", "7418329470324559104", "7410415343660797218", "7395450823351766312"]}, "differences": {"missing_in_original": [], "missing_in_tikhub": [], "count_diff": -20}}, {"author_name": "小虎Edgar", "sec_uid": "MS4wLjABAAAA5-j5s3mAdpSxsCMNvZS1-uJB0YZwYnwvMxn1CmkU218", "original_api": {"works_count": 41, "top10_ids": ["7476408519131024640", "7449287483499564322", "7382017732268543283", "7541348101174463796", "7536152753577938216", "7533555234733722880", "7530957288662158632", "7528360326934695208", "7525763743856643368", "7523167855665483060"]}, "tikhub_api": {"works_count": 23, "top10_ids": ["7476408519131024640", "7449287483499564322", "7382017732268543283", "7541348101174463796", "7536152753577938216", "7533555234733722880", "7530957288662158632", "7528360326934695208", "7525763743856643368", "7523167855665483060"]}, "differences": {"missing_in_original": [], "missing_in_tikhub": [], "count_diff": -18}}, {"author_name": "Eternal love for <PERSON>", "sec_uid": "MS4wLjABAAAAx3ZMTtzHh6ARgmOIIg2TTWAhDlmVrgMWBaYLKKfbSZwS_714uhBQ-odXQtyqMcpk", "original_api": {"works_count": 4, "top10_ids": ["7541003342115261734", "7540674713078123827", "7539543789816892682", "7529816949091126537"]}, "tikhub_api": {"works_count": 7, "top10_ids": ["7542059333924785435", "7541799801185553690", "7541460802155826478", "7541003342115261734", "7540674713078123827", "7539543789816892682", "7529816949091126537"]}, "differences": {"missing_in_original": [{"position": 1, "aweme_id": "7542059333924785435", "title": "黏黏的是啥#熊狒 #彩虹"}, {"position": 2, "aweme_id": "7541799801185553690", "title": "你几几年？我来帮你查一下#熊狒 #彩虹"}, {"position": 3, "aweme_id": "7541460802155826478", "title": "唯一#熊狒 #彩虹"}], "missing_in_tikhub": [], "count_diff": 3}}, {"author_name": "B1soul_", "sec_uid": "MS4wLjABAAAAVYQ9tJKYH4c9wFGpKxSCMMr-VulVdTUyzkUlXkAfLM_W5p0NO3kesouQGOtEQ6Xt", "original_api": {"works_count": 7, "top10_ids": ["7540275910423989562", "7541325621055147322", "7540973742681574716", "7539944632379165998", "7539561835843734843", "7474549129042005306", "7463739838257220923"]}, "tikhub_api": {"works_count": 11, "top10_ids": ["7540275910423989562", "7543591068786560314", "7543215954215505162", "7542899648651889979", "7542036293271211290", "7541325621055147322", "7540973742681574716", "7539944632379165998", "7539561835843734843", "7474549129042005306"]}, "differences": {"missing_in_original": [{"position": 2, "aweme_id": "7543591068786560314", "title": "今天不憨憨了吧。#迷彩 #寸头 #青春有很多样子"}, {"position": 3, "aweme_id": "7543215954215505162", "title": "憨憨的日常\n还是好尴尬啊哈哈哈哈#迷彩 #寸头 #青春 #憨憨"}, {"position": 4, "aweme_id": "7542899648651889979", "title": "啊啊啊啊啊尴尬到扣出三室一厅了\n就这样吧#寸头 #迷彩 #彩虹🌈 #青春 #憨憨"}, {"position": 5, "aweme_id": "7542036293271211290", "title": "亲亲😘\n我家猫为什么喜欢舔我啊哈哈哈哈#迷彩 #青春 #寸头"}], "missing_in_tikhub": [{"position": 7, "aweme_id": "7463739838257220923", "title": "傻乎乎的 想谈恋爱啊啊啊啊啊。。。#通讯录 #彩虹🌈 #寸头"}], "count_diff": 4}}, {"author_name": "威廉wy", "sec_uid": "MS4wLjABAAAA3lxKz_AowmAOSUOkoCyhFPHUSWEtJypUkxbJ1gL0Ivs", "original_api": {"works_count": 40, "top10_ids": ["7537649387973152059", "7494709876468256060", "7488194258927734073", "7540570163474844985", "7537025297826237754", "7534026626897841466", "7532531254240955705", "7531783772657765690", "7525696755595136314", "7524295540902677818"]}, "tikhub_api": {"works_count": 22, "top10_ids": ["7537649387973152059", "7494709876468256060", "7488194258927734073", "7540570163474844985", "7537025297826237754", "7534026626897841466", "7532531254240955705", "7531783772657765690", "7525696755595136314", "7524295540902677818"]}, "differences": {"missing_in_original": [], "missing_in_tikhub": [], "count_diff": -18}}, {"author_name": "跟着大师学数学", "sec_uid": "MS4wLjABAAAAirCoFXAfNmGmvmPVK1Wow3xtyR0gk0HSIHbFtKXt3cGa0DcSuQQ0S0e0vWnQWOOO", "original_api": {"works_count": 39, "top10_ids": ["7519479767092235547", "7540682565419453706", "7539573780122389786", "7538038338588462387", "7537529132373822778", "7536928911864663356", "7536910497557679370", "7536854308395535666", "7536203440251620659", "7535269836927798585"]}, "tikhub_api": {"works_count": 19, "top10_ids": ["7519479767092235547", "7541673372755053882", "7540682565419453706", "7539573780122389786", "7538038338588462387", "7537529132373822778", "7536928911864663356", "7536910497557679370", "7536854308395535666", "7536203440251620659"]}, "differences": {"missing_in_original": [{"position": 2, "aweme_id": "7541673372755053882", "title": "王虹教授讲座座无虚席#王虹 #数学 #学习 #清华大学 #北京大学"}], "missing_in_tikhub": [{"position": 10, "aweme_id": "7535269836927798585", "title": "王虹教授讲座座无虚席#王虹 #数学 #学习 #清华大学 #北京大学"}], "count_diff": -20}}, {"author_name": "明阳", "sec_uid": "MS4wLjABAAAA-u36frkwzzViwQ0D4BrfqLGwBeuvIsIC8bbeM3_wCLE9fra6gKVFBrTvKkWcYGan", "original_api": {"works_count": 40, "top10_ids": ["7541338460813823284", "7541007891295489343", "7539857283950791970", "7538772631450488116", "7538007974314528052", "7537622649594776872", "7536924722186472745", "7536409344625249555", "7535420208061123879", "7533245988979854628"]}, "tikhub_api": {"works_count": 20, "top10_ids": ["7542782716359134483", "7543554142515416363", "7541338460813823284", "7541007891295489343", "7539857283950791970", "7538772631450488116", "7538007974314528052", "7537622649594776872", "7536924722186472745", "7536409344625249555"]}, "differences": {"missing_in_original": [{"position": 1, "aweme_id": "7542782716359134483", "title": "⭐九宫格嘉宾招募中\n⭐每晚21：00-23：00\n⭐娱乐\n⭐pk\n⭐团战\n⭐游戏\n⭐竞赛\n每晚21：00-23：00\n有时间的小哥哥记得找我哦[嘿哈][嘿哈][嘿哈][嘿哈]\n@所有人 "}, {"position": 2, "aweme_id": "7543554142515416363", "title": "#直播"}], "missing_in_tikhub": [{"position": 9, "aweme_id": "7535420208061123879", "title": "中国四大谎言 "}, {"position": 10, "aweme_id": "7533245988979854628", "title": "哈哈，按时长大 "}], "count_diff": -20}}, {"author_name": "Sunny☀️粤语电台（全球直播版）", "sec_uid": "MS4wLjABAAAAHT_tTXdwEDFws6zYZGJTpEKNQ2Yq0xmtUCqrWrXPgj5DfVI1R7VGn_aWTLftAC80", "original_api": {"works_count": 8, "top10_ids": ["7523995644562820352", "7540990296647503144", "7537347708379565352", "7536519961936088372", "7535889640273546531", "7535555188355812642", "7534132966902123811", "7526591222190705920"]}, "tikhub_api": {"works_count": 10, "top10_ids": ["7523995644562820352", "7543245092799401268", "7542160671451483407", "7540990296647503144", "7537347708379565352", "7536519961936088372", "7535889640273546531", "7535555188355812642", "7534132966902123811", "7526591222190705920"]}, "differences": {"missing_in_original": [{"position": 2, "aweme_id": "7543245092799401268", "title": "濑濑濑～～～濑晒～～～8月30号凌晨3点11分你会做紧咩啊😏😏😏一个炮火连天既晚上，会唔会听个傻佬一路行一路粤韵风华😅😅😅定系吃完宵夜准备定开第二炮😂😂😂呢几日等我养jing蓄锐，我地30号凌晨见💤💤早抖#快来围观"}, {"position": 3, "aweme_id": "7542160671451483407", "title": "你地成日话我唔唱粤语歌🥺🥺🥺满足你地了！"}], "missing_in_tikhub": [], "count_diff": 2}}, {"author_name": "Z鄭向引力", "sec_uid": "MS4wLjABAAAAl5eucilGM0YTcNvmZ_VsxrBC8RDg6LLLBWEkSd3C9tiptyJNWjifiFswn6COuZrf", "original_api": {"works_count": 12, "top10_ids": ["7537741204718046506", "7535928322691173691", "7533650615889890599", "7531039509539458345", "7529694018423917882", "7527597703706987830", "7520992994041302331", "7517531734050573625", "7516488099272199443", "7515613418105146636"]}, "tikhub_api": {"works_count": 14, "top10_ids": ["7543153774833274170", "7542933391294614823", "7537741204718046506", "7535928322691173691", "7533650615889890599", "7531039509539458345", "7529694018423917882", "7527597703706987830", "7520992994041302331", "7517531734050573625"]}, "differences": {"missing_in_original": [{"position": 1, "aweme_id": "7543153774833274170", "title": "#熊狒#attention"}, {"position": 2, "aweme_id": "7542933391294614823", "title": "#阮梅#崩坏星穹铁道"}], "missing_in_tikhub": [{"position": 9, "aweme_id": "7516488099272199443", "title": "#生活碎片"}, {"position": 10, "aweme_id": "7515613418105146636", "title": "#fortonight"}], "count_diff": 2}}, {"author_name": "军武工厂", "sec_uid": "MS4wLjABAAAAOe0uMVp2HCGxBUOjCFQeEkUo5EReLsdSbS6vc8xkTlW_0gD3CXO3nlHny3tPCyQF", "original_api": {"works_count": 40, "top10_ids": ["7540595165183544634", "7538744704666504506", "7537260131627060540", "7535436561912696121", "7533964084964265273", "7532806379691658555", "7532064491522936123", "7531004028471659836", "7529838114769833273", "7528352919651241275"]}, "tikhub_api": {"works_count": 20, "top10_ids": ["7540595165183544634", "7538744704666504506", "7537260131627060540", "7535436561912696121", "7533964084964265273", "7532806379691658555", "7532064491522936123", "7531004028471659836", "7529838114769833273", "7528352919651241275"]}, "differences": {"missing_in_original": [], "missing_in_tikhub": [], "count_diff": -20}}, {"author_name": "<PERSON><PERSON>-yuu.", "sec_uid": "MS4wLjABAAAAtY8BpEy10iMYMBiEhRk-VEOols3AbGQdEEEOuxkRsPlT8c4MLyAP9PxZiVpenjo8", "original_api": {"works_count": 40, "top10_ids": ["7540365894914297145", "7538457273661001020", "7537981618611637564", "7537725988785196348", "7536853116727037243", "7532435455910219066", "7531972966495472953", "7531370784574246202", "7530683952500559164", "7529938753449102649"]}, "tikhub_api": {"works_count": 20, "top10_ids": ["7543184481843727675", "7540365894914297145", "7538457273661001020", "7537981618611637564", "7537725988785196348", "7536853116727037243", "7532435455910219066", "7531972966495472953", "7531370784574246202", "7530683952500559164"]}, "differences": {"missing_in_original": [{"position": 1, "aweme_id": "7543184481843727675", "title": "北京·雨🌧️ 喝完了，等拉稀 #络腮胡 #猪猪 #喝水日常"}], "missing_in_tikhub": [{"position": 10, "aweme_id": "7529938753449102649", "title": "大病一场 #猪猪 #络腮胡 #喝水日常"}], "count_diff": -20}}, {"author_name": "就一丢丢肥", "sec_uid": "MS4wLjABAAAA1FXT6o5KEKPx03pymbZltqFNK5vsD1ONgvC9cUBhCqxWORkHVrOZjRcxsJw7wg-Y", "original_api": {"works_count": 40, "top10_ids": ["7541089127178423612", "7541053179062144313", "7541030594614185275", "7540993180520090940", "7540992877297503547", "7540954413990890812", "7540952128783093052", "7540951935257988410", "7540931301755784507", "7540668431965605177"]}, "tikhub_api": {"works_count": 20, "top10_ids": ["7543592787382373692", "7543196104466533692", "7543185272159604027", "7542933972305743164", "7542493660912471353", "7542394883152547129", "7542187816228556091", "7541742206655745337", "7541686453508836667", "7541089127178423612"]}, "differences": {"missing_in_original": [{"position": 1, "aweme_id": "7543592787382373692", "title": "#撒娇天才养成计划 \n#热门#娱乐#抽象\n简简单单My son!"}, {"position": 2, "aweme_id": "7543196104466533692", "title": "10天。#热门#娱乐#摩旅"}, {"position": 3, "aweme_id": "7543185272159604027", "title": "到家，理个发，收拾下"}, {"position": 4, "aweme_id": "7542933972305743164", "title": "waiting……骑摩托车脖子有点不舒服……好了看看\n#寸头 #娱乐#热门#抽象"}, {"position": 5, "aweme_id": "7542493660912471353", "title": "南京-夫子庙\n#热门#娱乐#抽象"}, {"position": 6, "aweme_id": "7542394883152547129", "title": "南京今天有点热， 鸡鸣寺！"}, {"position": 7, "aweme_id": "7542187816228556091", "title": "南京。打个卡"}, {"position": 8, "aweme_id": "7541742206655745337", "title": "儿子,爸爸到扬州了，现在准备去泰州!\n#热门#娱乐#抽象"}, {"position": 9, "aweme_id": "7541686453508836667", "title": "#DJI  南京不给骑摩托车，爸爸已经到达了南京南站了\n#热门#娱乐#抽象"}], "missing_in_tikhub": [{"position": 2, "aweme_id": "7541053179062144313", "title": "第一次淋雨\n#热门#娱乐#摩旅"}, {"position": 3, "aweme_id": "7541030594614185275", "title": ""}, {"position": 4, "aweme_id": "7540993180520090940", "title": "儿子，爸爸吃晚饭了，最爱的还是蜜雪冰城！\n#热门#娱乐#抽象"}, {"position": 5, "aweme_id": "7540992877297503547", "title": "准备出黄山了，儿子！\n#热门#娱乐#抽象#摩旅"}, {"position": 6, "aweme_id": "7540954413990890812", "title": "黄山，遇雨，不错！#热门#娱乐#抽象#摩旅"}, {"position": 7, "aweme_id": "7540952128783093052", "title": ""}, {"position": 8, "aweme_id": "7540951935257988410", "title": ""}, {"position": 9, "aweme_id": "7540931301755784507", "title": "拉了坨大的！#热门#娱乐#抽象\n#摩旅"}, {"position": 10, "aweme_id": "7540668431965605177", "title": "安徽省黄山市-宏村\n#热门#摩旅"}], "count_diff": -20}}, {"author_name": "火鸡", "sec_uid": "MS4wLjABAAAA2SPZHqsDbhdwb6N3-Cx3YG2-e8KTpb4Zs94BsNN79Xw", "original_api": {"works_count": 42, "top10_ids": ["7385433078761540900", "7344650885798038795", "7469019430010359081", "7540993321876245787", "7534296675613117747", "7533171295368056073", "7531350199479913755", "7529847896578592010", "7525420812745264411", "7523920687741521190"]}, "tikhub_api": {"works_count": 23, "top10_ids": ["7385433078761540900", "7344650885798038795", "7469019430010359081", "7540993321876245787", "7534296675613117747", "7533171295368056073", "7531350199479913755", "7529847896578592010", "7525420812745264411", "7523920687741521190"]}, "differences": {"missing_in_original": [], "missing_in_tikhub": [], "count_diff": -19}}, {"author_name": "南方水牛（耕牛）", "sec_uid": "MS4wLjABAAAAl_NPNOAijWuZusrr8-m-mrJhhBfEUF2VjbpNvgR0KHsck9uLLBC9F36u515xEK78", "original_api": {"works_count": 43, "top10_ids": ["7354712036397436223", "7409637967746141476", "7330597315536899340", "7541366606947126538", "7540987860310265098", "7540253000184565043", "7539884183222226214", "7539140029416557874", "7538771773141191974", "7538027192840998154"]}, "tikhub_api": {"works_count": 23, "top10_ids": ["7354712036397436223", "7409637967746141476", "7330597315536899340", "7542482038986476809", "7542120062787521819", "7541366606947126538", "7540987860310265098", "7540253000184565043", "7539884183222226214", "7539140029416557874"]}, "differences": {"missing_in_original": [{"position": 4, "aweme_id": "7542482038986476809", "title": "牛顿 眼神纯真，水牛吃草。#吃草 #牛顿 #水牛 #纯真 #眼神"}, {"position": 5, "aweme_id": "7542120062787521819", "title": "牛顿 看什么那么入迷，水牛吃草。#牛顿 #水牛 #吃草 #纯真 #眼神"}], "missing_in_tikhub": [{"position": 9, "aweme_id": "7538771773141191974", "title": "喜欢吃草，农村水牛。#吃草 #水牛 #农村 #解压 #牛顿"}, {"position": 10, "aweme_id": "7538027192840998154", "title": "吃草 青草肥美，农村水牛。#吃草 #水牛 #农村 #解压 #纯真"}], "count_diff": -20}}, {"author_name": "Apple", "sec_uid": "MS4wLjABAAAAsFK8qDk2oXQ4Wnjt_HkPczoab32qhAK5z3Gn8pWIzDp_toc1YhMef4eaDYbPLDrR", "original_api": {"works_count": 40, "top10_ids": ["7526772049516219674", "7527519460970761524", "7524993585171762482", "7519408242737351971", "7514910497238486322", "7514657959637470473", "7514657768671005986", "7514652637573205285", "7511558366529408290", "7511555143737806080"]}, "tikhub_api": {"works_count": 20, "top10_ids": ["7542929878618770723", "7526772049516219674", "7527519460970761524", "7524993585171762482", "7519408242737351971", "7514910497238486322", "7514657959637470473", "7514657768671005986", "7514652637573205285", "7511558366529408290"]}, "differences": {"missing_in_original": [{"position": 1, "aweme_id": "7542929878618770723", "title": "北京时间 9 月 10 日凌晨 1 点，Apple 特别活动在 Apple 官网准时直播，到时来感受一下今年特有的热度吧。 #Apple特别活动"}], "missing_in_tikhub": [{"position": 10, "aweme_id": "7511555143737806080", "title": "天，岳岳的聊天 app 被人打开了！还好，App 可锁定可隐藏，刷你的脸才能打开。#iPhone保护隐私超省事"}], "count_diff": -20}}, {"author_name": "六本木", "sec_uid": "MS4wLjABAAAA1sBhMiQTetAacwWvn75lQ8STFMHkSZJ5NlVyWPHJ8tI", "original_api": {"works_count": 40, "top10_ids": ["7286614276788292919", "7283909671025397029", "7376103853369920805", "7532000173569494312", "7531538683267943731", "7526004722596531513", "7509258121949302016", "7503102730416966927", "7490073344566447394", "7487486773153598772"]}, "tikhub_api": {"works_count": 23, "top10_ids": ["7286614276788292919", "7283909671025397029", "7376103853369920805", "7542307607189916943", "7532000173569494312", "7531538683267943731", "7526004722596531513", "7509258121949302016", "7503102730416966927", "7490073344566447394"]}, "differences": {"missing_in_original": [{"position": 4, "aweme_id": "7542307607189916943", "title": "都2025年了 何必还恋恋不忘2024年的人 #top #小叔叔 #摄影师"}], "missing_in_tikhub": [{"position": 10, "aweme_id": "7487486773153598772", "title": "我的摄影日记#彩虹🌈 #脂包肌 #可狼可奶 @威廉wy"}], "count_diff": -17}}, {"author_name": "憨憨🇨🇳", "sec_uid": "MS4wLjABAAAAe0BydZSauCC2dYHR5HqYZm2M4o5JJygJ0W-LQAsr9l-kiROiEIymew3sYw6MXjdt", "original_api": {"works_count": 40, "top10_ids": ["7541005236107775289", "7540518705474702651", "7539828071965936954", "7539486031888977212", "7539228201135934779", "7539168846189710650", "7538846505442168121", "7538801576757890364", "7538391417882889529", "7538060815888239929"]}, "tikhub_api": {"works_count": 20, "top10_ids": ["7543204724489637180", "7542089592523935036", "7541005236107775289", "7540518705474702651", "7539828071965936954", "7539486031888977212", "7539228201135934779", "7539168846189710650", "7538846505442168121", "7538801576757890364"]}, "differences": {"missing_in_original": [{"position": 1, "aweme_id": "7543204724489637180", "title": "一个新词叫“拾起自己”\n简单自私的活着\n以自己为中心\n以自己最优先\n#要适应这个世界上所有的温度"}, {"position": 2, "aweme_id": "7542089592523935036", "title": "#机动战士高达 #疲惫的生活里总要有温柔的梦想໑ “这个世界什么都有，就是没有如果”"}], "missing_in_tikhub": [{"position": 9, "aweme_id": "7538391417882889529", "title": "#夏天该有的样子 夏天的浪漫，玩水占一半。"}, {"position": 10, "aweme_id": "7538060815888239929", "title": "#强子制燥 音乐节感谢老师们带我一起玩，素未谋面哈哈 @Mr张 🦔   #z纪元银河左岸音乐节"}], "count_diff": -20}}, {"author_name": "江苏省城市足球联赛", "sec_uid": "MS4wLjABAAAAKzVdnd5vaKlm_uYPXiThFvRJqxzv1QMOLP_YKki8IUz7Abda5zaahC87GR6W3b29", "original_api": {"works_count": 43, "top10_ids": ["7510823578028985641", "7502323683730509075", "7502312118314601764", "7540940328296009003", "7540918499036630315", "7540590842117983507", "7540549613284003111", "7539840005691198761", "7539565607727058215", "7539565399893970239"]}, "tikhub_api": {"works_count": 23, "top10_ids": ["7510823578028985641", "7502323683730509075", "7502312118314601764", "7543552254444358912", "7543472102460968244", "7543152293751934247", "7543075404043652395", "7540940328296009003", "7540918499036630315", "7540590842117983507"]}, "differences": {"missing_in_original": [{"position": 4, "aweme_id": "7543552254444358912", "title": "本周末锁定官方直播间，观看城市联赛第十轮精彩对决#江苏省城市足球联赛 #城市荣耀绿茵争锋"}, {"position": 5, "aweme_id": "7543472102460968244", "title": "城市联赛第十一轮预约购票规则优化 #江苏省城市足球联赛 #城市荣耀绿茵争锋"}, {"position": 6, "aweme_id": "7543152293751934247", "title": "《我家门口的足球场》系列（无锡赛区） 一座足球场的对白-云林生态体育公园#江苏省城市足球联赛 #城市荣耀绿茵争锋"}, {"position": 7, "aweme_id": "7543075404043652395", "title": "城市联赛解说天团，你中意谁？ #江苏省城市联赛 #城市荣耀绿茵争锋"}], "missing_in_tikhub": [{"position": 7, "aweme_id": "7540549613284003111", "title": "联赛第九轮，南通主场全场齐声高唱《歌唱祖国》 #江苏省城市足球联赛 #城市荣耀绿茵争锋"}, {"position": 8, "aweme_id": "7539840005691198761", "title": "城市联赛第9轮第二比赛日集锦#江苏省城市足球联赛 #城市荣耀绿茵争锋"}, {"position": 9, "aweme_id": "7539565607727058215", "title": "城市联赛射手榜#江苏省城市足球联赛 #城市荣耀绿茵争锋"}, {"position": 10, "aweme_id": "7539565399893970239", "title": "城市联赛积分榜（截止8月17日）#江苏省城市足球联赛 #城市荣耀绿茵争锋"}], "count_diff": -20}}, {"author_name": "五月份", "sec_uid": "MS4wLjABAAAAybnz6NJ0GEY0oqSiingGq6S6TWrbCuwH072pcQB7Y8g", "original_api": {"works_count": 30, "top10_ids": ["7538367765326990633", "7535654821664771391", "7523894440739654954", "7517620481824410899", "7515715435468164393", "7512337879746874665", "7510802158317554956", "7506189591699819788", "7499447858749590794", "7490501547290381618"]}, "tikhub_api": {"works_count": 18, "top10_ids": ["7538367765326990633", "7535654821664771391", "7523894440739654954", "7517620481824410899", "7515715435468164393", "7512337879746874665", "7510802158317554956", "7506189591699819788", "7499447858749590794", "7490501547290381618"]}, "differences": {"missing_in_original": [], "missing_in_tikhub": [], "count_diff": -12}}, {"author_name": "忠雄不是熊", "sec_uid": "MS4wLjABAAAA5hGtim9b_uY0Snq8GewfSBZpWPw-lR8E1EmWActTwLy0PAEub6anOS3uVr5i9X5v", "original_api": {"works_count": 40, "top10_ids": ["7540534905512824123", "7538699890910858556", "7537948294707989817", "7536918647458516283", "7535819428002303290", "7535286016573443385", "7533809686974418234", "7532105513992244540", "7531182240689261883", "7530153795561753914"]}, "tikhub_api": {"works_count": 20, "top10_ids": ["7543531418707873081", "7542542531260566841", "7540534905512824123", "7538699890910858556", "7537948294707989817", "7536918647458516283", "7535819428002303290", "7535286016573443385", "7533809686974418234", "7532105513992244540"]}, "differences": {"missing_in_original": [{"position": 1, "aweme_id": "7543531418707873081", "title": "这手套好说不说还是蛮好看的，谢谢某人的七夕礼物啦#健身 #寸头 #黑皮 #壮熊 #小叔叔"}, {"position": 2, "aweme_id": "7542542531260566841", "title": "热热热炸了#快乐羽毛球 #出汗 #黑皮体育生 #运动户外"}], "missing_in_tikhub": [{"position": 9, "aweme_id": "7531182240689261883", "title": "笑鼠，还可以动起来#AI奥运力士"}, {"position": 10, "aweme_id": "7530153795561753914", "title": "健身小趴菜，怎么才能练大呢#小叔叔 #你的男朋友 #黑皮 #健身"}], "count_diff": -20}}, {"author_name": "临沂弘扬", "sec_uid": "MS4wLjABAAAAdZtmOAAmX2xL5PRJ8bIGLdFewU0I-bmt2oNoVye9UmA", "original_api": {"works_count": 43, "top10_ids": ["7443722086767480101", "7460372039279430975", "7466367397004447012", "7540246922562653466", "7539505308541963566", "7538020413348547890", "7537305656207510830", "7535794321166617865", "7535051787679223090", "7534309545331608883"]}, "tikhub_api": {"works_count": 23, "top10_ids": ["7443722086767480101", "7460372039279430975", "7466367397004447012", "7543586695926713651", "7542844529511222554", "7541788039606881582", "7540246922562653466", "7539505308541963566", "7538020413348547890", "7537305656207510830"]}, "differences": {"missing_in_original": [{"position": 4, "aweme_id": "7543586695926713651", "title": "当你兄弟拧不动瓶盖时 #搞笑 #笑到肚子疼 #一定要看到最后 #弘扬兄弟"}, {"position": 5, "aweme_id": "7542844529511222554", "title": "当你喝到很冰镇的饮料时 #搞笑 #笑到肚子疼 #万万没想到 #弘扬兄弟"}, {"position": 6, "aweme_id": "7541788039606881582", "title": "当你拽不动公园垃圾桶时 #搞笑 #万万没想到 #笑到肚子疼 #弘扬兄弟"}], "missing_in_tikhub": [{"position": 8, "aweme_id": "7535794321166617865", "title": "当公园石墩被太阳暴晒后 #搞笑 #万万没想到 #笑到肚子疼 #弘扬兄弟"}, {"position": 9, "aweme_id": "7535051787679223090", "title": "当你的车被太阳暴晒后 #搞笑 #笑到肚子疼 #一定要看到结尾 #弘扬兄弟"}, {"position": 10, "aweme_id": "7534309545331608883", "title": "当公园扭腰凳被太阳暴晒后 #搞笑 #万万没想到 #笑到肚子疼 #弘扬兄弟"}], "count_diff": -20}}, {"author_name": "嘟嘟嘟嘟", "sec_uid": "MS4wLjABAAAAt2oIFfEmMlG_VRc50piPY36atGa47qh31rvcvLTBkNM", "original_api": {"works_count": 4, "top10_ids": ["7540754702839123200", "7539166680879254824", "7536969891851046144", "7535491445122911534"]}, "tikhub_api": {"works_count": 6, "top10_ids": ["7542422987526933812", "7541645467890879795", "7540754702839123200", "7539166680879254824", "7536969891851046144", "7535491445122911534"]}, "differences": {"missing_in_original": [{"position": 1, "aweme_id": "7542422987526933812", "title": "#足球 正脚背抽到的感觉也太爽了"}, {"position": 2, "aweme_id": "7541645467890879795", "title": "球场小懒猪#快乐足球"}], "missing_in_tikhub": [], "count_diff": 2}}, {"author_name": "喂維喂，杰", "sec_uid": "MS4wLjABAAAAIgUqwfZtvi7AA1aCfXt6Z_yapDCzZTqesSFWH_b4QTCEIt9-y6VWCsRIjHm3yGHE", "original_api": {"works_count": 40, "top10_ids": ["7539479137002507566", "7539182197144735036", "7533629794265943353", "7530872583818874170", "7529170559129767225", "7526557160333217083", "7523492354490764604", "7517946444954668348", "7516903227977026874", "7512266479439596860"]}, "tikhub_api": {"works_count": 20, "top10_ids": ["7541783464401472826", "7539479137002507566", "7539182197144735036", "7533629794265943353", "7530872583818874170", "7529170559129767225", "7526557160333217083", "7523492354490764604", "7517946444954668348", "7516903227977026874"]}, "differences": {"missing_in_original": [{"position": 1, "aweme_id": "7541783464401472826", "title": "#小肚腩 #夏季 #糙汉也清新 秋老虎快走了，冬天还远吗？"}], "missing_in_tikhub": [{"position": 10, "aweme_id": "7512266479439596860", "title": "35 块，质量还行。麦门永存！#夏天到了 #日常生活 #每日分享"}], "count_diff": -20}}, {"author_name": "布德德远-<PERSON>", "sec_uid": "MS4wLjABAAAAA-hz_vsKXLaPBoGEDpOeWNpcf_4Ge82FAuUg73ce4E4", "original_api": {"works_count": 42, "top10_ids": ["7287532201472707901", "7224083942721228047", "7259715534495796541", "7541340035371666715", "7540579210873376051", "7540171860651298091", "7539832808101924132", "7539492751152221449", "7538739642268388658", "7537254936150314278"]}, "tikhub_api": {"works_count": 22, "top10_ids": ["7287532201472707901", "7224083942721228047", "7259715534495796541", "7543158041921211711", "7542042966418181385", "7541340035371666715", "7540579210873376051", "7540171860651298091", "7539832808101924132", "7539492751152221449"]}, "differences": {"missing_in_original": [{"position": 4, "aweme_id": "7543158041921211711", "title": "给肌肉男@肥空 做刀，经常训练肌肉酸痛的不行还有一些大大小小的损伤。处理完后他说活动度增加了发力感更强了肌肉看起来更抛了线条也更好了。一身轻松！感谢认可 #肉壮 #壮熊 #筋膜刀 #健身"}, {"position": 5, "aweme_id": "7542042966418181385", "title": "腿日 #健身 #肉壮 #壮熊"}], "missing_in_tikhub": [{"position": 9, "aweme_id": "7538739642268388658", "title": "给小哥哥处理完全身都轻松了 #健身 #筋膜放松 #壮熊"}, {"position": 10, "aweme_id": "7537254936150314278", "title": "背日 #健身 #毫无训练痕迹 #肉壮 #壮熊 #性张力"}], "count_diff": -20}}, {"author_name": "黑魏，！", "sec_uid": "MS4wLjABAAAAuEUEKAMUFJjqH7RfNeWqZ5StkMu-gecRbSD09yL5q-A82lRiZZBUyF5UYZGJGjEh", "original_api": {"works_count": 28, "top10_ids": ["7540876420366273851", "7540497586260954426", "7539758936054336828", "7539384878682787132", "7537902825846689084", "7536786639767653689", "7535675757714312508", "7535301964189633852", "7530849703040175418", "7530107029902675258"]}, "tikhub_api": {"works_count": 20, "top10_ids": ["7543466262324858169", "7541982819845868860", "7540876420366273851", "7540497586260954426", "7539758936054336828", "7539384878682787132", "7537902825846689084", "7536786639767653689", "7535675757714312508", "7535301964189633852"]}, "differences": {"missing_in_original": [{"position": 1, "aweme_id": "7543466262324858169", "title": "太妖娆了，氛不出来了，#原创视频 #搞笑 #日常生活 #vlog十亿流量扶持计划"}, {"position": 2, "aweme_id": "7541982819845868860", "title": "保佑你，让你一天舒服的，快乐的，#搞笑 #日常生活 #原创内容"}], "missing_in_tikhub": [{"position": 9, "aweme_id": "7530849703040175418", "title": "花样百出，#日常生活 #搞笑 #vlog十亿流量扶持计划 #内容太过真实"}, {"position": 10, "aweme_id": "7530107029902675258", "title": "让你女友安全感满满，#搞笑 #日常生活 #vlog十亿流量扶持计划"}], "count_diff": -8}}, {"author_name": "来场雨", "sec_uid": "MS4wLjABAAAA_uPZcbUMuJTp7W6PiKUsagks0F2qv07LfL0Y_fI1oXk92vQUaHbpg8xUFWkkPA8B", "original_api": {"works_count": 8, "top10_ids": ["7501353237740670249", "7532127008681512252", "7516588110832405819", "7496184465351200012", "7487447588220833078", "7470163000201202956", "7466848521770028329", "7465657634167835964"]}, "tikhub_api": {"works_count": 8, "top10_ids": ["7501353237740670249", "7532127008681512252", "7516588110832405819", "7496184465351200012", "7487447588220833078", "7470163000201202956", "7466848521770028329", "7465657634167835964"]}, "differences": {"missing_in_original": [], "missing_in_tikhub": [], "count_diff": 0}}, {"author_name": "八  一", "sec_uid": "MS4wLjABAAAA3_POvs3i1xLr5Wn5TKFGnqjUllKEmvT6JFhCdruS6zw", "original_api": {"works_count": 7, "top10_ids": ["7534550763743415612", "7526350099870747963", "7522279428254125372", "7514837911981247803", "7508518009460739387", "7326376454235704628", "7326170882878082344"]}, "tikhub_api": {"works_count": 7, "top10_ids": ["7534550763743415612", "7526350099870747963", "7522279428254125372", "7514837911981247803", "7508518009460739387", "7326376454235704628", "7326170882878082344"]}, "differences": {"missing_in_original": [], "missing_in_tikhub": [], "count_diff": 0}}, {"author_name": "世界五百强💊制药", "sec_uid": "MS4wLjABAAAAOQpNm2ej5oUOLwW1l9gmMgVrJNRP25BvR01TzBHzWm7f3pDrF1r1-ASrViYDZKWd", "original_api": {"works_count": 5, "top10_ids": ["7399024766251764992", "7368140768944360744", "7321572921061772578", "7308886827509894434", "6965874008713596168"]}, "tikhub_api": {"works_count": 5, "top10_ids": ["7399024766251764992", "7368140768944360744", "7321572921061772578", "7308886827509894434", "6965874008713596168"]}, "differences": {"missing_in_original": [], "missing_in_tikhub": [], "count_diff": 0}}, {"author_name": "江苏新闻", "sec_uid": "MS4wLjABAAAA6WP-_5unmtt_edJ6BzntuirZGdvIOtEm6PXkOFCp_1E", "original_api": {"works_count": 42, "top10_ids": ["7537510997955841320", "7529189049140055339", "7541427677840870694", "7541404443820952847", "7541385680828501248", "7541385513136147747", "7541371126887222568", "7541369120261000488", "7541368897933479203", "7541366307808202018"]}, "tikhub_api": {"works_count": 23, "top10_ids": ["7543157733353065728", "7537510997955841320", "7529189049140055339", "7543652035028159744", "7543642306096729379", "7543640685434375464", "7543604683504422178", "7543604395447995648", "7543601230845611299", "7543595458015399220"]}, "differences": {"missing_in_original": [{"position": 1, "aweme_id": "7543157733353065728", "title": "28岁男子心跳骤停倒地，妻子一声呼救来了七八名医护@江苏省中西医结合医院"}, {"position": 4, "aweme_id": "7543652035028159744", "title": "“七夕”掌管大屏的都是什么人啊？！ 南京大屏又又又又开始整活儿了！#七夕节 #爱情"}, {"position": 5, "aweme_id": "7543642306096729379", "title": "双拳执笔的“烧伤男孩”来东南大学报到啦！"}, {"position": 6, "aweme_id": "7543640685434375464", "title": "电影《南京照相馆》摘得“金鹿奖”最佳影片、最佳导演、最佳编剧三项大奖，观影人次已超7900万！#南京照相馆"}, {"position": 7, "aweme_id": "7543604683504422178", "title": "“十三太保”不仅懂点足球的激情，还会点爱情的浪漫！8月29日，我们的节日·七夕主题文化活动，相约盐城东台"}, {"position": 8, "aweme_id": "7543604395447995648", "title": "镇江举办“一纸婚书 见证岁月”主题活动，解锁不同年代的爱情印记"}, {"position": 9, "aweme_id": "7543601230845611299", "title": "9.9元延误险理赔得碰运气？相关产品已下架！"}, {"position": 10, "aweme_id": "7543595458015399220", "title": "生态“朋友圈”再扩容 江苏近8年发现约30种新记录物种"}], "missing_in_tikhub": [{"position": 3, "aweme_id": "7541427677840870694", "title": "镇江队15岁小将张国庆：下一场对战南京队，有难度更有态度！拼就对了！"}, {"position": 4, "aweme_id": "7541404443820952847", "title": "陈怡飞往广州探望吴煜辉，呼吁大家关注罕见病群体。吴煜辉：我会像海燕一样坚强！"}, {"position": 5, "aweme_id": "7541385680828501248", "title": "“借”车摘菜是假 卖电瓶是真！男子盗窃车辆被拘留"}, {"position": 6, "aweme_id": "7541385513136147747", "title": "哪些领域增长好？1-7月江苏经济运行亮点来了！"}, {"position": 7, "aweme_id": "7541371126887222568", "title": "#苏超 解说员解说员洪超揭秘动人解说词诞生 “我是团队的嘴替，也是内心的嘴替”"}, {"position": 8, "aweme_id": "7541369120261000488", "title": "有理想 有比亚迪 #苏超 顶流常州大有来头！"}, {"position": 9, "aweme_id": "7541368897933479203", "title": "一次“打卡”四大展！江苏省美术馆推出精品展览"}, {"position": 10, "aweme_id": "7541366307808202018", "title": "#苏超 探营丨镇江队代理教练谈近期重点：加强后防线 提高全队默契度"}], "count_diff": -19}}, {"author_name": "壹个宇", "sec_uid": "MS4wLjABAAAAfqiIianZONdYpqRssqVxu_vQtScUOKd73NOuCKIYvBk", "original_api": {"works_count": 40, "top10_ids": ["7529024993424117050", "7539983332932373819", "7539134387985468729", "7538101934219267386", "7537640445205368121", "7537252991972756794", "7537006350623591740", "7536763679925865786", "7536628673987628345", "7536300969027571002"]}, "tikhub_api": {"works_count": 21, "top10_ids": ["7529024993424117050", "7539983332932373819", "7539134387985468729", "7538101934219267386", "7537640445205368121", "7537252991972756794", "7537006350623591740", "7536763679925865786", "7536628673987628345", "7536300969027571002"]}, "differences": {"missing_in_original": [], "missing_in_tikhub": [], "count_diff": -19}}, {"author_name": "TOP 苏州", "sec_uid": "MS4wLjABAAAA5sIqbzOXK3yepaknpAsN8eMw8Tt0A7g9Q7QgBAuKVUs", "original_api": {"works_count": 43, "top10_ids": ["7522360711717113134", "7472716124602240266", "7474461520932392211", "7539716924553399561", "7539714081553485107", "7539710661471816970", "7539381719439297818", "7538987649105693961", "7538783412279168283", "7538433603345075466"]}, "tikhub_api": {"works_count": 23, "top10_ids": ["7522360711717113134", "7472716124602240266", "7474461520932392211", "7543628649480588598", "7543243118183795968", "7542491101014330674", "7542151568234220827", "7539716924553399561", "7539714081553485107", "7539710661471816970"]}, "differences": {"missing_in_original": [{"position": 4, "aweme_id": "7543628649480588598", "title": "你敢想象吗？花桥中骏世界城开业11个月累计销售突破10亿+（2024年9月20日~2025年8月20日），这只是昆山一个镇上的综合体！这就是苏州作为江苏一哥的实力！#花桥 #花桥中骏世界城 #昆山 #苏州"}, {"position": 5, "aweme_id": "7543243118183795968", "title": "昆山城东又一新的商业综合体中建·泓里来了！紧邻昆山奥体中心，目前装修中，期待早日开业！#昆山 #昆山中建泓里 #昆山城东 #昆山商业综合体"}, {"position": 6, "aweme_id": "7542491101014330674", "title": "你敢想象这只是江苏的一个镇吗？高楼遍地，两条轨交穿镇而过！#花桥 #花桥镇 #昆山花桥 #苏州昆山"}, {"position": 7, "aweme_id": "7542151568234220827", "title": "谁能拒绝夜晚的金鸡湖！每一帧都是大片！这是独属苏州人自己的浪漫！#旅行推荐官 #金鸡湖 #城市夜景 #标志性建筑"}], "missing_in_tikhub": [{"position": 7, "aweme_id": "7539381719439297818", "title": "苏超苏州队暴露的问题分析 8月16日苏超联赛苏州队主场1:1泰州！结果差强人意！苏州队0:3输给淮安队暴露的右路防守问题未得到很好解决，苏州后卫3老带1小（1小在左路，左边前卫徐睿经常会协防到后场，所以左路不容易被打穿），右路防守球员年龄偏大（35+），转身慢速度跟不上，当右边前卫陆子皓攻上去后，右路就会给对手留下致命反击的空间！期待做好总结，以更好的状态面对后面的比赛！#苏州 #苏超 #苏州队 #泰州队"}, {"position": 8, "aweme_id": "7538987649105693961", "title": "今夜只谈足球，“苏超”第九轮苏州VS泰州，将于今天8月16日（周六）19:00在苏州市体育中心体育场进行！#苏超 #苏州 #苏州队 #泰州队"}, {"position": 9, "aweme_id": "7538783412279168283", "title": "今夜的昆山夏驾河皇冠假日酒店灯火通明，两岸青春星空Party热力爆表！汇聚青创力，铸就新未来！祝2025两岸青年企业家菁英论坛暨两岸青年研学交流活动成功举办！#昆山 #昆山皇冠假日酒店 #两岸一家亲 #两岸青年"}, {"position": 10, "aweme_id": "7538433603345075466", "title": "昆山开发区音乐市集奇妙夜！今夜河畔律动，淘趣夏驾！#昆山 #昆山开发区 #夏驾河 #音乐现场"}], "count_diff": -20}}, {"author_name": "东营区龙耀搏击馆", "sec_uid": "MS4wLjABAAAAk245RTq193dNcmv2v6fRkigKehilyWddywMXh3bTzq8", "original_api": {"works_count": 40, "top10_ids": ["7454411986495950138", "7450331013709925690", "7534988876935335226", "7529125853986721082", "7528802135363210554", "7528304820437732668", "7524217133522652474", "7515607592388742460", "7507298092279729467", "7506693405343862074"]}, "tikhub_api": {"works_count": 22, "top10_ids": ["7454411986495950138", "7450331013709925690", "7542710365109816633", "7534988876935335226", "7529125853986721082", "7528802135363210554", "7528304820437732668", "7524217133522652474", "7515607592388742460", "7507298092279729467"]}, "differences": {"missing_in_original": [{"position": 3, "aweme_id": "7542710365109816633", "title": "上午八个地市教学比赛#摔跤"}], "missing_in_tikhub": [{"position": 10, "aweme_id": "7506693405343862074", "title": "碎片🧩#教练的日常 #东营同城 #零基础学格斗"}], "count_diff": -18}}, {"author_name": "大虎", "sec_uid": "MS4wLjABAAAANOu1bscJvtOA4a5Xr1ckS1RARWqQEzsmF8ciuBb06AlMxK_fKxIBM6Ns37nuuSY6", "original_api": {"works_count": 13, "top10_ids": ["7511529783420702011", "7503911698652745017", "7491968029845196089", "7485010680043654457", "7484716080108293435", "7483520091775126842", "7482086300351352121", "7480934431419682105", "7479317829624384826", "7474958244872506684"]}, "tikhub_api": {"works_count": 14, "top10_ids": ["7543343763906759995", "7511529783420702011", "7503911698652745017", "7491968029845196089", "7485010680043654457", "7484716080108293435", "7483520091775126842", "7482086300351352121", "7480934431419682105", "7479317829624384826"]}, "differences": {"missing_in_original": [{"position": 1, "aweme_id": "7543343763906759995", "title": "我真的睡不着啊！#圆脸 #寸头 #络腮胡"}], "missing_in_tikhub": [{"position": 10, "aweme_id": "7474958244872506684", "title": "练了 #健身日常 #自律 #小胖子"}], "count_diff": 1}}, {"author_name": "雨夜☂︎（清唱助眠）", "sec_uid": "MS4wLjABAAAAKBWp6Yx8nhQeUqMJjp0edMJYkh0z9Xl6kqfYsW2zbB70rH4TC9h-FNzDHEkfM_qN", "original_api": {"works_count": 2, "top10_ids": ["7509526623717625145", "7518789167758511418"]}, "tikhub_api": {"works_count": 2, "top10_ids": ["7509526623717625145", "7518789167758511418"]}, "differences": {"missing_in_original": [], "missing_in_tikhub": [], "count_diff": 0}}, {"author_name": "阿文鸭🌈", "sec_uid": "MS4wLjABAAAAMSTnDtrin3vcjAsB8ShvR6xGv6CwhDzCH6cKfsxs8Wq_3wYyUZIw4mnz66FEM-ZB", "original_api": {"works_count": 40, "top10_ids": ["7541339737648532796", "7539120908557749563", "7534977002614033724", "7531676052738690363", "7530498893117984060", "7530161534644981049", "7529825332377095482", "7529433156882337084", "7529070747215924538", "7527131278434880827"]}, "tikhub_api": {"works_count": 20, "top10_ids": ["7543191026972183867", "7542214041873042747", "7541339737648532796", "7539120908557749563", "7534977002614033724", "7531676052738690363", "7530498893117984060", "7530161534644981049", "7529825332377095482", "7529433156882337084"]}, "differences": {"missing_in_original": [{"position": 1, "aweme_id": "7543191026972183867", "title": "不打算从家里带了，好重的…#圆寸 #络腮胡 #开学"}, {"position": 2, "aweme_id": "7542214041873042747", "title": "这个动漫的特效也太好了，我要吹爆#凡人修仙传"}], "missing_in_tikhub": [{"position": 9, "aweme_id": "7529070747215924538", "title": "今天又老了一岁咯，感觉自己越来越不年轻啦😭#寸头 #生日"}, {"position": 10, "aweme_id": "7527131278434880827", "title": "不听不听，王八念经#寸头 #熊 #络腮胡"}], "count_diff": -20}}, {"author_name": "中国之声", "sec_uid": "MS4wLjABAAAAzrJG9tUZvTE62g9c5nQuszeNzd6AaJXG27pAQyobVyE", "original_api": {"works_count": 40, "top10_ids": ["7541398849986858280", "7541337316313320739", "7541247911976242466", "7540941002761096483", "7540294512975826210", "7540293506863074575", "7540235405900156212", "7540232559809039656", "7539946282840886543", "7539937470725623040"]}, "tikhub_api": {"works_count": 20, "top10_ids": ["7543117428969032960", "7542815829633666339", "7542813425508863267", "7542501927418973440", "7541988576918048000", "7541969406436314408", "7541968215711288628", "7541631941964008744", "7541618236882750720", "7541398849986858280"]}, "differences": {"missing_in_original": [{"position": 1, "aweme_id": "7543117428969032960", "title": "国台办：九三阅兵将邀请台湾同胞出席。"}, {"position": 2, "aweme_id": "7542815829633666339", "title": "对话中国花游队徐汇妍：19岁的夏天，11枚世界大赛奖牌是怎样炼成的？  #决胜时刻 #世界冠军徐汇妍手把手教你练花游（总台中国之声记者 方亮 管月琦）"}, {"position": 3, "aweme_id": "7542813425508863267", "title": "以、意、美接连向法国发难，为何法国成为众矢之的？#新闻深一度"}, {"position": 4, "aweme_id": "7542501927418973440", "title": "电竞俱乐部推出“劝退班”业务：通过还原电竞选手日常的高强度训练，“劝退”那些渴望走上职业电竞道路的青少年。网络游戏和电子竞技有多不一样？家长如何正确疏导孩子？#新闻有观点"}, {"position": 5, "aweme_id": "7541988576918048000", "title": "#南侨机工中的花木兰  1939年，马来西亚华侨姑娘李月美毅然剪去长发，化名“李月眉”，女扮男装加入南侨机工队，成为滇缅公路上最勇敢的司机之一。她驾卡车穿梭于炮火之中抢运物资、抢救伤员，直到因伤身份曝光。她的一生，是千万华侨救亡的缩影。关注中央广播电视总台广播剧《南侨赤子》，重温“当代花木兰”传奇！"}, {"position": 6, "aweme_id": "7541969406436314408", "title": "机遇、合作、福祉……对即将到来的上合天津峰会，为什么来自吉尔吉斯斯坦的关注度已经拉满？#中国之声《大国外交》独家专访  中国驻吉尔吉斯斯坦大使刘江平：这种热切期盼和高度关注，体现了对上合组织的作用、特别是“上海精神”时代内涵的高度认同"}, {"position": 7, "aweme_id": "7541968215711288628", "title": "#大学教授评价这种树又聪明又无私又有英雄气概  我国胡杨林面积占全球的61%，大部分分布在新疆塔里木河流域。塔里木大学生命科学与技术学院教授李志军的很多个暑假都是在胡杨林里度过的，“生而千年不死，死而千年不倒，倒而千年不朽”，倔强身姿挺立沙海的胡杨最让她牵挂。过去十几年里，李志军带着学生跑遍全国64个县市,收集胡杨种质资源4000余份,筛选出665份核心种质，建立起中国胡杨资源数据库和胡杨种质资源圃；突破性地将育苗周期从2至3年缩短至4个月。胡杨守护沙漠，李志军守护胡杨，她说自己是在给塔克拉玛干沙漠“织围脖”，织了很长，还得继续织。#时光里    #媒体精选计划"}, {"position": 8, "aweme_id": "7541631941964008744", "title": "78岁老人被眼镜王蛇咬伤，家属手拎2米“凶手”冲进医院，带蛇就医是“必选项”吗？（总台中国之声记者 谭瑱）"}, {"position": 9, "aweme_id": "7541618236882750720", "title": "#中国的希望在延安   爱国侨领陈嘉庚曾如此断言，他领导南侨总会以财力物力人力全面援助祖国抗战。“人在车在物资在”，1939年至1942年南侨机工累计抢运军需物资45万余吨，超千人牺牲。广播剧《南侨赤子》正在播出，一条AI动画，重温这段不朽传奇！#广播剧南侨赤子"}], "missing_in_tikhub": [{"position": 2, "aweme_id": "7541337316313320739", "title": "#新闻超链接 恐怖悬疑类推理游戏“海龟汤”，会给孩子留下心理阴影吗？"}, {"position": 3, "aweme_id": "7541247911976242466", "title": "#会吹口琴、爱喝咖啡的南洋青年为何回国抗战 1939年，爱国侨领陈嘉庚一声号召，3200多名南洋青年高唱着《告别南洋》，奔赴生死一线的滇缅公路。他们如何铸就传奇？广播剧《南侨赤子》今日开播！"}, {"position": 4, "aweme_id": "7540941002761096483", "title": "这些“育儿补贴”领不得！各地陆续开放育儿补贴申领，近期出现了“育儿补贴”新型诈骗。#新闻深一度 ：如何识破骗局？"}, {"position": 5, "aweme_id": "7540294512975826210", "title": "时隔半年再次造访白宫 泽连斯基为什么换装了？#换装后的泽连斯基跟特朗普谈得怎么样 ？#新闻深一度  为您解答"}, {"position": 6, "aweme_id": "7540293506863074575", "title": "第三个全国生态日，一头野生麋鹿被货车撞死引发的民事公益诉讼案开庭审理！为何总有鹿被撞？如何与野生动物和谐共处？#新闻有观点"}, {"position": 7, "aweme_id": "7540235405900156212", "title": "杭州地铁一名男子穿黑丝披黑斗篷，爬行进入地铁车厢，诡异一幕是为“起号”。平台通报无限期封禁其账号。各种扮丑和网络闹剧何时休？“起号”的逻辑只能靠猎奇了吗？#新闻有观点"}, {"position": 8, "aweme_id": "7540232559809039656", "title": "A股沪指十年新高，市值首破100万亿！北证50单日暴涨6.79%，年内狂飙51%。政策×产业×资金罕见共振，带您深入北交所这一\"新质生产力试验田\"，一探究竟！#A股  #北交所  #新质生产力"}, {"position": 9, "aweme_id": "7539946282840886543", "title": "飞机乘客互相泼水引发“调整座椅自由”的讨论。#新闻新鲜说更细致的规则能否解决“空间”与“人情”之间的矛盾？"}, {"position": 10, "aweme_id": "7539937470725623040", "title": "A股总市值18日首破100万亿，科技成长股领涨。专家表示，市场有望延续强势格局，短期可注意高位回吐震荡压力。"}], "count_diff": -20}}, {"author_name": "厚切章魚燒", "sec_uid": "MS4wLjABAAAAo7UBz1Zhqfr0bI_UCiNq1O2UCDTXTRko-KLRwoygWJo", "original_api": {"works_count": 9, "top10_ids": ["7523681309258026299", "7518664186517327163", "7518363626588523836", "7514194094046989627", "7514176461746703674", "7513416926829251897", "7512752564284591418", "7510462653001813308", "7510117466460376378"]}, "tikhub_api": {"works_count": 9, "top10_ids": ["7523681309258026299", "7518664186517327163", "7518363626588523836", "7514194094046989627", "7514176461746703674", "7513416926829251897", "7512752564284591418", "7510462653001813308", "7510117466460376378"]}, "differences": {"missing_in_original": [], "missing_in_tikhub": [], "count_diff": 0}}, {"author_name": "风安轻", "sec_uid": "MS4wLjABAAAA53E73tjwe2Kx0WB8C78Tpk-IEeZyaoOwIRBMZpLhuGQ", "original_api": {"works_count": 43, "top10_ids": ["7276356615098486050", "7433332941167627577", "7270432265434303759", "7541399180083154233", "7541256917654818105", "7541041581042961722", "7541038698692529468", "7541026205248851259", "7541024731328711993", "7541020381164391740"]}, "tikhub_api": {"works_count": 23, "top10_ids": ["7276356615098486050", "7433332941167627577", "7270432265434303759", "7543593054609149244", "7543422484919733563", "7543255436758240572", "7543165010429136188", "7542889144730799418", "7542844980625362235", "7542718780914847033"]}, "differences": {"missing_in_original": [{"position": 4, "aweme_id": "7543593054609149244", "title": "心中坚信那光明  一路你我共前行"}, {"position": 5, "aweme_id": "7543422484919733563", "title": "叔叔帅"}, {"position": 6, "aweme_id": "7543255436758240572", "title": "风和自由"}, {"position": 7, "aweme_id": "7543165010429136188", "title": "叔叔帅"}, {"position": 8, "aweme_id": "7542889144730799418", "title": "不买的话，可以买好多好吃的，可以买4000斤的米，犹豫了 ，下个月13号就开始抢首发"}, {"position": 9, "aweme_id": "7542844980625362235", "title": "叔叔帅"}, {"position": 10, "aweme_id": "7542718780914847033", "title": "心中坚信那光明  一路你我共前行"}], "missing_in_tikhub": [{"position": 4, "aweme_id": "7541399180083154233", "title": "叔叔帅"}, {"position": 5, "aweme_id": "7541256917654818105", "title": "叔叔帅"}, {"position": 6, "aweme_id": "7541041581042961722", "title": "心中坚信那光明  一路你我共前行"}, {"position": 7, "aweme_id": "7541038698692529468", "title": "叔叔帅"}, {"position": 8, "aweme_id": "7541026205248851259", "title": "叔叔帅"}, {"position": 9, "aweme_id": "7541024731328711993", "title": "#阳光导入"}, {"position": 10, "aweme_id": "7541020381164391740", "title": "叔叔帅"}], "count_diff": -20}}, {"author_name": "火鸡面大王", "sec_uid": "MS4wLjABAAAAPUPHr-YzP8wQRUsEt1osxk7B2QA55J8MGSh06PlS_rw", "original_api": {"works_count": 1, "top10_ids": ["7497176289243270458"]}, "tikhub_api": {"works_count": 1, "top10_ids": ["7497176289243270458"]}, "differences": {"missing_in_original": [], "missing_in_tikhub": [], "count_diff": 0}}, {"author_name": "小钢炮体育", "sec_uid": "MS4wLjABAAAA2T0G0v9iFlqr1Yus4qGy9Q_dfS4gxtFfoN4Tt4iOYTk", "original_api": {"works_count": 8, "top10_ids": ["7515791384276897082", "7539480894047374607", "7518379214149340473", "7517657382613830972", "7471274965292076348", "7420441154845494569", "7364039873273007396", "7362488210829757737"]}, "tikhub_api": {"works_count": 8, "top10_ids": ["7515791384276897082", "7539480894047374607", "7518379214149340473", "7517657382613830972", "7471274965292076348", "7420441154845494569", "7364039873273007396", "7362488210829757737"]}, "differences": {"missing_in_original": [], "missing_in_tikhub": [], "count_diff": 0}}, {"author_name": "川啊", "sec_uid": "MS4wLjABAAAA8OEKSjzR4ZIwIz2_2RbUT9MblqtbJ8QzlTWtLpet65TeAPB9MaAPUQg2tQhrOhX9", "original_api": {"works_count": 26, "top10_ids": ["7540248235963026724", "7538759123337694527", "7537649716394773796", "7536766407042125098", "7536168534423768359", "7534670504281066815", "7532691621906877738", "7532451920901131559", "7529471851015146794", "7529113822398565651"]}, "tikhub_api": {"works_count": 20, "top10_ids": ["7540248235963026724", "7538759123337694527", "7537649716394773796", "7536766407042125098", "7536168534423768359", "7534670504281066815", "7532691621906877738", "7532451920901131559", "7529471851015146794", "7529113822398565651"]}, "differences": {"missing_in_original": [], "missing_in_tikhub": [], "count_diff": -6}}, {"author_name": "东方日出", "sec_uid": "MS4wLjABAAAAufu4s0CFBaXzfSd0Vce8qwIuQ-ULWODSDoniJOawR0I", "original_api": {"works_count": 40, "top10_ids": ["7540957333947632956", "7540202691550514490", "7539405271190588730", "7539101104853585209", "7538233614359694652", "7537154916218654011", "7536486642506206521", "7535684453353393465", "7535002498794687801", "7534610036078382396"]}, "tikhub_api": {"works_count": 20, "top10_ids": ["7543181154859126075", "7542429935958134075", "7541698817252330810", "7540957333947632956", "7540202691550514490", "7539405271190588730", "7539101104853585209", "7538233614359694652", "7537154916218654011", "7536486642506206521"]}, "differences": {"missing_in_original": [{"position": 1, "aweme_id": "7543181154859126075", "title": "应朋友们催更奥特曼来了 #奥特曼 #迪迦 #童年动漫"}, {"position": 2, "aweme_id": "7542429935958134075", "title": "东北菜实力排行2 #美食 #东北菜"}, {"position": 3, "aweme_id": "7541698817252330810", "title": "东北菜实力排行 #东北菜 #锅包肉 #美食"}], "missing_in_tikhub": [{"position": 8, "aweme_id": "7535684453353393465", "title": "火影忍者人品排行 #火影忍者 #鸣人 #佐助 #童年动画"}, {"position": 9, "aweme_id": "7535002498794687801", "title": "雪糕权威排行 #雪糕 #童年味道 #童年冰棍"}, {"position": 10, "aweme_id": "7534610036078382396", "title": "不要带着有色眼镜去看他们 #植物大战僵尸 #童年游戏"}], "count_diff": -20}}, {"author_name": "辣瑞舅舅", "sec_uid": "MS4wLjABAAAAnLOupJ_9cgb7UNvWeRvmQ8M3mO4PbwqtBmdlbM21hA4", "original_api": {"works_count": 43, "top10_ids": ["7486659733743586617", "7517052531437636921", "7477786376545602873", "7541234715312278843", "7541019435059612985", "7540971943924976953", "7540810431611473210", "7540557706475949372", "7540471475284577593", "7539763710618570044"]}, "tikhub_api": {"works_count": 23, "top10_ids": ["7486659733743586617", "7517052531437636921", "7477786376545602873", "7543551705708416314", "7543550765131287865", "7543418414203718972", "7543405601167625531", "7543187398147411257", "7543178024440253754", "7543100293091036476"]}, "differences": {"missing_in_original": [{"position": 4, "aweme_id": "7543551705708416314", "title": "#居家训练 #荷尔蒙 #小肚腩 #控腿"}, {"position": 5, "aweme_id": "7543550765131287865", "title": "#抖音ai创作 #荷尔蒙 #人像写真"}, {"position": 6, "aweme_id": "7543418414203718972", "title": "#居家运动健身 #没有腹肌 #荷尔蒙 #你的叔"}, {"position": 7, "aweme_id": "7543405601167625531", "title": "光影…#健身 #荷尔蒙 #黑白"}, {"position": 8, "aweme_id": "7543187398147411257", "title": "起来做饭了… #荷尔蒙 #小肚腩 #没有腹肌 #毫无训练痕迹"}, {"position": 9, "aweme_id": "7543178024440253754", "title": "这还是很还原的嘛…#暗调朦胧写真  #荷尔蒙 #视觉艺术 #你的叔"}, {"position": 10, "aweme_id": "7543100293091036476", "title": "这？又招财有招桃花，还是个海王！#抖音ai创作 @一念先森 #荷尔蒙 #你的男朋友"}], "missing_in_tikhub": [{"position": 4, "aweme_id": "7541234715312278843", "title": "duangduangduang！ #居家锻炼身体 #居家锻炼 #练胸肌 #没有腹肌"}, {"position": 5, "aweme_id": "7541019435059612985", "title": "你男朋友我还没礼物，赶快下单送我！ #七夕节送礼 #马登硬核好礼 #马登礼盒 #送男友"}, {"position": 6, "aweme_id": "7540971943924976953", "title": "短裤不错！直接下单！ #腿毛 #美式短裤推荐 #小肚小肚 #没有腹肌"}, {"position": 7, "aweme_id": "7540810431611473210", "title": "橱窗有货！请随意… #居家训练 #没有腹肌 #荷尔蒙 #小肚腩 #毫无训练痕迹"}, {"position": 8, "aweme_id": "7540557706475949372", "title": "各位去我橱窗看看吧，随便买别客气！hhhh #脂包肌选手 #没有腹肌 #荷尔蒙 #毫无训练痕迹"}, {"position": 9, "aweme_id": "7540471475284577593", "title": "#居家锻练 #家中健身 #没有腹肌"}, {"position": 10, "aweme_id": "7539763710618570044", "title": "#居家运动健身 #荷尔蒙"}], "count_diff": -20}}, {"author_name": "咿迦花花", "sec_uid": "MS4wLjABAAAAfc7NhG9gSURZlI7GyZF0EkAUxkNNrnuAFoSInZvN01w", "original_api": {"works_count": 42, "top10_ids": ["7480130780459666714", "7508332318442016052", "7541314623065656610", "7541215408352087348", "7540844845403180288", "7540474754101939490", "7540104445629435176", "7539732224716721443", "7539371439930985780", "7539003483695205632"]}, "tikhub_api": {"works_count": 22, "top10_ids": ["7480130780459666714", "7508332318442016052", "7543444063186718004", "7543075014188092712", "7542790392158473487", "7542702355201887522", "7542329645297143075", "7541967624240123188", "7541591183706606883", "7541314623065656610"]}, "differences": {"missing_in_original": [{"position": 3, "aweme_id": "7543444063186718004", "title": "所有的偏爱｜都会为你而来"}, {"position": 4, "aweme_id": "7543075014188092712", "title": "好看的花花当然要和你一起欣赏🌼🌸"}, {"position": 5, "aweme_id": "7542790392158473487", "title": "风💨里藏笑🌼🍬｜🎑夜中💫生光"}, {"position": 6, "aweme_id": "7542702355201887522", "title": "我会一直陪在你身边｜你可以反复向我确认"}, {"position": 7, "aweme_id": "7542329645297143075", "title": "累了就向后倒｜我永远是你的四面八方"}, {"position": 8, "aweme_id": "7541967624240123188", "title": "世界太吵，别听，别看，别管，朝我走"}, {"position": 9, "aweme_id": "7541591183706606883", "title": "你的每一次笑｜都是我不可多得的解药"}], "missing_in_tikhub": [{"position": 4, "aweme_id": "7541215408352087348", "title": "和别人谈起你｜是我想你的方式"}, {"position": 5, "aweme_id": "7540844845403180288", "title": "小小的世界｜有你在我身边就够了"}, {"position": 6, "aweme_id": "7540474754101939490", "title": "小小世界｜有你在我身边就够了"}, {"position": 7, "aweme_id": "7540104445629435176", "title": "所有可以分享的瞬间｜我想到的都是你"}, {"position": 8, "aweme_id": "7539732224716721443", "title": "恰到好处的不经意｜是我蓄谋已久的偶然。"}, {"position": 9, "aweme_id": "7539371439930985780", "title": "好看的花花｜一定要分享给最爱的宝宝"}, {"position": 10, "aweme_id": "7539003483695205632", "title": "十里山河不如你｜万般野心只为你"}], "count_diff": -20}}, {"author_name": "陈陈FM", "sec_uid": "MS4wLjABAAAAV1nmXY1OD2GtWOXBHmD028oQmisWGGWKfTsLzK2-q-ZVBzM-tNBtkhADLHEl1Jjx", "original_api": {"works_count": 2, "top10_ids": ["7526605311596334386", "7517768653661932837"]}, "tikhub_api": {"works_count": 2, "top10_ids": ["7526605311596334386", "7517768653661932837"]}, "differences": {"missing_in_original": [], "missing_in_tikhub": [], "count_diff": 0}}, {"author_name": "狮子狮子", "sec_uid": "MS4wLjABAAAA1sOIo-uIG5YXgA0v2YgZuMAblL-4hfCa7JmjFuOVBz0aJJVbjqZArzQgXvKlYZSQ", "original_api": {"works_count": 40, "top10_ids": ["7527980455116836123", "7518361630367993115", "7541360982758477094", "7540594184361610547", "7539554893310577929", "7539106696507264266", "7538736227281882406", "7537638285527895323", "7537252451506883890", "7536881813395197193"]}, "tikhub_api": {"works_count": 22, "top10_ids": ["7527980455116836123", "7518361630367993115", "7543196082655841546", "7542878040097950986", "7542078376754171187", "7541687743899045129", "7541360982758477094", "7540594184361610547", "7539554893310577929", "7539106696507264266"]}, "differences": {"missing_in_original": [{"position": 3, "aweme_id": "7543196082655841546", "title": "这个人怎么这样啊，以后不来了😡#狮子水电工 #失败的man #精神状态belike #离谱剧情 #看到最后"}, {"position": 4, "aweme_id": "7542878040097950986", "title": "呃被发现了😰#狮子水电工 #熊 #精神状态belike #礼物 #一定要看到最后"}, {"position": 5, "aweme_id": "7542078376754171187", "title": "唯一接受的闭嘴方式#狮子水电工 #练功券 #正能量 #无不良引导 #接"}, {"position": 6, "aweme_id": "7541687743899045129", "title": "好奇怪到底是什么？#狮子快递员 #一定要看到最后 #熊 #快递 #猎奇"}], "missing_in_tikhub": [{"position": 7, "aweme_id": "7538736227281882406", "title": "到底多久才算久？#狮子水电工 #熊 #白袜 #锻炼 #精神状态belike"}, {"position": 8, "aweme_id": "7537638285527895323", "title": "2015-2025领子快要把我勒四了#十年前vs十年后 #空少 #制服 #熊 #正装"}, {"position": 9, "aweme_id": "7537252451506883890", "title": "有这样的机器人你会几点回家🥵#狮子水电工 #熊 #机器人 #指令 #万万没想到"}, {"position": 10, "aweme_id": "7536881813395197193", "title": "能接受我的暗恋吗#狮子水电工 #英语教学 #英语口语 #熊 #暗恋"}], "count_diff": -18}}, {"author_name": "大岳dy", "sec_uid": "MS4wLjABAAAAcWIaLhPbIviuX9BTFN-U5EoxmAT0Y78VDTZQNGa_5cw", "original_api": {"works_count": 40, "top10_ids": ["7522695191704358170", "7522410236196425010", "7520937882766347566", "7520585287665192201", "7513893526556560650", "7510179701114850610", "7500076257671417151", "7431492959327014183", "7392901226892987699", "7355753645536726281"]}, "tikhub_api": {"works_count": 20, "top10_ids": ["7522695191704358170", "7522410236196425010", "7520937882766347566", "7520585287665192201", "7513893526556560650", "7510179701114850610", "7500076257671417151", "7431492959327014183", "7392901226892987699", "7355753645536726281"]}, "differences": {"missing_in_original": [], "missing_in_tikhub": [], "count_diff": -20}}, {"author_name": "小虎Edgar(Hunk版)", "sec_uid": "MS4wLjABAAAAPYBVNqW2OMw9OSrZ-kKGOaL0FiOWJtzEG5CamYiamSmw23GjWoziXQ-bcnJQgAoR", "original_api": {"works_count": 41, "top10_ids": ["7495333159337561378", "7466630993709960500", "7398023456815582501", "7540605587294997760", "7539492537531796736", "7538008256217369896", "7536920270889159971", "7535410618195889408", "7534673244792769827", "7531648919500541236"]}, "tikhub_api": {"works_count": 23, "top10_ids": ["7495333159337561378", "7466630993709960500", "7398023456815582501", "7543203433491762447", "7542840193108856099", "7540605587294997760", "7539492537531796736", "7538008256217369896", "7536920270889159971", "7535410618195889408"]}, "differences": {"missing_in_original": [{"position": 4, "aweme_id": "7543203433491762447", "title": "玩水 #肌肉男 #泳池 #夏日氛围感 #有肉感的胖子"}, {"position": 5, "aweme_id": "7542840193108856099", "title": "泳池里的花 #泳池 #脂包肌 #荷尔蒙"}], "missing_in_tikhub": [{"position": 9, "aweme_id": "7534673244792769827", "title": "海边和晚霞都在了，你呢？ #海边"}, {"position": 10, "aweme_id": "7531648919500541236", "title": "#胸肌 #脂包肌 #肉壮"}], "count_diff": -18}}, {"author_name": "口罩哥研报60秒", "sec_uid": "MS4wLjABAAAAnKeRN8QUgooS1pPRqOf_N_jnuztzUyocl0_vUndQFJs", "original_api": {"works_count": 40, "top10_ids": ["7541424640625069312", "7541381195159719202", "7541377989969644800", "7541307557216144675", "7541294913402408192", "7541286498776354088", "7541277081604508962", "7541061170478042402", "7540933500724579599", "7540919236429679907"]}, "tikhub_api": {"works_count": 20, "top10_ids": ["7543603865871289640", "7543591639508667700", "7543587044199910708", "7543526033141501219", "7543236545132023055", "7543198548180978984", "7543157117033860404", "7543154354082991412", "7542820977202400546", "7542782322765204788"]}, "differences": {"missing_in_original": [{"position": 1, "aweme_id": "7543603865871289640", "title": "汇金加仓白酒的原因越传越离谱，还是理性点吧。"}, {"position": 2, "aweme_id": "7543591639508667700", "title": "全体科技疯狂时，汇金加了白酒。但要知道，汇金的科技持仓是巨鲸级别，只不过白酒这个细节值得关注。"}, {"position": 3, "aweme_id": "7543587044199910708", "title": "A神越来越猛，美股却开始担心日元加息了。"}, {"position": 4, "aweme_id": "7543526033141501219", "title": "团子太不容易了， PK 外卖业务的正是今天发布的民营 500 强TOP2……"}, {"position": 5, "aweme_id": "7543236545132023055", "title": "团团要取消罚款了，这次财报是真 nasty 了。"}, {"position": 6, "aweme_id": "7543198548180978984", "title": "有些小伙伴要慌了，但是先别慌，马上就要卫星了。"}, {"position": 7, "aweme_id": "7543157117033860404", "title": "牛了这次的老印子50%关税没有推迟正式开启了…..为什么这次没有taco…."}, {"position": 8, "aweme_id": "7543154354082991412", "title": "创业板 3%现在抹掉了…."}, {"position": 9, "aweme_id": "7542820977202400546", "title": "人工智能+的正式印发来了，六大方向10 万亿产业规模的战略布局。"}, {"position": 10, "aweme_id": "7542782322765204788", "title": "韩国老铁刚过去白房子听到一句话：你家那块我们基地的土地就给我们吧….."}], "missing_in_tikhub": [{"position": 1, "aweme_id": "7541424640625069312", "title": "薇子大发慈悲了，最后时刻歌声嘹亮，甚至发明说法 new normal 新常态来应对后续的降息，9 月概率飙升到 91%。"}, {"position": 2, "aweme_id": "7541381195159719202", "title": "mlf 6000"}, {"position": 3, "aweme_id": "7541377989969644800", "title": "牛了！玛特直接纳入恒指。"}, {"position": 4, "aweme_id": "7541307557216144675", "title": "H20 通知停产和韩王再次 20 厘米，代表着一个交替。"}, {"position": 5, "aweme_id": "7541294913402408192", "title": "ETF 的重要趋势，总量突破，宽基切换主题。"}, {"position": 6, "aweme_id": "7541286498776354088", "title": "90 万亿存款怎么搬？中信测算了，今年先来个 4.5~9 万亿固收加为主，后续还能更刺激。"}, {"position": 7, "aweme_id": "7541277081604508962", "title": "疯狂了 3800，接下来就是存款搬家。"}, {"position": 8, "aweme_id": "7541061170478042402", "title": "美联储反复强调不支持降息，华尔街一厢情愿相信肯定会降。马克说了 9 月不降，不然让他们坐下聊聊。"}, {"position": 9, "aweme_id": "7540933500724579599", "title": "Deepseek V3.1正式发布，上演思考快与慢。但就问一句，这 V3 .1不能是 R2 吧"}, {"position": 10, "aweme_id": "7540919236429679907", "title": "老印子这回是站起来了，直接强调肯定继续买油。"}], "count_diff": -20}}, {"author_name": "小鲨鱼🐟🐟", "sec_uid": "MS4wLjABAAAA9ogjg64Jp71BSbTSAtIwHj1f21YVUJRRPNdjeK2_E54lNCOuqzYlCgOv7HdUTdDl", "original_api": {"works_count": 39, "top10_ids": ["7541211236497050907", "7540026231569354011", "7536205245686746419", "7535453927404457254", "7532637586692754738", "7529644452022242611", "7529110567468354843", "7527969962313501979", "7525051406862077230", "7523169421288885530"]}, "tikhub_api": {"works_count": 20, "top10_ids": ["7541211236497050907", "7540026231569354011", "7536205245686746419", "7535453927404457254", "7532637586692754738", "7529644452022242611", "7529110567468354843", "7527969962313501979", "7525051406862077230", "7523169421288885530"]}, "differences": {"missing_in_original": [], "missing_in_tikhub": [], "count_diff": -19}}, {"author_name": "冰糖", "sec_uid": "MS4wLjABAAAA3J4CBPa2nnIJ1RNbO0yKdCvYax14T_iiFHh3JUY9bPQ", "original_api": {"works_count": 41, "top10_ids": ["7138708665858641183", "7537916134592417075", "7537485579933912371", "7537146446333578546", "7536878523748355338", "7528889094550654254", "7528347594792946971", "7526427602896391433", "7525015097787780403", "7524295773669100838"]}, "tikhub_api": {"works_count": 21, "top10_ids": ["7138708665858641183", "7543213106522115374", "7537916134592417075", "7537485579933912371", "7537146446333578546", "7536878523748355338", "7528889094550654254", "7528347594792946971", "7526427602896391433", "7525015097787780403"]}, "differences": {"missing_in_original": [{"position": 2, "aweme_id": "7543213106522115374", "title": "你喜欢的人，长什么样？我康康！"}], "missing_in_tikhub": [{"position": 10, "aweme_id": "7524295773669100838", "title": "周一的牛马，你下班了吗？"}], "count_diff": -20}}, {"author_name": "john-", "sec_uid": "MS4wLjABAAAA6IFVMOaLTcDwKUMuInQeOOyGF8o3Ci8iNIPoSDd5wDFQIk6B_JWV_ks45_PEm033", "original_api": {"works_count": 7, "top10_ids": ["7396948767930371337", "7420970065551576335", "7409593192498105635", "7541397994760801596", "7538256847821081914", "7537277705885306112", "7450359986498080034"]}, "tikhub_api": {"works_count": 7, "top10_ids": ["7396948767930371337", "7420970065551576335", "7409593192498105635", "7541397994760801596", "7538256847821081914", "7537277705885306112", "7450359986498080034"]}, "differences": {"missing_in_original": [], "missing_in_tikhub": [], "count_diff": 0}}, {"author_name": "天才小土豆", "sec_uid": "MS4wLjABAAAAMQ-9iK1MRuaGtcquP1YiqtaZ01Fdbc2wSORy8NWIf-1ekAtPpbdLmj6RyO2j_4W6", "original_api": {"works_count": 40, "top10_ids": ["7525794006316813627", "7515967496161414459", "7502633991771966778", "7541411890376609082", "7540821711950220602", "7540669131097918778", "7539390798514261307", "7537634603012902201", "7536579428848471356", "7535081500762066234"]}, "tikhub_api": {"works_count": 23, "top10_ids": ["7525794006316813627", "7515967496161414459", "7502633991771966778", "7543638088367852859", "7542858097569844537", "7542505168324627772", "7541738282196061500", "7541411890376609082", "7540821711950220602", "7540669131097918778"]}, "differences": {"missing_in_original": [{"position": 4, "aweme_id": "7543638088367852859", "title": "要不是我怕输了丢脸，我就动手打你了……#健身减肥 #毫无训练痕迹 #卧推 #健身房日常 #荷尔蒙"}, {"position": 5, "aweme_id": "7542858097569844537", "title": "阿中，别藏了，看见你了，尾巴都露出来了……\n#健身干货 #健身减肥 #腰腹训练 #手倒立"}, {"position": 6, "aweme_id": "7542505168324627772", "title": "今日体重88.8kg，卧推11.32吨……\n#自律的顶端就是享受孤独 #健身逆袭 #健身减肥 #腹肌马甲线"}, {"position": 7, "aweme_id": "7541738282196061500", "title": "一天睡了两次午觉……起来拉伸一下\n#坐角式 #一字马 #风吹麦浪慢生活 #打开美好生活 #日常分享"}], "missing_in_tikhub": [{"position": 7, "aweme_id": "7539390798514261307", "title": "下周积分赛目标，赢一个场……\n#居家健身 #乒乓球"}, {"position": 8, "aweme_id": "7537634603012902201", "title": "第二次完成70公斤200个，喜欢这种简单到极致的计划。\n#健身 #练胸日 #健身光速成长计划 #健身减肥 #胸肌训练"}, {"position": 9, "aweme_id": "7536579428848471356", "title": "错过了落日余晖，还可以期待满天繁星……\n#秀出我的硬核实力 #健身爱好者 #街头健身 #倒立练习 #健身减肥"}, {"position": 10, "aweme_id": "7535081500762066234", "title": "谁能有我菜……\n#兴趣爱好 #生命在于运动一起锻炼吧 #快乐乒乓球🏓 #秀出我的硬核实力 #就是因为热爱"}], "count_diff": -17}}, {"author_name": "法拉贺", "sec_uid": "MS4wLjABAAAAHJ7GJgO9O2XuCeTdsSb56ApGCfnKfNnazSixD54gPwo", "original_api": {"works_count": 43, "top10_ids": ["7292723760170323209", "7219972306842471683", "7214772734125935910", "7540648513131121980", "7536178581497236796", "7534703042018528571", "7533220273509485882", "7531739649468976444", "7530262906412387644", "7528773320851459385"]}, "tikhub_api": {"works_count": 23, "top10_ids": ["7292723760170323209", "7219972306842471683", "7214772734125935910", "7542117054243261756", "7540648513131121980", "7536178581497236796", "7534703042018528571", "7533220273509485882", "7531739649468976444", "7530262906412387644"]}, "differences": {"missing_in_original": [{"position": 4, "aweme_id": "7542117054243261756", "title": "“走的慢没关系方向对就不怕远”#跑步🏃 #励志 @小宝在这"}], "missing_in_tikhub": [{"position": 10, "aweme_id": "7528773320851459385", "title": "跑完10公里奖励自己一桶红烧牛肉面！🍜#跑步 @小宝在这"}], "count_diff": -20}}, {"author_name": "阅氿湖", "sec_uid": "MS4wLjABAAAA263TgskHGfc4mPRO_m29OXx4ol9jNxVUfRLHyHrDrpPPZE7hMJdmyQXRdIsJP8b-", "original_api": {"works_count": 42, "top10_ids": ["7502117207872687379", "7502385673338686732", "7502277240794369291", "7540887716525165882", "7540990335346855225", "7540521664006589753", "7539106870011727164", "7538754619381075257", "7538649787647413563", "7538617298221141305"]}, "tikhub_api": {"works_count": 22, "top10_ids": ["7502117207872687379", "7502385673338686732", "7502277240794369291", "7543625097488682299", "7543430127511309625", "7543410724032613690", "7543178261875690811", "7543090182032756028", "7541532298072608057", "7540887716525165882"]}, "differences": {"missing_in_original": [{"position": 4, "aweme_id": "7543625097488682299", "title": "美国煽动格陵兰岛公民协助特朗普政府夺岛 #国际新闻#国际局势#美国#财经#特朗普"}, {"position": 5, "aweme_id": "7543430127511309625", "title": "055新型号内置起重机，美媒惊呼海上无限火力 #国际局势#国际新闻#军事科普#军事科普 #财经"}, {"position": 6, "aweme_id": "7543410724032613690", "title": "美国政府正考虑收购洛克希德·马丁股权！美国企业开启全面国有化 #国际新闻#国际局势#美国#财经#金融"}, {"position": 7, "aweme_id": "7543178261875690811", "title": "德国:莫迪四次拒绝特朗普电话！印度全国反美情绪高涨 #国际新闻 #国际局势 #印度 #美国 #财经"}, {"position": 8, "aweme_id": "7543090182032756028", "title": "德国官宣比亚迪472km/h极速刷新世界记录！加速完爆布加迪 #国际新闻#国际局势#汽车#比亚迪#科技"}, {"position": 9, "aweme_id": "7541532298072608057", "title": "美国政府收购10%英特尔股份，开启大政府资本主义模式 #国际新闻#国际局势#财经#美国#金融"}], "missing_in_tikhub": [{"position": 5, "aweme_id": "7540990335346855225", "title": "中国新型高速直升机亮相！共轴旋翼系统+推进式螺旋桨 #国际新闻#国际局势#军事科普#军事科技#财经"}, {"position": 6, "aweme_id": "7540521664006589753", "title": "美国财长贝森特猛批印度狂发战争财 #国际新闻#国际局势#印度#财经#美国"}, {"position": 7, "aweme_id": "7539106870011727164", "title": "上海官宣建造核动力舰船！江苏和广东提前庆祝？ #军事科普#军事科技#江苏#广东#核动力航母"}, {"position": 8, "aweme_id": "7538754619381075257", "title": "原来雾化热水器是家里隐藏的健康刺客 #避雷 #雾化热水器黑幕真相 #热水器劝你选0雾化0酸雾0酸水 #燃气热水器#健康"}, {"position": 9, "aweme_id": "7538649787647413563", "title": "路透社:特朗普用关税威胁挪威给他颁发诺贝尔和平奖 #国际新闻#国际局势#美国#特朗普#财经"}, {"position": 10, "aweme_id": "7538617298221141305", "title": "马斯克猛夸中国时速1,000公里高铁！上海到杭州只需要9分钟 #国际新闻#国际局势#财经#科技"}], "count_diff": -20}}, {"author_name": "奔跑吧鑫", "sec_uid": "MS4wLjABAAAADa-s8YrYMpfT-pHCpAmx0aV0dhmdTjLKof5hv_POY_w", "original_api": {"works_count": 41, "top10_ids": ["7520777623855222073", "7498338533528358203", "7541181099457547580", "7540999089920855354", "7540437282373831994", "7539357014875802938", "7538051846100979003", "7538049008822259004", "7537468820747783483", "7535978478907575611"]}, "tikhub_api": {"works_count": 22, "top10_ids": ["7520777623855222073", "7498338533528358203", "7543424651050257721", "7543218889137999163", "7541954962163518780", "7541592542342368569", "7541181099457547580", "7540999089920855354", "7540437282373831994", "7539357014875802938"]}, "differences": {"missing_in_original": [{"position": 3, "aweme_id": "7543424651050257721", "title": "战胜自己#健身 #户外运动 #跑步运动 #自律改变生活 #你的坚持终将美好"}, {"position": 4, "aweme_id": "7543218889137999163", "title": "这几天一直下雨室内增肌的一天#上热搜 #增肌 #运动 #纪录美好生活"}, {"position": 5, "aweme_id": "7541954962163518780", "title": "换个路线换个心情，跑到山水画里了。此时此刻的心情无法表达😊解压舒畅#生命在于运动一起锻炼吧 #跑步治愈一切 #坚持跑步🏃"}, {"position": 6, "aweme_id": "7541592542342368569", "title": "行走中国#户外运动爱好者 #山高人为峰 #户外登山 #我为家乡代言"}], "missing_in_tikhub": [{"position": 7, "aweme_id": "7538051846100979003", "title": "秋天的凉爽。夜间的5公里#运动跑步 #散散步吹吹风透透气 #跑步🏃🏻 #户外跑步"}, {"position": 8, "aweme_id": "7538049008822259004", "title": "秋天的凉爽，夜晚的5公里#运动跑步 #户外跑步 #跑步运动 #身强力壮才是硬道理"}, {"position": 9, "aweme_id": "7537468820747783483", "title": "上班路上跑步，完美一天#生命在于运动一起锻炼吧 #生命不息运动不止 #跑步人的快乐"}, {"position": 10, "aweme_id": "7535978478907575611", "title": "今天，你打算让身体沉睡，还是让它在奔跑中醒来？选择权在你脚下。#坚持跑步🏃 #生命在于运动一起锻炼吧 #晨跑打卡 #出汗的感觉真好"}], "count_diff": -19}}, {"author_name": "星晨♏️大海", "sec_uid": "MS4wLjABAAAA9a6Ubyk3cxQ9YRxp44l8JAYal94puGSNgJ6uaEOX-Cw", "original_api": {"works_count": 42, "top10_ids": ["7182201661639691531", "6999586554079415584", "6995169866575645991", "7541436161279233323", "7541371510863220022", "7541191287246458175", "7541188122265242934", "7540925797130358070", "7540669651811192083", "7540542056361495847"]}, "tikhub_api": {"works_count": 23, "top10_ids": ["7182201661639691531", "6999586554079415584", "6995169866575645991", "7543609771032890663", "7543403064221502763", "7543252487265209641", "7543190670888308010", "7543035924968672555", "7542880640288492854", "7542504884563840275"]}, "differences": {"missing_in_original": [{"position": 4, "aweme_id": "7543609771032890663", "title": "#无处安放的大长腿 #小肚腩"}, {"position": 5, "aweme_id": "7543403064221502763", "title": "#抖音ai创作 #壮"}, {"position": 6, "aweme_id": "7543252487265209641", "title": "#抖音ai创作 #毛熊 #壮 #男士造型"}, {"position": 7, "aweme_id": "7543190670888308010", "title": "#AI暗冷奇幻大片 #事事如意就不叫生活了 #40岁男人 #成熟大叔"}, {"position": 8, "aweme_id": "7543035924968672555", "title": "#庭院果树 #大丰收 #我的乡村生活 #龙眼树 #"}, {"position": 9, "aweme_id": "7542880640288492854", "title": "#刮痧 #拔罐 #排寒祛湿"}, {"position": 10, "aweme_id": "7542504884563840275", "title": "#有肉感的胖子 #大肚子"}], "missing_in_tikhub": [{"position": 4, "aweme_id": "7541436161279233323", "title": "#AI雨夜失意照 #抖音ai创作 在也不会，去相信那些乱七八糟无聊话题"}, {"position": 5, "aweme_id": "7541371510863220022", "title": "#40岁男人 #壮熊 #不一样的感觉"}, {"position": 6, "aweme_id": "7541191287246458175", "title": "#滄桑大叔 #成熟大叔 #深情老男人沧桑大叔"}, {"position": 7, "aweme_id": "7541188122265242934", "title": "#抖音ai创作"}, {"position": 8, "aweme_id": "7540925797130358070", "title": "#抖音ai创作"}, {"position": 9, "aweme_id": "7540669651811192083", "title": "#抖音ai创作"}, {"position": 10, "aweme_id": "7540542056361495847", "title": "#大肚子 #啤酒肚"}], "count_diff": -19}}, {"author_name": "七侠镇同福影城", "sec_uid": "MS4wLjABAAAAQp5lTtlt9Q6GEhOxVGukwxVZXz91UqcYMnuybMtCeNg7I5wDNZwH-iTw-qmUY1wa", "original_api": {"works_count": 42, "top10_ids": ["7533307368214449442", "7524214756656319796", "7541143882080242970", "7541143151579352346", "7541142738364861746", "7541142282322414899", "7541141681802906890", "7541141217946504475", "7541140629221379366", "7541139996774796595"]}, "tikhub_api": {"works_count": 22, "top10_ids": ["7533307368214449442", "7524214756656319796", "7543392159441980724", "7543391708898184488", "7543391460712860968", "7543390518802189602", "7543390094913326336", "7543389302256864527", "7543388744133512463", "7543388315169459508"]}, "differences": {"missing_in_original": [{"position": 3, "aweme_id": "7543392159441980724", "title": "武林外传(高清版) 66-19 #武林外传@DOU+小助手"}, {"position": 4, "aweme_id": "7543391708898184488", "title": "武林外传(高清版) 66-18 #武林外传@DOU+小助手"}, {"position": 5, "aweme_id": "7543391460712860968", "title": "武林外传(高清版) 66-17 #武林外传@DOU+小助手"}, {"position": 6, "aweme_id": "7543390518802189602", "title": "武林外传(高清版) 66-16 #武林外传@DOU+小助手"}, {"position": 7, "aweme_id": "7543390094913326336", "title": "武林外传(高清版) 66-15 #武林外传@DOU+小助手"}, {"position": 8, "aweme_id": "7543389302256864527", "title": "武林外传(高清版) 66-14 #武林外传@DOU+小助手"}, {"position": 9, "aweme_id": "7543388744133512463", "title": "武林外传(高清版) 66-13 #武林外传@DOU+小助手"}, {"position": 10, "aweme_id": "7543388315169459508", "title": "武林外传(高清版) 66-12 #武林外传@DOU+小助手"}], "missing_in_tikhub": [{"position": 3, "aweme_id": "7541143882080242970", "title": "武林外传(高清版) 43-18 #武林外传@DOU+小助手"}, {"position": 4, "aweme_id": "7541143151579352346", "title": "武林外传(高清版) 43-17 #武林外传@DOU+小助手"}, {"position": 5, "aweme_id": "7541142738364861746", "title": "武林外传(高清版) 43-16 #武林外传@DOU+小助手"}, {"position": 6, "aweme_id": "7541142282322414899", "title": "武林外传(高清版) 43-15 #武林外传@DOU+小助手"}, {"position": 7, "aweme_id": "7541141681802906890", "title": "武林外传(高清版) 43-14 #武林外传@DOU+小助手"}, {"position": 8, "aweme_id": "7541141217946504475", "title": "武林外传(高清版) 43-13 #武林外传@DOU+小助手"}, {"position": 9, "aweme_id": "7541140629221379366", "title": "武林外传(高清版) 43-12 #武林外传@DOU+小助手"}, {"position": 10, "aweme_id": "7541139996774796595", "title": "武林外传(高清版) 43-11 #武林外传@DOU+小助手"}], "count_diff": -20}}, {"author_name": "叨叨傅", "sec_uid": "MS4wLjABAAAA9QwW62-yIwSKFysbfKte0sezQCi6SxjWplG65mke3qQ", "original_api": {"works_count": 41, "top10_ids": ["7539870097483271463", "7019207912253934852", "7538247623850380585", "7539431582072737065", "7538824405486144809", "7536859162346425643", "7535406461950643497", "7534628891815644452", "7534217018267864363", "7533590839688596790"]}, "tikhub_api": {"works_count": 21, "top10_ids": ["7539870097483271463", "7019207912253934852", "7538247623850380585", "7542141396842466579", "7539431582072737065", "7538824405486144809", "7536859162346425643", "7535406461950643497", "7534628891815644452", "7534217018267864363"]}, "differences": {"missing_in_original": [{"position": 4, "aweme_id": "7542141396842466579", "title": "听说两京联手了？ #苏超"}], "missing_in_tikhub": [{"position": 10, "aweme_id": "7533590839688596790", "title": "不知道我能不能顶得住！ #苏超"}], "count_diff": -20}}, {"author_name": "偷走你所有内裤🇨🇳", "sec_uid": "MS4wLjABAAAAxfndGaZGFGNjyxQe3JkS7kD9QaqibmndGcebg5-GjLiYX2srySlcW28ce9PCU_ag", "original_api": {"works_count": 43, "top10_ids": ["7517128667831897384", "7502601445016833320", "7501497983604739362", "7541287637253688591", "7541023642089000226", "7540871812172844322", "7540736336593734927", "7540689321117928738", "7540580153501764916", "7540452707435433250"]}, "tikhub_api": {"works_count": 23, "top10_ids": ["7517128667831897384", "7502601445016833320", "7501497983604739362", "7543505811415797044", "7543183181273959715", "7543043246210485504", "7542885623587179811", "7542837488722267444", "7542800128550997263", "7542706505126513960"]}, "differences": {"missing_in_original": [{"position": 4, "aweme_id": "7543505811415797044", "title": "速来报名"}, {"position": 5, "aweme_id": "7543183181273959715", "title": "#外卖小哥 #第一视角 你们要视频，久违了"}, {"position": 6, "aweme_id": "7543043246210485504", "title": "#小叔叔"}, {"position": 7, "aweme_id": "7542885623587179811", "title": "不就是希望我锻炼身体吗，来呀"}, {"position": 8, "aweme_id": "7542837488722267444", "title": "回复 @惊蛰的评论 露一手就露一手"}, {"position": 9, "aweme_id": "7542800128550997263", "title": "有被背刺到，汗流浃背咯#小叔叔 #圆寸"}, {"position": 10, "aweme_id": "7542706505126513960", "title": "#黑皮 跳完我就跑 送完我就跑"}], "missing_in_tikhub": [{"position": 4, "aweme_id": "7541287637253688591", "title": "我一生行善积德，这是我应得的#内容太过真实 #外卖小哥"}, {"position": 5, "aweme_id": "7541023642089000226", "title": "？？？？"}, {"position": 6, "aweme_id": "7540871812172844322", "title": "连休息都有愧疚感"}, {"position": 7, "aweme_id": "7540736336593734927", "title": "不想了，睡觉，过去的就过去了，往前看就好了"}, {"position": 8, "aweme_id": "7540689321117928738", "title": "我想我会更坚强，会更独立，会更丰盈，会有得更远"}, {"position": 9, "aweme_id": "7540580153501764916", "title": "请举例说明是谁"}, {"position": 10, "aweme_id": "7540452707435433250", "title": "我可以混🐷圈吗🤔算什么级别的嘎#直男#已婚 #小叔叔 #寸头 #络腮胡"}], "count_diff": -20}}, {"author_name": "陈多福", "sec_uid": "MS4wLjABAAAA7dPP_Uxzlc3yK4VluJBR6JaNxe7EI988pkHsmmciWBs", "original_api": {"works_count": 43, "top10_ids": ["7423226833618881819", "7428833052912127271", "7163873646786727176", "7541428143975320871", "7541012541430861095", "7538481883627097380", "7537165811988942123", "7536529192549682473", "7536458967170649398", "7535434988712660266"]}, "tikhub_api": {"works_count": 23, "top10_ids": ["7423226833618881819", "7428833052912127271", "7163873646786727176", "7541875963534036263", "7541817472979717395", "7541428143975320871", "7541012541430861095", "7538481883627097380", "7537165811988942123", "7536529192549682473"]}, "differences": {"missing_in_original": [{"position": 4, "aweme_id": "7541875963534036263", "title": "后边两个人非要逼我拍这个 #壮熊 #熊熊 #鸳鸯戏"}, {"position": 5, "aweme_id": "7541817472979717395", "title": "鸳鸯戏 #胖熊 #鸳鸯戏"}], "missing_in_tikhub": [{"position": 9, "aweme_id": "7536458967170649398", "title": "这菌子闻着好上头 #菌子 #熊熊"}, {"position": 10, "aweme_id": "7535434988712660266", "title": "德克士很好吃，那你呢？"}], "count_diff": -20}}, {"author_name": "只橙🍊", "sec_uid": "MS4wLjABAAAAEs4KWXo7eg4eFjGv-OCbi3p0e_B2SJVVogxm0FcoHNIe8bLhWYf9NH57GMlsNSvQ", "original_api": {"works_count": 13, "top10_ids": ["7541198542007176507", "7540669077531315515", "7539494178427112761", "7538590451248123195", "7538065839695039801", "7536890094385974588", "7533945779452022073", "7532879992222780730", "7528483711189863738", "7457396086156676410"]}, "tikhub_api": {"works_count": 12, "top10_ids": ["7543684696413736251", "7542760038122982714", "7541728573163048251", "7541198542007176507", "7540669077531315515", "7539494178427112761", "7538590451248123195", "7538065839695039801", "7536890094385974588", "7533945779452022073"]}, "differences": {"missing_in_original": [{"position": 1, "aweme_id": "7543684696413736251", "title": "好不甘心"}, {"position": 2, "aweme_id": "7542760038122982714", "title": "为啥没有眼睛。#抖音ai创作"}, {"position": 3, "aweme_id": "7541728573163048251", "title": "疯了#夕阳"}], "missing_in_tikhub": [{"position": 8, "aweme_id": "7532879992222780730", "title": "拉布拉布"}, {"position": 9, "aweme_id": "7528483711189863738", "title": "合肥的公司招聘啦 地址大东门交通便利，环境舒适，福利多多，职等你来！！！\n现招聘电销、运营。来个饭搭子🌝"}, {"position": 10, "aweme_id": "7457396086156676410", "title": "以前只知道想要的东西都有价码，现在发现可能不想要的代价更大"}], "count_diff": -1}}, {"author_name": "哈维", "sec_uid": "MS4wLjABAAAANnCDa82iQcvebdAe2ww2trc9BhLDMBYohFc3HMPebqg", "original_api": {"works_count": 40, "top10_ids": ["7526792693910883643", "7520171284350373177", "7505421368810835210", "7540669161959312698", "7539281439025876284", "7535752686593330492", "7534384643006090556", "7532041405855649082", "7528873734003281211", "7528849349107879226"]}, "tikhub_api": {"works_count": 21, "top10_ids": ["7526792693910883643", "7520171284350373177", "7505421368810835210", "7540669161959312698", "7539281439025876284", "7535752686593330492", "7534384643006090556", "7532041405855649082", "7528873734003281211", "7528849349107879226"]}, "differences": {"missing_in_original": [], "missing_in_tikhub": [], "count_diff": -19}}, {"author_name": "四川观察", "sec_uid": "MS4wLjABAAAAg-FNvWWqPDHzDcC7zfumsDrlPzyYn9z0co5OUmXSgcM", "original_api": {"works_count": 40, "top10_ids": ["7541414502735138084", "7541405498155732287", "7541370302161358095", "7541368405585480987", "7541363595133668642", "7541355942999264564", "7541351141477453066", "7541303295341759802", "7541344692559023410", "7541334874796444978"]}, "tikhub_api": {"works_count": 20, "top10_ids": ["7543178252139924786", "7543648832005950771", "7543643181867355430", "7543632600535928110", "7543631430232968457", "7543627925615136010", "7543621336719985966", "7543618466478165274", "7543615128932044083", "7543593209642159420"]}, "differences": {"missing_in_original": [{"position": 1, "aweme_id": "7543178252139924786", "title": "又是一年七月七，牛郎织女来相会。8月29日，#今日七夕 祝福穿过银河 ，抵达你的心间，鹊桥相连，心桥相通，祝七夕幸福，爱意浓浓。"}, {"position": 2, "aweme_id": "7543648832005950771", "title": "#全国军训看广西  8月28日，广西钦州，#军训遇暴雨学生坚持会操 ，校领导冒雨观看，无一离席。"}, {"position": 3, "aweme_id": "7543643181867355430", "title": "8月28日，电影《南京照相馆》 荣获第二十届中国长春电影节“金鹿奖”最佳影片奖、最佳导演奖、最佳编剧奖。"}, {"position": 4, "aweme_id": "7543632600535928110", "title": "据日本《每日新闻》8月28日报道，日本警方表示，近日在神奈川县爱川町的郊外发现4个麻袋，里面装着大量人骨。经鉴定，这些人骨共包括4个人的头盖骨，5到6个人的股骨和前臂骨。警方正在对这起事件进行进一步搜查。"}, {"position": 5, "aweme_id": "7543631430232968457", "title": "8月28日，惠州惠阳公安通报：8月25日，惠阳区淡水街道某公司职员张某（男，38岁）因工作问题与同事陈某（女，32岁）发生纠纷。其间，张某将水杯砸向陈某，致其头部受伤。目前，张某已被公安机关依法刑事拘留，案件正在进一步侦办中。"}, {"position": 6, "aweme_id": "7543627925615136010", "title": "如果川超有笔画保卫战，广元最害怕？8月28日，广元主教练：请叫我“廣元”，这下不得虚哪个了！巴中：…… #川超观察"}, {"position": 7, "aweme_id": "7543621336719985966", "title": "8月28日，中国国防部新闻发言人张晓刚表示，我们要求日方深刻反省历史罪责，加快处置日遗化武进程，早日还中国人民净土。"}, {"position": 8, "aweme_id": "7543618466478165274", "title": "8月28日下午，国防部例行记者会上，#国防部回应日美将举行实战训练 ，#国防部回应日本不断强军扩武 。"}, {"position": 9, "aweme_id": "7543615128932044083", "title": "8月28日，#双流正寻找林恒无名幕 林徽因《哭三弟恒》背后的故事：1941年3月14日，25岁空军胞弟林恒在成都双流上空驾驶战斗机与日寇血战，不幸被敌机击中牺牲。据多家媒体报道，林徽因的儿子梁从诫回忆：“父亲匆匆赶往成都收殓了他（林恒）的遗体，掩埋在一处无名的墓地。”"}, {"position": 10, "aweme_id": "7543593209642159420", "title": "脚踩泥土追云写诗！#贵州校长把课堂搬进田野 ，农村娃的夏天有多治愈！贵州遵义茅天镇校长田术硬核“拆墙”，带学生躺田野听蝉鸣、追牛群画速写，把语文课变成“追云写诗大赛”！当城里娃困在补习班时，这些山野孩子光脚踩泥土、用镜头定格蜻蜓振翅——“美育是乡村孩子的解药，更是治愈童年的光！” \n\n3年来，#vivo童画未来美育计划 已走进90多所学校，200多位教师蜕变为“美育魔法师”，惠及约40000名孩子。用AI设计飞船、把蝉鸣编成电子诗，镜头下的每一帧都是对世界的炽热告白。 网友直呼：这才是素质教育天花板！"}], "missing_in_tikhub": [{"position": 1, "aweme_id": "7541414502735138084", "title": "最帅风景线！8月22日，上海虹桥站，#网友偶遇军人候车坐姿整齐划一 百余号人好似“复制粘贴”，着装整齐，纪律严明，可敬可佩!@哲也"}, {"position": 2, "aweme_id": "7541405498155732287", "title": "8月22日，#东方甄选辟谣孙东旭离职 ：在休假中，俞敏洪与孙东旭一直保持着良好的沟通。"}, {"position": 3, "aweme_id": "7541370302161358095", "title": "来自“峨眉派”的足球祝福！8月22日，#凌云用四川话为川超乐山队加油 ：竞技体育和传统武术内核都是永不言弃，乐山战友雄起！"}, {"position": 4, "aweme_id": "7541368405585480987", "title": "危险！8月22日，四川泸州。#小孩穿过护栏上高速玩耍被劝回 路过司机看到后连忙下车将小孩带回安全区，并报警寻求看护，事后孩子家长称自己睡着了，没发现孩子跑出家门。"}, {"position": 5, "aweme_id": "7541363595133668642", "title": "8月22日据极目新闻，#夫妻为保鲜往面条饺子皮里加硼砂 因生产、销售有毒、有害食品罪夫妻俩双双坐牢，被追缴违法所得，处15.84万元惩罚性赔偿金。"}, {"position": 6, "aweme_id": "7541355942999264564", "title": "被错误羁押6370天后，77岁男子申请国家赔偿1911万元。8月22日，杨徐邱表示，因案情复杂，云南省高院对他的申请将延期作出决定。"}, {"position": 7, "aweme_id": "7541351141477453066", "title": "8月22日，湖南永州市宁远县就民警找曝光者谈话家中老人被吓轻生一事发布情况通报：民警在走访的过程中，全程佩戴使用执法记录仪，没有语言恐吓，未发生过激言语，双方交流平和。"}, {"position": 8, "aweme_id": "7541303295341759802", "title": "补电解质真的能让运动不痛不累吗？专家揭秘运动疲劳成因和科学补给方案——电解质不仅要“喝对”，更要“补足”，才能有效减少运动后疲劳。#运动健身#电解质水"}, {"position": 9, "aweme_id": "7541344692559023410", "title": "8月22日据央视新闻，内蒙古自治区党委副书记、自治区政府主席王莉霞涉嫌严重违纪违法，目前正接受中央纪委国家监委纪律审查和监察调查。"}, {"position": 10, "aweme_id": "7541334874796444978", "title": "游客踏进黄龙景区钙化池内洗澡？8月22日，景区工作人员回应：并非洗澡，系下池捡拾无人机，已对游客批评教育，对维护该段的工作人员扣罚绩效。"}], "count_diff": -20}}, {"author_name": "J1°", "sec_uid": "MS4wLjABAAAAw3g-2YU5LDhWtpANQaAB43QexJOvDrzZ5SSuUTtXPD0", "original_api": {"works_count": 7, "top10_ids": ["7533225298389765386", "7475693488516959503", "7449116281812405544", "7442683317100612875", "7419166359109553418", "7383960983313632550", "7203312213321321788"]}, "tikhub_api": {"works_count": 8, "top10_ids": ["7542844623192100148", "7533225298389765386", "7475693488516959503", "7449116281812405544", "7442683317100612875", "7419166359109553418", "7383960983313632550", "7203312213321321788"]}, "differences": {"missing_in_original": [{"position": 1, "aweme_id": "7542844623192100148", "title": "#酒后行为艺术家 #熊熊 好久不见…"}], "missing_in_tikhub": [], "count_diff": 1}}, {"author_name": "ZUQ", "sec_uid": "MS4wLjABAAAAB0wfcsFT5vcIaUlpzkJQtMFqVz6rWhkxoIJf-qExqNc", "original_api": {"works_count": 19, "top10_ids": ["7538023317992262921", "7528798244777053449", "7523865295438630144", "7515048210773544218", "7512787810307411260", "7509074430686825779", "7506121346083474739", "7505467512637967642", "7502026296886381878", "7498698003592498451"]}, "tikhub_api": {"works_count": 19, "top10_ids": ["7538023317992262921", "7528798244777053449", "7523865295438630144", "7515048210773544218", "7512787810307411260", "7509074430686825779", "7506121346083474739", "7505467512637967642", "7502026296886381878", "7498698003592498451"]}, "differences": {"missing_in_original": [], "missing_in_tikhub": [], "count_diff": 0}}, {"author_name": "睿10学踢球⚽️", "sec_uid": "MS4wLjABAAAAhi0tbV9sG4sDg61ZZbxQmm5ZBTumnVssye4z5LR2eJg", "original_api": {"works_count": 1, "top10_ids": ["7518138103874194722"]}, "tikhub_api": {"works_count": 1, "top10_ids": ["7518138103874194722"]}, "differences": {"missing_in_original": [], "missing_in_tikhub": [], "count_diff": 0}}, {"author_name": "奥利弗<PERSON><PERSON><PERSON>", "sec_uid": "MS4wLjABAAAAPtedkC4Q5I4lWVa0HcvFsY-2oLmFWGvedFRxMA0612E", "original_api": {"works_count": 35, "top10_ids": ["7526732629421493523", "7540625329749069107", "7540172560428813577", "7539874983061884170", "7538344382332816694", "7536880242116431123", "7533573858964540735", "7531737518476234047", "7529665254264098089", "7529456913604594998"]}, "tikhub_api": {"works_count": 20, "top10_ids": ["7526732629421493523", "7540625329749069107", "7540172560428813577", "7539874983061884170", "7538344382332816694", "7536880242116431123", "7533573858964540735", "7531737518476234047", "7529665254264098089", "7529456913604594998"]}, "differences": {"missing_in_original": [], "missing_in_tikhub": [], "count_diff": -15}}, {"author_name": "🧺·碎碎念", "sec_uid": "MS4wLjABAAAAJ3QuSuR0RQrZULtzSqbTdn0X9MY3qr7c1UhwSpEHSEFjNOpif1VZCCI38vHupsvD", "original_api": {"works_count": 10, "top10_ids": ["7537696742742052147", "7522107926363819321", "7513559203944910140", "7484272539833732363", "7478275901780741436", "7472427686326062394", "7469073944306273594", "7464983362713701673", "7463140633855774009", "7462014888940981562"]}, "tikhub_api": {"works_count": 10, "top10_ids": ["7537696742742052147", "7522107926363819321", "7513559203944910140", "7484272539833732363", "7478275901780741436", "7472427686326062394", "7469073944306273594", "7464983362713701673", "7463140633855774009", "7462014888940981562"]}, "differences": {"missing_in_original": [], "missing_in_tikhub": [], "count_diff": 0}}, {"author_name": "小🐯吃素", "sec_uid": "MS4wLjABAAAAUqWXdawXjlmSenQGp5yR9IVPbf4Fkm8hG_6rvKojkAQJkOGfjnLoTr8kFiwzL7Pr", "original_api": {"works_count": 39, "top10_ids": ["7537340622296059196", "7537219147404610873", "7534779755679714620", "7534464190435577146", "7529630063479704891", "7529161581324176698", "7521230685379710268", "7519899229676178748", "7519396579473591609", "7518738864132771132"]}, "tikhub_api": {"works_count": 20, "top10_ids": ["7537340622296059196", "7537219147404610873", "7534779755679714620", "7534464190435577146", "7529630063479704891", "7529161581324176698", "7521230685379710268", "7519899229676178748", "7519396579473591609", "7518738864132771132"]}, "differences": {"missing_in_original": [], "missing_in_tikhub": [], "count_diff": -19}}, {"author_name": "摇裤儿大王", "sec_uid": "MS4wLjABAAAA5jTyKhUToWVp9HprCVOShx0HuIq3JvdclpEKlfv8F4zWh_IPjpIY0IzzsQmU8lSw", "original_api": {"works_count": 9, "top10_ids": ["7349303943886916914", "7536444808107429147", "7518746215053626650", "7512921020358069554", "7495708887103982858", "7494343261441297691", "7485788880109718835", "7482792638450568486", "7421923439730052387"]}, "tikhub_api": {"works_count": 10, "top10_ids": ["7349303943886916914", "7541498941238955290", "7536444808107429147", "7518746215053626650", "7512921020358069554", "7495708887103982858", "7494343261441297691", "7485788880109718835", "7482792638450568486", "7421923439730052387"]}, "differences": {"missing_in_original": [{"position": 2, "aweme_id": "7541498941238955290", "title": "人都是会变的！"}], "missing_in_tikhub": [], "count_diff": 1}}, {"author_name": "X馆长", "sec_uid": "MS4wLjABAAAAx9m8EGQZfsV_ssqKBCXdwlHrk9wVIxHhUzCA6G6sO9FGuwRM07ZbnplhwTTB6KT4", "original_api": {"works_count": 7, "top10_ids": ["7500571257597267260", "7494967817935064370", "7493821128616594739", "7493384209952738620", "7493145283400912188", "7492642711225339195", "7492303425774816572"]}, "tikhub_api": {"works_count": 7, "top10_ids": ["7500571257597267260", "7494967817935064370", "7493821128616594739", "7493384209952738620", "7493145283400912188", "7492642711225339195", "7492303425774816572"]}, "differences": {"missing_in_original": [], "missing_in_tikhub": [], "count_diff": 0}}, {"author_name": "开运可达鸭", "sec_uid": "MS4wLjABAAAAYZrGkVzBPZO7f952bEhb8xJqTY2l9TvkYbUT0SH2_HzafbYmlSv_GBf-AFQWiAGN", "original_api": {"works_count": 40, "top10_ids": ["7538983156545080614", "7536877375901076774", "7533902892970249510", "7533577873870556426", "7533054943411981577", "7531700111320026394", "7530839397316365606", "7530088191953882414", "7529022072791928110", "7528600963419589915"]}, "tikhub_api": {"works_count": 20, "top10_ids": ["7543064481350880550", "7542066122612296998", "7538983156545080614", "7536877375901076774", "7533902892970249510", "7533577873870556426", "7533054943411981577", "7531700111320026394", "7530839397316365606", "7530088191953882414"]}, "differences": {"missing_in_original": [{"position": 1, "aweme_id": "7543064481350880550", "title": "可惜我当不了你的理想型 #体育生 #寸头"}, {"position": 2, "aweme_id": "7542066122612296998", "title": "七夕和谁过。#寸头 #1m"}], "missing_in_tikhub": [{"position": 9, "aweme_id": "7529022072791928110", "title": "为什么看不上我 用东北话哄你不好吗。#寸头 #东北男孩"}, {"position": 10, "aweme_id": "7528600963419589915", "title": "我是个坚强的笨老爷们儿。#寸头 #糙汉"}], "count_diff": -20}}, {"author_name": "2025 大吉大利", "sec_uid": "MS4wLjABAAAAelQEgL8_NsNK-sX_9fzE-zq2J-to4dd4_R9sn0df5Bs", "original_api": {"works_count": 6, "top10_ids": ["7540673667577318665", "7539169887882726656", "7529085936798797108", "7485259849731689763", "7434320165619191074", "7422910822655053097"]}, "tikhub_api": {"works_count": 6, "top10_ids": ["7540673667577318665", "7539169887882726656", "7529085936798797108", "7485259849731689763", "7434320165619191074", "7422910822655053097"]}, "differences": {"missing_in_original": [], "missing_in_tikhub": [], "count_diff": 0}}, {"author_name": "渠宝", "sec_uid": "MS4wLjABAAAAtSP0qa0_YixQ9Tg-0m8saiIU_uSglt8P_XdeEc6_rXs", "original_api": {"works_count": 7, "top10_ids": ["7541369824314019132", "7536221105117171003", "7531757701756915001", "7522501759542447360", "7517674800174157113", "7512433882186026298", "7502078649181687074"]}, "tikhub_api": {"works_count": 9, "top10_ids": ["7543626472020643132", "7542889483868359995", "7541369824314019132", "7536221105117171003", "7531757701756915001", "7522501759542447360", "7517674800174157113", "7512433882186026298", "7502078649181687074"]}, "differences": {"missing_in_original": [{"position": 1, "aweme_id": "7543626472020643132", "title": "按时长大噜😎#老年人健身的好处 #穿袜子睡觉有哪些好处 我们都要变成厉害的大人保护稚气的自己"}, {"position": 2, "aweme_id": "7542889483868359995", "title": "我们会再见的😎"}], "missing_in_tikhub": [], "count_diff": 2}}, {"author_name": "我叫小李（减脂版）", "sec_uid": "MS4wLjABAAAAjP7sI1Nn1FDX5BMEfi5L3I3gSYIoJOq7FecFxrlAg5g", "original_api": {"works_count": 10, "top10_ids": ["7539364387548925236", "7535124758457044258", "7534287707435486479", "7530655852446502179", "7529384129815022905", "7529180372039879994", "7528766136692985128", "7526144895784111417", "7519085480748289280", "7501186293549010176"]}, "tikhub_api": {"works_count": 11, "top10_ids": ["7542836751670054196", "7539364387548925236", "7535124758457044258", "7534287707435486479", "7530655852446502179", "7529384129815022905", "7529180372039879994", "7528766136692985128", "7526144895784111417", "7519085480748289280"]}, "differences": {"missing_in_original": [{"position": 1, "aweme_id": "7542836751670054196", "title": "大头大头……下雨不愁😜#健身 #自拍自拍自拍 #圆寸"}], "missing_in_tikhub": [{"position": 10, "aweme_id": "7501186293549010176", "title": "头发理了，胡子刮了，小狗也溜了 #故事的小黄花 #柴犬 #草地 #top"}], "count_diff": 1}}, {"author_name": "橙子榨汁", "sec_uid": "MS4wLjABAAAAbtAmo3jqfWf2-piNf0K5aQOfbX_zYw63eM_no4veAqw", "original_api": {"works_count": 26, "top10_ids": ["7500591514931268873", "7539138561842679067", "7537580109433179402", "7536217325637389628", "7535475826134961417", "7533999373619236123", "7532413488284290330", "7532140120457497866", "7530489951969676594", "7529857959276399899"]}, "tikhub_api": {"works_count": 21, "top10_ids": ["7500591514931268873", "7542842006973369651", "7541739158030912777", "7539138561842679067", "7537580109433179402", "7536217325637389628", "7535475826134961417", "7533999373619236123", "7532413488284290330", "7532140120457497866"]}, "differences": {"missing_in_original": [{"position": 2, "aweme_id": "7542842006973369651", "title": "没时间 真的没时间拍了"}, {"position": 3, "aweme_id": "7541739158030912777", "title": "酒店随拍 交作业了#圆寸 #胖熊"}], "missing_in_tikhub": [{"position": 9, "aweme_id": "7530489951969676594", "title": "诶诶 嘿嘿"}, {"position": 10, "aweme_id": "7529857959276399899", "title": "跳舞日常 和我一起跳舞吧"}], "count_diff": -5}}, {"author_name": "安馨", "sec_uid": "MS4wLjABAAAAHgLrPfmNa7QMG61Vl1csF-6bSJTK6x1q35olcsVktz4", "original_api": {"works_count": 43, "top10_ids": ["7277136406399503674", "7165439472421588261", "7166587113339899173", "7540487947842604347", "7539825630419651897", "7537267851746905401", "7519033471047650617", "7516459487105715515", "7512013690439863610", "7508693372115389754"]}, "tikhub_api": {"works_count": 23, "top10_ids": ["7277136406399503674", "7165439472421588261", "7166587113339899173", "7543197223397559610", "7542349454914129210", "7540487947842604347", "7539825630419651897", "7537267851746905401", "7519033471047650617", "7516459487105715515"]}, "differences": {"missing_in_original": [{"position": 4, "aweme_id": "7543197223397559610", "title": "眼皮子咋这么沉呢#我服了 #内容过于真实"}, {"position": 5, "aweme_id": "7542349454914129210", "title": "半夜凉快想出去逛逛#花式带娃#虎贝尔遛娃神器#虎贝尔城市漫步车\n"}], "missing_in_tikhub": [{"position": 9, "aweme_id": "7512013690439863610", "title": "学会这几句在东北你就权威了#东北话 #内容过于真实"}, {"position": 10, "aweme_id": "7508693372115389754", "title": "嘎哈啊#内容太过真实 #东北话"}], "count_diff": -20}}, {"author_name": "宇霖", "sec_uid": "MS4wLjABAAAAzkxgL7xOq1T3Jy8lBK-9iVQuRxL88QSONs7c-sTGIKE", "original_api": {"works_count": 43, "top10_ids": ["7470124896891211059", "7475558030315097354", "7099378072335535374", "7541244449886063914", "7540951852444618025", "7539811017871805732", "7539523647868276007", "7537595301429136679", "7537231742168911123", "7536903929927748900"]}, "tikhub_api": {"works_count": 23, "top10_ids": ["7470124896891211059", "7475558030315097354", "7099378072335535374", "7543229361089301779", "7542718775448276265", "7542531606172781860", "7542035075157724459", "7541675825633037609", "7541244449886063914", "7540951852444618025"]}, "differences": {"missing_in_original": [{"position": 4, "aweme_id": "7543229361089301779", "title": "我当时人麻了#vlog十亿流量扶持计划 #沈阳出租车 视频有点长 请耐心看完"}, {"position": 5, "aweme_id": "7542718775448276265", "title": "亲测有用！#听到就睁眼全民挑战 #精神状态belike #宇霖"}, {"position": 6, "aweme_id": "7542531606172781860", "title": "打个车司机非要给我上才艺#沈阳出租车 #刀马刀马手势舞 #抽象"}, {"position": 7, "aweme_id": "7542035075157724459", "title": "今天休息 做做家务健健身#vlog十亿流量扶持计划 #抽象 #整活"}, {"position": 8, "aweme_id": "7541675825633037609", "title": "当我决定用三个月的时间认真减肥的结果#内容过于真实 #減肥 #抽象"}], "missing_in_tikhub": [{"position": 6, "aweme_id": "7539811017871805732", "title": "出海啦"}, {"position": 7, "aweme_id": "7539523647868276007", "title": "啊？哈哈哈哈#抽象 #整活"}, {"position": 8, "aweme_id": "7537595301429136679", "title": "重生之我在沈阳当的哥#vlog十亿流量扶持计划 #沈阳出租车 #抽象"}, {"position": 9, "aweme_id": "7537231742168911123", "title": "狗不干我干 干的就是出租车#vlog十亿流量扶持计划 #沈阳出租车 #抽象"}, {"position": 10, "aweme_id": "7536903929927748900", "title": "不当牛不当马 今天当牛马 #vlog十亿流量扶持计划 #国产 #沈阳出租车"}], "count_diff": -20}}, {"author_name": "练完忘吃蛋白粉", "sec_uid": "MS4wLjABAAAA_9N5CX2I55mtsFN3ziLcVQ4tpQtDMGrYTJiBjm2f1-W5csItEoCgU4G9iCeQPhzk", "original_api": {"works_count": 40, "top10_ids": ["7541360347673972027", "7540680511040638267", "7539624855131491642", "7539108931559705916", "7538682644843728187", "7538271274687221052", "7537667909814127931", "7536507466965749049", "7535283554525089083", "7534392670442425659"]}, "tikhub_api": {"works_count": 20, "top10_ids": ["7543268956057521465", "7542878276059221307", "7542534240260279610", "7542515288231677244", "7542200247184477498", "7541360347673972027", "7540680511040638267", "7539624855131491642", "7539108931559705916", "7538682644843728187"]}, "differences": {"missing_in_original": [{"position": 1, "aweme_id": "7543268956057521465", "title": "做你的也行～\n#肌肉 #增肌 #荷尔蒙 #撸铁 #憨憨"}, {"position": 2, "aweme_id": "7542878276059221307", "title": "夏天可以过去了吗？好热～\n#肌肉 #增肌 #胸肌 #荷尔蒙"}, {"position": 3, "aweme_id": "7542534240260279610", "title": "#戏鸳鸯dj #浅跳一下 #憨憨 柔弱一下～#手臂力量"}, {"position": 4, "aweme_id": "7542515288231677244", "title": "依旧练胸\n#肌肉 #健身 #增肌 #寸头 #憨憨"}, {"position": 5, "aweme_id": "7542200247184477498", "title": "幼稚的像小学同桌～#肌肉 #增肌 #情侣 #寸头 #恋爱日常"}], "missing_in_tikhub": [{"position": 6, "aweme_id": "7538271274687221052", "title": "胸大过肚子了，可以进熊圈了吗？#肌肉 #健身 #荷尔蒙 #增肌 #胸肌"}, {"position": 7, "aweme_id": "7537667909814127931", "title": "晚上好，亲爱的。#肌肉 #荷尔蒙 #情侣日常 #你的男朋友"}, {"position": 8, "aweme_id": "7536507466965749049", "title": "感觉肌肉从来不属于我，来的慢，去得快～\n#肌肉 #吗喽 #荷尔蒙 #top"}, {"position": 9, "aweme_id": "7535283554525089083", "title": "健身 #肌肉 #健身 #寸头 #胸肌 #增肌 第一次来健身房锻炼，可以告诉我一下，需要注意什么吗？"}, {"position": 10, "aweme_id": "7534392670442425659", "title": "雨下整夜，我的爱溢出就像雨水。#健身 #肌肉 #荷尔蒙 #胸肌 #增肌"}], "count_diff": -20}}, {"author_name": "醉烬诗", "sec_uid": "MS4wLjABAAAAUhGKJGvbpJhCZYFpkLu3ia54Yn6vxwf-RkLIxtFOfpphz0ye7kXU9L4tCWn3TEkg", "original_api": {"works_count": 40, "top10_ids": ["7507140985752489276", "7495052720026963259", "7488753523845303610", "7484833580003134779", "7484690638089309497", "7484482402975354169", "7481235716794535225", "7473798638200737083", "7472208470436039993", "7464756273297296699"]}, "tikhub_api": {"works_count": 20, "top10_ids": ["7507140985752489276", "7495052720026963259", "7488753523845303610", "7484833580003134779", "7484690638089309497", "7484482402975354169", "7481235716794535225", "7473798638200737083", "7472208470436039993", "7464756273297296699"]}, "differences": {"missing_in_original": [], "missing_in_tikhub": [], "count_diff": -20}}, {"author_name": "1憨憨🇨🇳", "sec_uid": "MS4wLjABAAAAaGCNaz0HCcJpPBTKosZ1gZR6z4-YEuAhoQMhtRQEZ_c", "original_api": {"works_count": 40, "top10_ids": ["7540743678602038571", "7540281399779347754", "7539507034460310847", "7537271960993811755", "7537029149907651859", "7533922071441870134", "7533565593879809343", "7532690361588813092", "7531981256784891175", "7530860602374917418"]}, "tikhub_api": {"works_count": 20, "top10_ids": ["7542840042759392575", "7542464624365817124", "7541475348476808490", "7540743678602038571", "7540281399779347754", "7539507034460310847", "7537271960993811755", "7537029149907651859", "7533922071441870134", "7533565593879809343"]}, "differences": {"missing_in_original": [{"position": 1, "aweme_id": "7542840042759392575", "title": "#小叔叔 夏天吹空调感冒真的很难痊愈啊。"}, {"position": 2, "aweme_id": "7542464624365817124", "title": "#小叔叔 等外卖，卖个萌。"}, {"position": 3, "aweme_id": "7541475348476808490", "title": "#小叔叔 这天气，感冒真老火，头晕眼花，一直流鼻涕，嘴里还火热热的。啊啊啊。"}], "missing_in_tikhub": [{"position": 8, "aweme_id": "7532690361588813092", "title": "#小叔叔 外面太阳好大，可以帮我去溜狗狗吗？宝。"}, {"position": 9, "aweme_id": "7531981256784891175", "title": "#小叔叔 周日过了，今天是周一。"}, {"position": 10, "aweme_id": "7530860602374917418", "title": "#小叔叔 没睡好觉，干啥都感觉没劲。啊啊啊啊啊！"}], "count_diff": -20}}, {"author_name": "老赵Morg", "sec_uid": "MS4wLjABAAAAeKdZyuPYM5weawNwly0NituhSj0LrVs-8l8ja-YaZgM", "original_api": {"works_count": 41, "top10_ids": ["7084074159273299200", "7536453994529459482", "7532393245456747803", "7527256667671596326", "7524653446172036403", "7520979457172180251", "7517915697872227594", "7516175058633542949", "7511659138976353574", "7507222031948172570"]}, "tikhub_api": {"works_count": 21, "top10_ids": ["7084074159273299200", "7536453994529459482", "7532393245456747803", "7527256667671596326", "7524653446172036403", "7520979457172180251", "7517915697872227594", "7516175058633542949", "7511659138976353574", "7507222031948172570"]}, "differences": {"missing_in_original": [], "missing_in_tikhub": [], "count_diff": -20}}, {"author_name": "大侄子", "sec_uid": "MS4wLjABAAAASPXGGubWQnUVx2g9oBu73IUsHv6bq1un-bcNiBJeVY4", "original_api": {"works_count": 40, "top10_ids": ["7541379293294939449", "7541230977055853882", "7541056275955223865", "7540570945305333052", "7540266259004362043", "7539952770772061500", "7539777096488717627", "7535120439481224506", "7533558276840164668", "7532933025245924668"]}, "tikhub_api": {"works_count": 20, "top10_ids": ["7543118001300081977", "7542741978728762684", "7542427759964982586", "7542191894647524668", "7541379293294939449", "7541230977055853882", "7541056275955223865", "7540570945305333052", "7540266259004362043", "7539952770772061500"]}, "differences": {"missing_in_original": [{"position": 1, "aweme_id": "7543118001300081977", "title": "#大数据推荐给有需要的人 #大叔已上线"}, {"position": 2, "aweme_id": "7542741978728762684", "title": "#正装 #大数据推荐给有需要的人"}, {"position": 3, "aweme_id": "7542427759964982586", "title": "#小叔叔 #爷们儿 #寸头"}, {"position": 4, "aweme_id": "7542191894647524668", "title": "#小叔叔"}], "missing_in_tikhub": [{"position": 7, "aweme_id": "7539777096488717627", "title": "#胖熊 #小叔叔 #黑皮 午安，睡醒继续搬砖"}, {"position": 8, "aweme_id": "7535120439481224506", "title": "#这些年经历了什么"}, {"position": 9, "aweme_id": "7533558276840164668", "title": "#小叔叔 #胖熊 #纯爷们 #大肚子"}, {"position": 10, "aweme_id": "7532933025245924668", "title": "#小叔叔 #胖熊 #纯爷们 五花肉"}], "count_diff": -20}}, {"author_name": "龙八一", "sec_uid": "MS4wLjABAAAACXcUnDVFe1YzLuBVvUUrxQSjYir_8l1wT3Y4SickomZ_fNajZtcC0yZdq10ctepN", "original_api": {"works_count": 42, "top10_ids": ["7405910961111993634", "7383320164260908288", "7540145037414305083", "7538782345689976121", "7535838064482438458", "7534706088324631867", "7534366060700060987", "7525405987508489531", "7523563021819399482", "7516526196940606778"]}, "tikhub_api": {"works_count": 22, "top10_ids": ["7405910961111993634", "7383320164260908288", "7540145037414305083", "7538782345689976121", "7535838064482438458", "7534706088324631867", "7534366060700060987", "7525405987508489531", "7523563021819399482", "7516526196940606778"]}, "differences": {"missing_in_original": [], "missing_in_tikhub": [], "count_diff": -20}}, {"author_name": "霹雳厂花", "sec_uid": "MS4wLjABAAAA3gvDzCAt40zw8DGU-7FR08B0T9937hvDLLVhha6AChq70Y-pYrHgn0xJVz6xx6cB", "original_api": {"works_count": 41, "top10_ids": ["7484957997907709225", "7489782069896285494", "7375093650734255386", "7539508509151661322", "7535061634239941939", "7534321849238015283", "7527264219789217062", "7526164044677483803", "7525777447515032882", "7523180319694130458"]}, "tikhub_api": {"works_count": 23, "top10_ids": ["7484957997907709225", "7489782069896285494", "7375093650734255386", "7543218523447807241", "7539508509151661322", "7535061634239941939", "7534321849238015283", "7527264219789217062", "7526164044677483803", "7525777447515032882"]}, "differences": {"missing_in_original": [{"position": 4, "aweme_id": "7543218523447807241", "title": "你当像鸟飞往你的山"}], "missing_in_tikhub": [{"position": 10, "aweme_id": "7523180319694130458", "title": "属于我们的夏天就要来了 （@OfAkiva ）#我的人生AB面 #夏天就要听Bahama"}], "count_diff": -18}}, {"author_name": "爱上半导体", "sec_uid": "MS4wLjABAAAAjAwjTSmx7FPFX7smio7plz5O-IV-mZQ7eA7CMCi3UX4", "original_api": {"works_count": 40, "top10_ids": ["7530520945249766708", "7523091901700984073", "7513964759813852454", "7510265122452032794", "7502057162881944886", "7498338229653867787", "7493157238572387635", "7482989135276313910", "7479074306996129034", "7471654753051757862"]}, "tikhub_api": {"works_count": 20, "top10_ids": ["7542914658118798619", "7530520945249766708", "7523091901700984073", "7513964759813852454", "7510265122452032794", "7502057162881944886", "7498338229653867787", "7493157238572387635", "7482989135276313910", "7479074306996129034"]}, "differences": {"missing_in_original": [{"position": 1, "aweme_id": "7542914658118798619", "title": "实现接口大一统的USB数据线、到底是如何工作的？ #USB #通讯"}], "missing_in_tikhub": [{"position": 10, "aweme_id": "7471654753051757862", "title": "蓝牙编程、在Ai面前如切瓜砍菜！小白也能1分钟搞定！ 据说现在有人用Ai做APP开发，一个半小时就做出来了一个苹果APP，上架之后、下载量居然排名第一。据我朋友说收益差不多得100来万，简直不可思议。\n比起纯软件编程、硬件编程不知道要简单多少倍。\n于是用蓝牙的编程来试试Ai硬件编程的水平。\n不试不知道、一试吓一跳！编程结果完全出乎我的意料！\n不到一分钟、手机竟然和Arduino建立了蓝牙通讯，完全满足了我的需求，这以后硬件编程的壁垒还存在吗？真可怕！\n所以对于你们之前想搞、但是由于技术不达标没有搞成的项目。现在是时候搞起来了！\n不多说了！搞起来吧！#人工智能 #单片机 #deepseek #蓝牙 #Arduino"}], "count_diff": -20}}, {"author_name": "民生铺子大码男装", "sec_uid": "MS4wLjABAAAAsPmsJ0_iQxy9maX2AQFAkwN7sXcYuRDjIiFdXE62oaezZ7HPotVMdxJKBv9llUg2", "original_api": {"works_count": 38, "top10_ids": ["7536549170128506148", "7536141648964750646", "7533585036685217043", "7531681698704411923", "7530611558399135039", "7528349877698546987", "7519135486343367948", "7511348609528220939", "7461421333096795402", "7453399946507701514"]}, "tikhub_api": {"works_count": 20, "top10_ids": ["7536549170128506148", "7536141648964750646", "7533585036685217043", "7531681698704411923", "7530611558399135039", "7528349877698546987", "7519135486343367948", "7511348609528220939", "7461421333096795402", "7453399946507701514"]}, "differences": {"missing_in_original": [], "missing_in_tikhub": [], "count_diff": -18}}, {"author_name": "可乐啊", "sec_uid": "MS4wLjABAAAAF1E1iorNkF2GC0hnKeFzuWRWDxJFu7Qc8WX3uhOcLuHfQPi26Py9dwc_obO3qFMm", "original_api": {"works_count": 24, "top10_ids": ["7540686299373145387", "7524716355205483786", "7516580221366390067", "7515466881814973705", "7511002638775782693", "7510222032764833050", "7506515024514469174", "7497698434238352678", "7483010145157500170", "7474621518715063589"]}, "tikhub_api": {"works_count": 19, "top10_ids": ["7540686299373145387", "7524716355205483786", "7516580221366390067", "7515466881814973705", "7511002638775782693", "7510222032764833050", "7506515024514469174", "7497698434238352678", "7483010145157500170", "7474621518715063589"]}, "differences": {"missing_in_original": [], "missing_in_tikhub": [], "count_diff": -5}}, {"author_name": "🐳肥鱼队长", "sec_uid": "MS4wLjABAAAAj2WboLrdpTyfdQAYLoqtlbrttfbClI81265FGm1937s", "original_api": {"works_count": 42, "top10_ids": ["7297817126293851432", "7139142755694955790", "7221780141293292855", "7541379767397190955", "7541337539144846638", "7540993287035112714", "7540961546689416491", "7540603551212358953", "7540572182893350182", "7539839342727302463"]}, "tikhub_api": {"works_count": 23, "top10_ids": ["7297817126293851432", "7139142755694955790", "7221780141293292855", "7543594849465273646", "7543562878881058084", "7543205432480468233", "7542851026471570724", "7542826380132928822", "7542073852189216054", "7542037371148897574"]}, "differences": {"missing_in_original": [{"position": 4, "aweme_id": "7543594849465273646", "title": "不得不说河南的超市真的太有活人感了\n值得全国的企业学习\n不管是几线城市老百姓都需要这样的欣欣向荣和安居乐业\n\n#日常逛超市 #幸福感 #安居乐业 #国泰民安"}, {"position": 5, "aweme_id": "7543562878881058084", "title": "终于吃到心心念念的云南味道了 #郑州 #云南菜"}, {"position": 6, "aweme_id": "7543205432480468233", "title": "姐妹们冲啊\n\n#七夕#开学季#自助女装#郑州#教师节"}, {"position": 7, "aweme_id": "7542851026471570724", "title": "看到后面有惊喜别错过啦～ #卡吉诺乳酪月饼#vlog日常"}, {"position": 8, "aweme_id": "7542826380132928822", "title": "66两次的艾熏体验 还真挺奇妙的 #蜗艾蜗家艾公馆 #艾草盛夏伏送万家 #蜗家艾养伏启安康#蜗艾蜗家三伏灸#清心以艾蕾养于熏"}, {"position": 9, "aweme_id": "7542073852189216054", "title": "这个七夕来正弘收礼啦～ #爱意正弘#打卡 #vlog #郑州约会"}, {"position": 10, "aweme_id": "7542037371148897574", "title": "希望郑州的夜越来越精彩\n\n#醉美夜郑州"}], "missing_in_tikhub": [{"position": 4, "aweme_id": "7541379767397190955", "title": "这就是泰式精油SPA嘛 这次的体验也太新鲜啦 #泰式养生#下班放松好去处#按摩spa#郑州按摩"}, {"position": 5, "aweme_id": "7541337539144846638", "title": "我只能说现在的自助餐也太卷了吧 \n#尹宝赞自助烤肉#尹宝赞#原切烤肉  #延边市集  #郑州美食"}, {"position": 6, "aweme_id": "7540993287035112714", "title": "人均80吃饱又吃好的客家菜 \n#客家小馆 #客家菜 #郑州美食"}, {"position": 7, "aweme_id": "7540961546689416491", "title": "川式烧烤炭火香气也太足了吧 #围满停车场烧烤#特色烧烤 #夜宵吃什么 #烧烤"}, {"position": 8, "aweme_id": "7540603551212358953", "title": "农科路店榴莲畅吃还有19.9代50的券能用 萍姐你太牛啦 #火锅 #榴莲自助 #萍姐火锅#郑州火锅"}, {"position": 9, "aweme_id": "7540572182893350182", "title": "原本来许昌准备逛胖东来\n结果意外发现了真汕头 \n#寻味烟火气 #烟火小店"}, {"position": 10, "aweme_id": "7539839342727302463", "title": "59代100 这家黄油熟成牛排真的有点东西 #七夕约会指南 #郑州约会西餐厅 #pogoyo蒲公英 #黄油熟成牛排"}], "count_diff": -19}}, {"author_name": "haoyu16cm", "sec_uid": "MS4wLjABAAAAtJYnHV1RE3ml1xhdcn4n4j6ZEjcsWmWS4xIn4KtCDLsrsfz4u516CeaBpFQ83TdS", "original_api": {"works_count": 42, "top10_ids": ["7452017322648292617", "7539416917355498806", "7506892653637881099", "7541381252383198506", "7541215586089733415", "7540903986255039780", "7540650997455768875", "7540311502713916711", "7540308862054747431", "7539854717369060671"]}, "tikhub_api": {"works_count": 23, "top10_ids": ["7452017322648292617", "7539416917355498806", "7506892653637881099", "7543186803500649747", "7543184432993078582", "7543160113743433014", "7542927343322582311", "7542766712837082411", "7542511332245048595", "7542457827072216356"]}, "differences": {"missing_in_original": [{"position": 4, "aweme_id": "7543186803500649747", "title": ""}, {"position": 5, "aweme_id": "7543184432993078582", "title": ""}, {"position": 6, "aweme_id": "7543160113743433014", "title": ""}, {"position": 7, "aweme_id": "7542927343322582311", "title": ""}, {"position": 8, "aweme_id": "7542766712837082411", "title": "摸头杀差点被打了"}, {"position": 9, "aweme_id": "7542511332245048595", "title": "谁敢发表情包试试！#吴京 #男人味 #臭男人"}, {"position": 10, "aweme_id": "7542457827072216356", "title": ""}], "missing_in_tikhub": [{"position": 4, "aweme_id": "7541381252383198506", "title": ""}, {"position": 5, "aweme_id": "7541215586089733415", "title": ""}, {"position": 6, "aweme_id": "7540903986255039780", "title": "不懂就问#山东男孩"}, {"position": 7, "aweme_id": "7540650997455768875", "title": "家人们觉得我应该怎么办#成都 #山东男孩"}, {"position": 8, "aweme_id": "7540311502713916711", "title": "他害羞了 说明了什么？"}, {"position": 9, "aweme_id": "7540308862054747431", "title": ""}, {"position": 10, "aweme_id": "7539854717369060671", "title": ""}], "count_diff": -19}}, {"author_name": "马上就出发", "sec_uid": "MS4wLjABAAAAjTOLaodn44tvXmVqLNhjilxtohyigjhXItXObtlQLbztKBopRCgrcWSH9O9GdRkn", "original_api": {"works_count": 39, "top10_ids": ["7473440733014379816", "7473712766134668579", "7510978412572904714", "7510824150275165450", "7503484978748935451", "7496122410157935887", "7494510375761726772", "7493112613338271010", "7492426676291357992", "7491508496786017551"]}, "tikhub_api": {"works_count": 21, "top10_ids": ["7473440733014379816", "7510978412572904714", "7510824150275165450", "7503484978748935451", "7496122410157935887", "7494510375761726772", "7493112613338271010", "7492426676291357992", "7491508496786017551", "7490152301701778728"]}, "differences": {"missing_in_original": [{"position": 10, "aweme_id": "7490152301701778728", "title": "体制内的快说说 你们真的是这样嘛#分配对象 #体制内生活 #体制内和体制外"}], "missing_in_tikhub": [{"position": 2, "aweme_id": "7473712766134668579", "title": "老一辈艺术家还是太超前了 你们还知道什么版本的呢？#原来早就看过李羲承进行曲了 #前奏一响拾起多少人的回忆 #万物都能李羲承进行曲 #李羲承"}], "count_diff": -18}}, {"author_name": "阿胜l", "sec_uid": "MS4wLjABAAAAL4_9SBb_ANHCQhLwKaDCvLPd4u63i5hwaJBcO6RA0a4", "original_api": {"works_count": 18, "top10_ids": ["7540952715238542651", "7540573074015014202", "7539869384681688377", "7539092158382574908", "7537975583327702332", "7534267131937803578", "7533889406161521980", "7523536519190400314", "7522808103449201979", "7514235277200592185"]}, "tikhub_api": {"works_count": 19, "top10_ids": ["7543561702894816571", "7543510279260278073", "7543178507753459002", "7540952715238542651", "7540573074015014202", "7539869384681688377", "7539092158382574908", "7537975583327702332", "7534267131937803578", "7533889406161521980"]}, "differences": {"missing_in_original": [{"position": 1, "aweme_id": "7543561702894816571", "title": "谁来介绍个男人#小叔叔 #想男人想到失眠 #全能"}, {"position": 2, "aweme_id": "7543510279260278073", "title": "我要送你日不落的想念 想和你过俩人世界#稻田 #风吹麦浪 #想你了"}, {"position": 3, "aweme_id": "7543178507753459002", "title": "给爷笑一个。哈哈！#我的摄影日记 #大自然 #丽江 #日常生活"}], "missing_in_tikhub": [{"position": 8, "aweme_id": "7523536519190400314", "title": "劝分朋友 见证爱情 @小泽✔️ #劝分 #塑料闺蜜"}, {"position": 9, "aweme_id": "7522808103449201979", "title": "你可以说我但我不能说你 #塑料兄弟 #朋友的迷惑行为 #塑料闺蜜"}, {"position": 10, "aweme_id": "7514235277200592185", "title": "只想要你的偏爱，唯爱我#偏爱 #有肉感的小胖子"}], "count_diff": 1}}, {"author_name": "感觉", "sec_uid": "MS4wLjABAAAAuYlBd1Dp65u9cmdhsELFVKNHKJul9yamK5WxTLLzFy4", "original_api": {"works_count": 35, "top10_ids": ["6802109691586546957", "7539464310449524028", "7524356566369520955", "7486645383280594191", "7484665187123989775", "7400420008792296758", "7360243007783750948", "7359422026580315446", "7348720537499192588", "7345327439544880438"]}, "tikhub_api": {"works_count": 21, "top10_ids": ["6802109691586546957", "7539464310449524028", "7524356566369520955", "7486645383280594191", "7484665187123989775", "7400420008792296758", "7360243007783750948", "7359422026580315446", "7348720537499192588", "7345327439544880438"]}, "differences": {"missing_in_original": [], "missing_in_tikhub": [], "count_diff": -14}}, {"author_name": "通州全知道", "sec_uid": "MS4wLjABAAAA4nwdIpU6caPz-tCgUY4hQQzlduRf3SzeOB2xYIqV9M0", "original_api": {"works_count": 43, "top10_ids": ["7478282043349077302", "7304175146003172658", "7176935523938749736", "7541304367861746954", "7540237217348750601", "7539689706083142963", "7538751534876708147", "7538760915240406282", "7538403707268975922", "7537998313539276082"]}, "tikhub_api": {"works_count": 23, "top10_ids": ["7478282043349077302", "7304175146003172658", "7176935523938749736", "7543600693626604838", "7543232489268006154", "7542816810484223258", "7541971252966231296", "7541304367861746954", "7540237217348750601", "7539689706083142963"]}, "differences": {"missing_in_original": [{"position": 4, "aweme_id": "7543600693626604838", "title": "新增6片真草足球场！苏超南通队霸榜是有原因的！ 南通有一百多支业余球队？！#苏超 #南通队 #南通 #业余足球 #苏超联赛"}, {"position": 5, "aweme_id": "7543232489268006154", "title": "通州也有家居博主？这套顶复花园，是多少女生的梦中情家？ #顶复 #家居博主 #南通通州 #顶复空中花园 #家居生活@小影（顶复装修中）"}, {"position": 6, "aweme_id": "7542816810484223258", "title": "探秘南通大厂，第二站：沃太能源！宿舍规格比我家里都高？！ 还有健身房、篮球、羽毛球场！通州的大厂里到底是有什么惊喜是我不知道的？快拿出来给大家开开眼！#探厂 #大厂食堂  #南通通州 #工厂食堂 #探秘 公司食堂打饭日记"}, {"position": 7, "aweme_id": "7541971252966231296", "title": "你觉得这一碗蟹黄拌面怎么样？ #蟹黄拌面 #蟹黄面 #南通美食 #拌面 #烟火小店"}], "missing_in_tikhub": [{"position": 7, "aweme_id": "7538751534876708147", "title": "上次评论区的猪头肉，我们一次尝了三家！ 猪头王、金余建华卤菜、老电镀厂，吃过的朋友，你们觉得哪家更好吃？#猪头肉 #下酒菜 #南通美食 #猪头肉一绝 #精诚一家 夏日消暑美食"}, {"position": 8, "aweme_id": "7538760915240406282", "title": "在今天的这样一个日子，我们来听听百岁老人讲述当年发生的事 #日本投降纪念日 #日本投降勿忘国耻 #南通"}, {"position": 9, "aweme_id": "7538403707268975922", "title": "阿连，这周末，你的新“狼”来了~让我来看看你的\"连\"“蛋” #苏超 #苏超联赛 #抽象 #南通 #连云港 苏超第九轮开赛在即"}, {"position": 10, "aweme_id": "7537998313539276082", "title": "对话2025届通高录取北大的两位学霸！全程高质量干货！ 学霸的学习经验和心态分享，值得学习。有想走竞赛这条路的同学可以参考。#孩子教育 #家长必读 #南通教育 #强基计划 #北京大学 全网学霸都在晒录取通知书"}], "count_diff": -20}}, {"author_name": "喜欢唱歌的A2老司机", "sec_uid": "MS4wLjABAAAAiXGEs_tkT7vZBX71hR-G8lMhVaPC6SXvcTSENql-56dMLRxk-XHyBAU35axynSpa", "original_api": {"works_count": 43, "top10_ids": ["7503765534841654568", "7495215242856926479", "7487855437488966912", "7541327076452896015", "7540169220454829327", "7539492520608304424", "7538782492988345600", "7537527891342708020", "7536549237262208256", "7535300402302356770"]}, "tikhub_api": {"works_count": 23, "top10_ids": ["7503765534841654568", "7495215242856926479", "7487855437488966912", "7543591243079978240", "7543211330964409634", "7542738827182918964", "7541992947085217024", "7541327076452896015", "7540169220454829327", "7539492520608304424"]}, "differences": {"missing_in_original": [{"position": 4, "aweme_id": "7543591243079978240", "title": "和 @我是买鱼的 一起 #合拍"}, {"position": 5, "aweme_id": "7543211330964409634", "title": "和 @倾听雨落🎵 一起 #合拍 印度神曲，"}, {"position": 6, "aweme_id": "7542738827182918964", "title": "等你下课#周杰伦#耳机翻唱"}, {"position": 7, "aweme_id": "7541992947085217024", "title": "可不可以#张紫豪#耳机翻唱"}], "missing_in_tikhub": [{"position": 7, "aweme_id": "7538782492988345600", "title": "如果呢#郑润泽#耳机翻唱"}, {"position": 8, "aweme_id": "7537527891342708020", "title": "小胡同#郑润泽#无卡耳机翻唱"}, {"position": 9, "aweme_id": "7536549237262208256", "title": "搁浅#周杰伦#耳机无卡翻唱#主歌"}, {"position": 10, "aweme_id": "7535300402302356770", "title": "有何不可#许嵩#无卡耳机翻唱#这歌只能夹才有感觉"}], "count_diff": -20}}, {"author_name": "傲娇的土豆泥", "sec_uid": "MS4wLjABAAAAecFfuvghcvk2j51X43PocEVIQ1V7zhb3e56OKeROpPOFxQpOY3MRpD0us8Myag1B", "original_api": {"works_count": 40, "top10_ids": ["7540627480866327844", "7539411309755862313", "7539034943658265892", "7537296074386640170", "7535787930129386791", "7530497933998624043", "7513799323743292683", "7513444050578623785", "7513240746197011724", "7501338100909886746"]}, "tikhub_api": {"works_count": 20, "top10_ids": ["7542536676913384758", "7540627480866327844", "7539411309755862313", "7539034943658265892", "7537296074386640170", "7535787930129386791", "7530497933998624043", "7513799323743292683", "7513444050578623785", "7513240746197011724"]}, "differences": {"missing_in_original": [{"position": 1, "aweme_id": "7542536676913384758", "title": ""}], "missing_in_tikhub": [{"position": 10, "aweme_id": "7501338100909886746", "title": ""}], "count_diff": -20}}, {"author_name": "XUAN", "sec_uid": "MS4wLjABAAAAwpHR75w2zqUsQPCSmgnr6vd5jwXh_m5SDW5O8AHCdgY", "original_api": {"works_count": 39, "top10_ids": ["7490965914012093748", "7410266785326320934", "7537188666289589504", "7534994641708404003", "7534186242079264015", "7528958290069064960", "7521462756505210112", "7516514111900339471", "7514541411757460788", "7513233436950744360"]}, "tikhub_api": {"works_count": 21, "top10_ids": ["7490965914012093748", "7410266785326320934", "7537188666289589504", "7534994641708404003", "7534186242079264015", "7528958290069064960", "7521462756505210112", "7516514111900339471", "7514541411757460788", "7513233436950744360"]}, "differences": {"missing_in_original": [], "missing_in_tikhub": [], "count_diff": -18}}, {"author_name": "炮炮_official", "sec_uid": "MS4wLjABAAAAfA7mFRjvslzD6HiOILEIIjQgp5DQSB9MNkLa1eE76PRSYTYe-RPAQpt6zKHtS-W_", "original_api": {"works_count": 5, "top10_ids": ["7539389905556032826", "7533261745066937660", "7518684782240386354", "7517121737676524826", "7507556645447781691"]}, "tikhub_api": {"works_count": 7, "top10_ids": ["7543156364194843963", "7542174455033597241", "7539389905556032826", "7533261745066937660", "7518684782240386354", "7517121737676524826", "7507556645447781691"]}, "differences": {"missing_in_original": [{"position": 1, "aweme_id": "7543156364194843963", "title": "#黑皮 #壮熊 #熊狒"}, {"position": 2, "aweme_id": "7542174455033597241", "title": "接我的船来了#壮熊 #阳光男孩 #一起看风景 #小叔叔"}], "missing_in_tikhub": [], "count_diff": 2}}, {"author_name": "四季豆", "sec_uid": "MS4wLjABAAAAS7i6ZMiSTtJBg9G48GxqilPA6BDAn24sQkgSbzclVF_BmDOh3LXUihXD0uCtNZeE", "original_api": {"works_count": 12, "top10_ids": ["7540503153461611834", "7539703933125594428", "7537116059548405051", "7541245537761135932", "7540871119815085369", "7540137116581678394", "7537853047444294969", "7537629389329567035", "7537281679149272378", "7536901342770990394"]}, "tikhub_api": {"works_count": 17, "top10_ids": ["7540503153461611834", "7539703933125594428", "7537116059548405051", "7543471895406382393", "7542732057413586234", "7542445786404113721", "7542000592983805242", "7541616729441799482", "7541245537761135932", "7540871119815085369"]}, "differences": {"missing_in_original": [{"position": 4, "aweme_id": "7543471895406382393", "title": "秋风卷走最后一片枫，想卷走我和菲菲的誓言，回忆像冷却的猪油，封住了这个秋天，我们笨拙的爱终究要归于这案板上的秋凉。#肉肉 #熊 #抖音流量扶持"}, {"position": 5, "aweme_id": "7542732057413586234", "title": "我讨厌的给它一巴掌，讨厌我的给它两巴掌。#熊#生气"}, {"position": 6, "aweme_id": "7542445786404113721", "title": "生病想拍一条下楼买药的，拍了几十分钟不疼了。#熊 #工装"}, {"position": 7, "aweme_id": "7542000592983805242", "title": "周日才睡醒，身体好很多了，应该明后天就完全恢复了#熊 #圆脸 #寸头"}, {"position": 8, "aweme_id": "7541616729441799482", "title": "身体好一点了，但还是头疼，拿三年前照片糊弄一下#熊 #十亿流量扶持计划 #毛肚"}], "missing_in_tikhub": [{"position": 6, "aweme_id": "7540137116581678394", "title": "让我更难过的是，你都那样对我了，我还是爱你#熊 #熊狒"}, {"position": 7, "aweme_id": "7537853047444294969", "title": "第一张是之前抖音头像，最后一张是支付宝头像#熊 #那些年"}, {"position": 8, "aweme_id": "7537629389329567035", "title": "如果不是距离原因，我们会不会有结果。#熊 #想你了 #抖音小助手dou上热门"}, {"position": 9, "aweme_id": "7537281679149272378", "title": "#早睡早起 #熊  最近几天都是10.30左右就睡了，至于为啥只睡了3个小时，是因为后半夜手表没电了"}, {"position": 10, "aweme_id": "7536901342770990394", "title": "#熊熊#🌈 #无不良引导  拍的嘴都歪了，朋友说相机不会骗人，我就长这样。"}], "count_diff": 5}}, {"author_name": "啊 Q", "sec_uid": "MS4wLjABAAAANa04jr3tgQJT2Mvd1qLjThNCr4brHYDfBLg0yItUR-s", "original_api": {"works_count": 42, "top10_ids": ["6872637577829321991", "6870061595242908936", "6854859355121143048", "7525358188557208867", "7521755076411542819", "7519479863490415872", "7519171523267153187", "7516462528235769140", "7508651488874433844", "6988104065402047757"]}, "tikhub_api": {"works_count": 23, "top10_ids": ["6872637577829321991", "6870061595242908936", "6854859355121143048", "7525358188557208867", "7521755076411542819", "7519479863490415872", "7519171523267153187", "7516462528235769140", "7508651488874433844", "6988104065402047757"]}, "differences": {"missing_in_original": [], "missing_in_tikhub": [], "count_diff": -19}}, {"author_name": "張家漢", "sec_uid": "MS4wLjABAAAA9xP5UhhZxRDljEFQc6wi32BnhJPwcvn9vZIwVOJtO5o", "original_api": {"works_count": 28, "top10_ids": ["7404322116780199178", "7488624401689152820", "7388836277707148554", "7538002615550020864", "7536578179574779136", "7536207579136544000", "7535463461271325987", "7535088848403238178", "7533892252269530403", "7533509569441418536"]}, "tikhub_api": {"works_count": 22, "top10_ids": ["7404322116780199178", "7488624401689152820", "7388836277707148554", "7543217135524072756", "7538002615550020864", "7536578179574779136", "7536207579136544000", "7535463461271325987", "7535088848403238178", "7533892252269530403"]}, "differences": {"missing_in_original": [{"position": 4, "aweme_id": "7543217135524072756", "title": "解放1️⃣下#胖熊 #圆寸"}], "missing_in_tikhub": [{"position": 10, "aweme_id": "7533509569441418536", "title": "今天 也是第四天。#top"}], "count_diff": -6}}, {"author_name": "李小虎", "sec_uid": "MS4wLjABAAAA5RbJZqEJqCaRpyp8fQgDjtbut3yOg25eudSWJmRYbnpW023ljr-1jTWh2Re0fJ79", "original_api": {"works_count": 35, "top10_ids": ["7493245974010203404", "7539517990211947814", "7538692354916994350", "7535807641081400626", "7535471154972609801", "7532883454385081609", "7530598619638369563", "7530168786453007667", "7529872119145942310", "7528599070546660658"]}, "tikhub_api": {"works_count": 21, "top10_ids": ["7493245974010203404", "7543660682345090355", "7539517990211947814", "7538692354916994350", "7535807641081400626", "7535471154972609801", "7532883454385081609", "7530598619638369563", "7530168786453007667", "7529872119145942310"]}, "differences": {"missing_in_original": [{"position": 2, "aweme_id": "7543660682345090355", "title": "#抖音ai创作"}], "missing_in_tikhub": [{"position": 10, "aweme_id": "7528599070546660658", "title": "敬这破败不堪的自己和乱七八糟的生活#去看海 #腿毛 #纪录美好生活 #黑皮"}], "count_diff": -14}}, {"author_name": "陈沁扬", "sec_uid": "MS4wLjABAAAAHHwJ_IyJ7U75LOGDJ33EHRR01U5q_9wAvsSbxV0uT8Ig3d4avWGcBDGPnu2l1-iu", "original_api": {"works_count": 42, "top10_ids": ["7539205972993510665", "7110490387940134151", "6927270442075966724", "7537306751700684041", "7533190075032620314", "7531388310498594058", "7529908667828047142", "7525296381546138907", "7525288095446748443", "7524331367670549811"]}, "tikhub_api": {"works_count": 22, "top10_ids": ["7539205972993510665", "7110490387940134151", "6927270442075966724", "7543620449344785690", "7542548366904659246", "7537306751700684041", "7533190075032620314", "7531388310498594058", "7529908667828047142", "7525296381546138907"]}, "differences": {"missing_in_original": [{"position": 4, "aweme_id": "7543620449344785690", "title": "家庭音乐工作室 #养老俱乐部 #陈沁扬#编曲 #音乐制作 #音乐工作室"}, {"position": 5, "aweme_id": "7542548366904659246", "title": "我弹这4个和弦你有什么感觉？ #和弦的魅力 #编曲 #音乐制作 #陈沁扬"}], "missing_in_tikhub": [{"position": 9, "aweme_id": "7525288095446748443", "title": "每次都是叼着雪茄瞎打鼓 燥起来吧，少年💙#即兴乱舞嗨起来"}, {"position": 10, "aweme_id": "7524331367670549811", "title": "《者.布鲁斯.来女》 歌手2025 者来女"}], "count_diff": -20}}, {"author_name": "富贵的勋勋", "sec_uid": "MS4wLjABAAAAQ034kqCgXma_pN4DhLkUm-AzBKDcenk13-LJCCb7ngw", "original_api": {"works_count": 39, "top10_ids": ["7491837737394375951", "7486489271868591360", "7539933591121235257", "7528292989220703546", "7525719343922842940", "7522521173450050873", "7522144352023530812", "7518015697286516026", "7516956378248219962", "7516553476827172154"]}, "tikhub_api": {"works_count": 21, "top10_ids": ["7491837737394375951", "7486489271868591360", "7543204752873737529", "7543164789557644601", "7539933591121235257", "7528292989220703546", "7525719343922842940", "7522521173450050873", "7522144352023530812", "7518015697286516026"]}, "differences": {"missing_in_original": [{"position": 3, "aweme_id": "7543204752873737529", "title": "感觉对了就行…"}, {"position": 4, "aweme_id": "7543164789557644601", "title": "#日常分享"}], "missing_in_tikhub": [{"position": 9, "aweme_id": "7516956378248219962", "title": "#猝不及防 #内容太过真实"}, {"position": 10, "aweme_id": "7516553476827172154", "title": "没有父母的撑腰、万事只能靠自己…"}], "count_diff": -18}}, {"author_name": "赵er博", "sec_uid": "MS4wLjABAAAAxDzKWrBhtvDxjswqZsXzcSvlBxRsafu8yapQpO6vr_I", "original_api": {"works_count": 34, "top10_ids": ["7519518488118906122", "7262373516773788967", "7287791521166789951", "7537341588247809290", "7534610713109777691", "7532042527450352946", "7521304165081468211", "7516483194981371145", "7501236770479967524", "7491199625148288310"]}, "tikhub_api": {"works_count": 22, "top10_ids": ["7519518488118906122", "7262373516773788967", "7287791521166789951", "7537341588247809290", "7534610713109777691", "7532042527450352946", "7521304165081468211", "7516483194981371145", "7513134710941027642", "7501236770479967524"]}, "differences": {"missing_in_original": [{"position": 9, "aweme_id": "7513134710941027642", "title": "熊熊篮球队，出击！🏀。#篮球 #熊 #txl"}], "missing_in_tikhub": [{"position": 10, "aweme_id": "7491199625148288310", "title": "今天发点没发过的剩图🥹#黑皮体育生 #185 #熊狒 #熊熊"}], "count_diff": -12}}, {"author_name": "如梦苏州", "sec_uid": "MS4wLjABAAAAJQj8xVpBH2Wr7PPTUJkyQjnJ7xhzBQSb5MdCQ72Suj7RsvFMAtM6WHEGw8q_ohXm", "original_api": {"works_count": 42, "top10_ids": ["7521706884572433699", "7532048865656589568", "7512669223534316852", "7541419114322529570", "7540959413181713716", "7540913990173674804", "7540507365378133282", "7540225963859414287", "7539806075592658228", "7539358088538721570"]}, "tikhub_api": {"works_count": 23, "top10_ids": ["7521706884572433699", "7532048865656589568", "7512669223534316852", "7543505870862159139", "7543216682937994511", "7543092865317129524", "7542888492016471331", "7542416333616581928", "7542125606839962920", "7541765393170681103"]}, "differences": {"missing_in_original": [{"position": 4, "aweme_id": "7543505870862159139", "title": "苏州之眼摩天轮双人票准备好了，七夕可用，不用随时退，还在等什么 #浪漫摩天轮 #金鸡湖浪漫摩天轮 #七夕一定要坐摩天轮"}, {"position": 5, "aweme_id": "7543216682937994511", "title": "苏州高新区狮山公园的水幕瀑布也太美了！人少景美，地铁还能直达。#苏州 #周末去哪儿玩 #人少景美 #七夕去哪玩"}, {"position": 6, "aweme_id": "7543092865317129524", "title": "鹿北湖畔七夕烟花秀单人只要19.9，性价比真的高！#苏州 #七夕 #烟花秀"}, {"position": 7, "aweme_id": "7542888492016471331", "title": "苏州白塔公园荷花市集已接近尾声，再见就是明年六月啦！还没打卡的抓紧时间了，错过又等一年。#苏州 #荷花市集 #白塔公园 #荷花花束"}, {"position": 8, "aweme_id": "7542416333616581928", "title": "这个七夕怎么过？当然是来苏州桃花坞七夕游园会逛市集，赏灯会，还有非遗盒子灯、麒麟舞表演！就在8月29-31日，精彩不能错过！#苏州 #七夕 #桃花坞游园会 #盒子灯 #麒麟舞"}, {"position": 9, "aweme_id": "7542125606839962920", "title": "七夕浪漫约会好去处！当东山“小洱海”遇上橘子海日落，也太美了吧！#苏州 #日落 #周末去哪儿 #拍照打卡"}, {"position": 10, "aweme_id": "7541765393170681103", "title": "苏州人的嘴巴是真紧啊！七夕阳澄半岛乐园烟花连发三天！#苏州 #阳澄湖半岛乐园 #烟花秀"}], "missing_in_tikhub": [{"position": 4, "aweme_id": "7541419114322529570", "title": "小黄鸭主题乐园太湖新城店开业啦！一票畅玩40多无动力游玩！还包含玻璃水滑！1.1m以下儿童免费！#苏州 #小黄鸭主题乐园 #亲子遛娃好去处 #苏州小黄鸭主题乐园太湖新城店"}, {"position": 5, "aweme_id": "7540959413181713716", "title": "昆山的宝妈们，你们不知道吧？昆山新开了一家海洋馆，全天海狮、国风变脸表演，还可以投喂海豹，小兔子，海龟等。#苏州 #海洋馆 #亲子游玩好去处 #周末去哪儿 #海洋动物"}, {"position": 6, "aweme_id": "7540913990173674804", "title": "摩天轮七夕双人特惠票178，我想七夕那天，苏州金鸡湖摩天轮上面应该会站满幸福的人吧！#苏州 #摩天轮 #金鸡湖 #周末去哪儿玩"}, {"position": 7, "aweme_id": "7540507365378133282", "title": "姑苏城外寒山寺，夜半钟声到客船。终于来到了小学课本中的寒山寺了！#苏州 #寒山寺 #旅游攻略 #周末去哪儿"}, {"position": 8, "aweme_id": "7540225963859414287", "title": "在阳台就能看到的烟花秀也太美了吧！就在龙之梦太湖古镇大酒店！ #万岁山真的来江浙沪了 #太湖古镇演艺狂欢季 #烟花八月上湖州 #在太湖古镇的花火中穿越千年"}, {"position": 9, "aweme_id": "7539806075592658228", "title": "龙之梦太湖古镇的烟花也太美了！每天两场高空低空烟花，还有打铁花火壶等各种精彩表演！#龙之梦太湖古镇大酒店 #万岁山真的来江浙沪了 #太湖古镇演艺狂欢季 #立秋第一场花火盛宴的浪漫感 #烟花八月上湖州"}, {"position": 10, "aweme_id": "7539358088538721570", "title": "独墅湖一到周末便成为了大家向往的自由！#苏州 #独墅湖 #桨板"}], "count_diff": -19}}, {"author_name": "爱吃鸡腿(づ ●─● )づ", "sec_uid": "MS4wLjABAAAA-R8B8djIEwlQMEyDZjZDPQwmyYu8qWlIN2DSQ5CnGiwR8AxEAYOC4KGMYp8Ldhbl", "original_api": {"works_count": 38, "top10_ids": ["7539450435221900590", "7537961674649783603", "7508690575400553755", "7540949650389650726", "7540178928763014409", "7539100170618342706", "7538352902013095194", "7536838081430867251", "7534632380011695386", "7533867906141048114"]}, "tikhub_api": {"works_count": 21, "top10_ids": ["7539450435221900590", "7537961674649783603", "7508690575400553755", "7543666864405761331", "7542556881777593650", "7542127265153469742", "7541650671948270886", "7540949650389650726", "7540178928763014409", "7539100170618342706"]}, "differences": {"missing_in_original": [{"position": 4, "aweme_id": "7543666864405761331", "title": "#憨憨日常 我戒不掉你，像戒不掉呼吸~@阿源 \n这是阿源剪的哦，祝大家七夕快乐"}, {"position": 5, "aweme_id": "7542556881777593650", "title": "#憨憨跳舞 这是谁要看到跳舞呀，来评论区认领一下(づ ●─● )づ"}, {"position": 6, "aweme_id": "7542127265153469742", "title": "#憨憨日常记录 #憨憨男孩 有点无聊，水个视频(๑><๑），虽然水，但是还是水(๑><๑），所以不要在评论区说我水了，我知道我真的很水\\^O^/ 恭喜你们看到我没有戴眼镜的样子，是真的不好看╯﹏╰"}, {"position": 7, "aweme_id": "7541650671948270886", "title": "#不要搞了好吧 #谁还不会脸势舞 哈哈哈，看着好好玩(๑><๑）"}], "missing_in_tikhub": [{"position": 7, "aweme_id": "7538352902013095194", "title": "@森吃米饭🌿 \n森:8月14日\n我的粉丝好像变的没那么开朗了，感觉情绪有点低落，喂了一盒椿药，希望能开心起来。#熊熊 \n腿:8月14日\n粉丝们，等我有钱了，我要买俩个鸡腿，一个你们看着我吃，一个我吃给你们看。#憨憨日常"}, {"position": 8, "aweme_id": "7536838081430867251", "title": "#憨憨日常 #憨憨男孩 #内向社恐 私信主包要微信，主包说我家太穷了，门都没有(๑><๑）🤠"}, {"position": 9, "aweme_id": "7534632380011695386", "title": "#杀青梗 #憨憨日常 尽力了，热死，实在太快了，手脚跟不上#手脚不协调的我 #side"}, {"position": 10, "aweme_id": "7533867906141048114", "title": "#憨憨日常 #憨憨男孩 都说我圆圆胖胖跟猪相似，在这样诋毁我真的放肆(๑><๑）哈哈哈，摆烂了(￣y▽￣)~*捂嘴偷笑"}], "count_diff": -17}}, {"author_name": "虎胜🐯", "sec_uid": "MS4wLjABAAAAVX6KiwKRTiU91ePICU5E3e3UUW7XMhPf13ScLQneEf2fKyUPLLTkwl_stX11I9KB", "original_api": {"works_count": 40, "top10_ids": ["7394311933387722018", "7467124869893803314", "7537686033565011219", "7536919144232897846", "7536533247334911268", "7535384671602052388", "7532487998559636755", "7531010045996567871", "7529117894664310055", "7528657423335902507"]}, "tikhub_api": {"works_count": 22, "top10_ids": ["7394311933387722018", "7467124869893803314", "7542113516884020534", "7537686033565011219", "7536919144232897846", "7536533247334911268", "7535384671602052388", "7532487998559636755", "7531010045996567871", "7529117894664310055"]}, "differences": {"missing_in_original": [{"position": 3, "aweme_id": "7542113516884020534", "title": "手臂怎么能上40 #健身 #日常生活 #健身房日常 #增肌 #毫无训练痕迹"}], "missing_in_tikhub": [{"position": 10, "aweme_id": "7528657423335902507", "title": "北京第二站-天安门。#我爱我的祖国 #厉害了我的国🇨🇳 #五星红旗🇨🇳 #天安门 #天安门广场"}], "count_diff": -18}}, {"author_name": "楚桥说美澳", "sec_uid": "MS4wLjABAAAAvEVC4A2SxQXub4akTl1RrnuBFfvatHl7L19ZbK2U1LI", "original_api": {"works_count": 42, "top10_ids": ["7137883041585827072", "7141206488680369408", "7137564965455645986", "7540224622189972794", "7539797488426454332", "7537208859065175355", "7535716826136022329", "7531999657213201723", "7530160268450729274", "7529796647673728314"]}, "tikhub_api": {"works_count": 22, "top10_ids": ["7137883041585827072", "7141206488680369408", "7137564965455645986", "7540224622189972794", "7539797488426454332", "7537208859065175355", "7535716826136022329", "7531999657213201723", "7530160268450729274", "7529796647673728314"]}, "differences": {"missing_in_original": [], "missing_in_tikhub": [], "count_diff": -20}}, {"author_name": "？？？？？？？？", "sec_uid": "MS4wLjABAAAAnmc1INCnjVUDfuHueLBPoQk9iex4wu3XLs-DC-bPAZw", "original_api": {"works_count": 39, "top10_ids": ["7541006314685549862", "7539154641906224422", "7533205375719361843", "7530628268048977203", "7518002477527764262", "7517209835856497930", "7507992782410632486", "7502806084328115497", "7492810725291855143", "7491617495854353675"]}, "tikhub_api": {"works_count": 20, "top10_ids": ["7542105480752942387", "7541006314685549862", "7539154641906224422", "7533205375719361843", "7530628268048977203", "7518002477527764262", "7517209835856497930", "7507992782410632486", "7502806084328115497", "7492810725291855143"]}, "differences": {"missing_in_original": [{"position": 1, "aweme_id": "7542105480752942387", "title": "^_−☆ #分享 #微醺"}], "missing_in_tikhub": [{"position": 10, "aweme_id": "7491617495854353675", "title": "#ootd #霸总"}], "count_diff": -19}}, {"author_name": "臭小子", "sec_uid": "MS4wLjABAAAAE6fnfcspYaQE-PKSQZ2d3w8YKlQsOVdivUKIqs28cFCWuCiFxFs7fYafsH-INTSV", "original_api": {"works_count": 8, "top10_ids": ["7540856229116742972", "7536423224793500988", "7535780198471028025", "7530505050557664570", "7528272608455724346", "7524225346902068537", "7521582597098343737", "7498927491538390299"]}, "tikhub_api": {"works_count": 10, "top10_ids": ["7543461489340992827", "7543120938007629114", "7540856229116742972", "7536423224793500988", "7535780198471028025", "7530505050557664570", "7528272608455724346", "7524225346902068537", "7521582597098343737", "7498927491538390299"]}, "differences": {"missing_in_original": [{"position": 1, "aweme_id": "7543461489340992827", "title": "干啥啥不行 干饭第一名#小叔叔 #圆寸 #熊熊"}, {"position": 2, "aweme_id": "7543120938007629114", "title": "爱情是流动的\n不由人的💔#生活是一种态度 #小叔叔 #曾经的少年已不再年少"}], "missing_in_tikhub": [], "count_diff": 2}}, {"author_name": "张明烁", "sec_uid": "MS4wLjABAAAArwZ9zU-jCbjsY4RYSiCSCMGQQYd6q2N-MJ2kT8_9vVjKmkrP-nCH9kS-_ehrGvSF", "original_api": {"works_count": 40, "top10_ids": ["7533984451274755347", "7541267032869096745", "7540882936145644857", "7540628103463996735", "7539795021127732516", "7538411069695135019", "7536575161341644086", "7534728760609721660", "7532570198763687227", "7530653719668591914"]}, "tikhub_api": {"works_count": 20, "top10_ids": ["7533984451274755347", "7543692374716173610", "7542244277931363623", "7541881437701066047", "7541267032869096745", "7540882936145644857", "7540628103463996735", "7539795021127732516", "7538411069695135019", "7536575161341644086"]}, "differences": {"missing_in_original": [{"position": 2, "aweme_id": "7543692374716173610", "title": "七夕快乐"}, {"position": 3, "aweme_id": "7542244277931363623", "title": "看海的约定…"}, {"position": 4, "aweme_id": "7541881437701066047", "title": "已经猜不到你在干什么了，我们也好久没有面对面说过话了…"}], "missing_in_tikhub": [{"position": 8, "aweme_id": "7534728760609721660", "title": "。。"}, {"position": 9, "aweme_id": "7532570198763687227", "title": "最近在干嘛"}, {"position": 10, "aweme_id": "7530653719668591914", "title": "晚安"}], "count_diff": -20}}, {"author_name": "笑妄川", "sec_uid": "MS4wLjABAAAAfZwf0JKJ31dPaFmpOFudMKPLdAgzog5xK68xYn4UNAo", "original_api": {"works_count": 40, "top10_ids": ["7519370262796209467", "7511567700809239868", "7538752458165718330", "7538616801886932281", "7537563006079913276", "7537156588790009147", "7536204440739761466", "7536048216417504570", "7535357652814941498", "7533448271081557307"]}, "tikhub_api": {"works_count": 22, "top10_ids": ["7519370262796209467", "7511567700809239868", "7538752458165718330", "7538616801886932281", "7537563006079913276", "7537156588790009147", "7536204440739761466", "7536048216417504570", "7535357652814941498", "7533448271081557307"]}, "differences": {"missing_in_original": [], "missing_in_tikhub": [], "count_diff": -18}}, {"author_name": "小北", "sec_uid": "MS4wLjABAAAA8LSMSqKOaiP21U7XlZInia8hCzGKlLOdlMVNeS685iOe9gUkbhRob52RIfbdvRKP", "original_api": {"works_count": 19, "top10_ids": ["7526693135155023145", "7522643640692788521", "7541119333448289555", "7539708905548598591", "7538223573040270655", "7536730742506163492", "7535601396474694951", "7534903610095160617", "7533774100671548735", "7533036580073966911"]}, "tikhub_api": {"works_count": 20, "top10_ids": ["7526693135155023145", "7522643640692788521", "7542632712394886442", "7541119333448289555", "7539708905548598591", "7538223573040270655", "7536730742506163492", "7535601396474694951", "7534903610095160617", "7533774100671548735"]}, "differences": {"missing_in_original": [{"position": 3, "aweme_id": "7542632712394886442", "title": "二哥要进城打工了#肉壮"}], "missing_in_tikhub": [{"position": 10, "aweme_id": "7533036580073966911", "title": "这些天有台风，会不会刮个小可爱来我身边😂#肉壮"}], "count_diff": 1}}, {"author_name": "王晓", "sec_uid": "MS4wLjABAAAAIJIc0d8k-A5DoJ9u6mGn0uRo7ZQhZT4Ka7MsH2ek5NU", "original_api": {"works_count": 40, "top10_ids": ["7540978181504404782", "7540909602616659227", "7540296288391318822", "7536886536270597427", "7534247622422842674", "7534035081472838939", "7533622488580853018", "7533410052323151150", "7532767166216949043", "7530186142843686153"]}, "tikhub_api": {"works_count": 20, "top10_ids": ["7543676390546607386", "7543666794122251529", "7543661753750932762", "7543652525192056091", "7543579619581938982", "7543302538422963506", "7543279981347147054", "7543271179320331571", "7543219168624364826", "7542887219174837513"]}, "differences": {"missing_in_original": [{"position": 1, "aweme_id": "7543676390546607386", "title": "#抖音ai创作"}, {"position": 2, "aweme_id": "7543666794122251529", "title": "#AI型男姿态#小叔叔"}, {"position": 3, "aweme_id": "7543661753750932762", "title": "#抖音ai创作"}, {"position": 4, "aweme_id": "7543652525192056091", "title": "#抖音ai创作 #小叔叔 #圆寸"}, {"position": 5, "aweme_id": "7543579619581938982", "title": "#抖音ai创作 #圆寸 #小叔叔"}, {"position": 6, "aweme_id": "7543302538422963506", "title": "#vlog日常 #小叔叔 晚安晚安💤"}, {"position": 7, "aweme_id": "7543279981347147054", "title": "#抖音ai创作 #圆寸 #小叔叔"}, {"position": 8, "aweme_id": "7543271179320331571", "title": "#抖音ai创作 🤣"}, {"position": 9, "aweme_id": "7543219168624364826", "title": "#抖音ai创作 #小叔叔"}, {"position": 10, "aweme_id": "7542887219174837513", "title": "#抖音ai创作 #圆寸 #小叔叔"}], "missing_in_tikhub": [{"position": 1, "aweme_id": "7540978181504404782", "title": "#抖音ai创作 #每日分享 #小叔叔"}, {"position": 2, "aweme_id": "7540909602616659227", "title": "#vlog日常 #憨憨日常 #小叔叔 摇头晃脑，差点坐过站🤣"}, {"position": 3, "aweme_id": "7540296288391318822", "title": "#抖音ai创作 #小叔叔 #圆寸"}, {"position": 4, "aweme_id": "7536886536270597427", "title": "#小叔叔 #寸头 #络腮胡"}, {"position": 5, "aweme_id": "7534247622422842674", "title": "#vlog日常 #小叔叔"}, {"position": 6, "aweme_id": "7534035081472838939", "title": "#日常生活 #吃喝玩乐 开心🥳"}, {"position": 7, "aweme_id": "7533622488580853018", "title": "#vlog日常 #小叔叔 #根本停不下来"}, {"position": 8, "aweme_id": "7533410052323151150", "title": "#日常生活 #小叔叔"}, {"position": 9, "aweme_id": "7532767166216949043", "title": "#小叔叔 #日常生活 #日常发呆"}, {"position": 10, "aweme_id": "7530186142843686153", "title": "#日常生活 #小叔叔"}], "count_diff": -20}}, {"author_name": "宇宇黑黑", "sec_uid": "MS4wLjABAAAAdGAH_4wJo5ciOVHL_GQrn7tl1ZC4Kdc1nmOcWEFY5Bg", "original_api": {"works_count": 42, "top10_ids": ["7436996806631755048", "7432929254532304128", "7541429217029573923", "7541425264422898978", "7541059282336337186", "7541046867213176098", "7540534231019588898", "7540261612310580495", "7539888902066588963", "7539854091246898484"]}, "tikhub_api": {"works_count": 22, "top10_ids": ["7436996806631755048", "7432929254532304128", "7543541090490780963", "7543142616617323810", "7543131822616759567", "7542835950046563618", "7542835572991003956", "7542834576252488975", "7542546302519233826", "7542118582039055651"]}, "differences": {"missing_in_original": [{"position": 3, "aweme_id": "7543541090490780963", "title": "#AI型男卧床写真 #抖音ai创作 #你的男朋友 #小叔叔"}, {"position": 4, "aweme_id": "7543142616617323810", "title": "#抖音ai创作 #小叔叔"}, {"position": 5, "aweme_id": "7543131822616759567", "title": "#AI温馨午睡写真 #抖音ai创作"}, {"position": 6, "aweme_id": "7542835950046563618", "title": "#AI雨夜失意照 #抖音ai创作 #小叔叔"}, {"position": 7, "aweme_id": "7542835572991003956", "title": "#AI酷感工装写真 #抖音ai创作 #小叔叔"}, {"position": 8, "aweme_id": "7542834576252488975", "title": "#沙滩壮男随拍 #小叔叔 #荷尔蒙"}, {"position": 9, "aweme_id": "7542546302519233826", "title": "#小叔叔 #健身 #黑皮 #荷尔蒙 #健身搭档"}, {"position": 10, "aweme_id": "7542118582039055651", "title": "#AI浴后朦胧写真"}], "missing_in_tikhub": [{"position": 3, "aweme_id": "7541429217029573923", "title": "#小叔叔 #黑皮 #原味"}, {"position": 4, "aweme_id": "7541425264422898978", "title": "#AI森林精灵大片 #抖音ai创作"}, {"position": 5, "aweme_id": "7541059282336337186", "title": "#AI酷帅随拍 #抖音ai创作"}, {"position": 6, "aweme_id": "7541046867213176098", "title": "#AI健硕写真 #抖音ai创作"}, {"position": 7, "aweme_id": "7540534231019588898", "title": "#AI中年壮熊随拍 #抖音ai创作"}, {"position": 8, "aweme_id": "7540261612310580495", "title": "#AI白净胡子大片 #抖音ai创作"}, {"position": 9, "aweme_id": "7539888902066588963", "title": "#黑皮 #寸头 #原味"}, {"position": 10, "aweme_id": "7539854091246898484", "title": "#AI风拂侧颜照 #抖音ai创作 #抖音全新ai写真 #想跪下来求自己别玩写真了"}], "count_diff": -20}}, {"author_name": "浪琴～小李", "sec_uid": "MS4wLjABAAAAEg4fW8DWFdwRdaewSyGqVSmhOPcQjf6klt7M1j3sw0I", "original_api": {"works_count": 41, "top10_ids": ["7525780120134487356", "7491821556172131619", "7511709016779738426", "7540929096902167866", "7540449035464002876", "7539174595204599055", "7538020889725439272", "7537924032243895609", "7537196576789515578", "7532084383966170426"]}, "tikhub_api": {"works_count": 22, "top10_ids": ["7525780120134487356", "7491821556172131619", "7511709016779738426", "7543596579912256803", "7543109258411068712", "7542841744670657832", "7542840817719889204", "7542523172894362920", "7542522782439001344", "7542059440570354944"]}, "differences": {"missing_in_original": [{"position": 4, "aweme_id": "7543596579912256803", "title": "你们收到七夕的礼物了嘛。工资发一万二\n敢给对象买一万七的手表\n这就是我们的零零后#礼物推荐 #浪琴#黛绰维纳手表 #七夕礼物 #同城发现"}, {"position": 5, "aweme_id": "7543109258411068712", "title": "小姐姐，七夕给男朋友准备的礼物。一枚浪琴军旗手表。小姐姐人在外地。出于信任后天七夕给小姐姐安排✈️到南京。在这里也非常感谢抖音的小伙伴对我的认可。#记录一下 #浪琴#浪琴军旗 #七夕男表推荐 #宿迁浪琴销冠李方玉"}, {"position": 6, "aweme_id": "7542841744670657832", "title": "七夕选择一个什么样的礼物又能打动对方，又能显出新意#礼物推荐 #浪琴#心月#七夕手表 #女友礼物"}, {"position": 7, "aweme_id": "7542840817719889204", "title": "七夕礼物怎么选，浪琴名匠#礼物推荐 #浪琴#名匠#七夕礼物推荐 #手表"}, {"position": 8, "aweme_id": "7542523172894362920", "title": "七夕香薰手工活动来啦，关注我报名参加#浪琴#工作碎片 #强烈推荐 #礼物推荐"}, {"position": 9, "aweme_id": "7542522782439001344", "title": "浪琴心月，七夕节不知道怎么给我冲这个#浪琴 #七夕手表 #心月 #礼物推荐 #腕表分享"}, {"position": 10, "aweme_id": "7542059440570354944", "title": "浪琴名匠四针月相女表，七夕还不知道选什么礼物了，快给我看过来做功课#礼物推荐 #浪琴#名匠四针月相 #手表#七夕手表"}], "missing_in_tikhub": [{"position": 4, "aweme_id": "7540929096902167866", "title": "七夕礼物手表推荐来啦。#宿迁金鹰浪琴#浪琴#名匠#七夕礼物 #宿迁浪琴销冠李方玉"}, {"position": 5, "aweme_id": "7540449035464002876", "title": "感谢抖音，让我在线上遇到这么多顾客，也非常感谢大家的支持，此视频立在这儿，只要是我的粉丝超声波清洗消磁，测防水，都免费。#直播录屏分享 #强烈推荐 #浪琴 #浪琴柜哥 #宿迁浪琴销冠李方玉"}, {"position": 6, "aweme_id": "7539174595204599055", "title": "常州进球。太牛啦，终于进球啦，必须要发一个#常州进球 #苏超 #足球"}, {"position": 7, "aweme_id": "7538020889725439272", "title": "常州你再输就去镇江金山寺放水给镇江淹了#比赛现场 #苏超#苏超联赛 #镇江vs常州 #足球"}, {"position": 8, "aweme_id": "7537924032243895609", "title": "苏超垫底之战来啦！竞争激烈，你站哪队？哈哈哈！！常州队赢了？直奔恐龙园，给恐龙们表演个“比你们还猛”！镇江队赢了？速去金山寺，求菩萨下次让对手直接“被水漫”！#足球 #苏超 #苏超联赛 #苏超常州#快来围观"}, {"position": 9, "aweme_id": "7537196576789515578", "title": "不是票这么难抢吗？看个比赛好歹能看到票，大麦直接都看不到，你们都抢到票了吗？#常州vs镇江 #苏超抢票 #苏超 #苏超联赛 #上热搜"}, {"position": 10, "aweme_id": "7532084383966170426", "title": "苏超爆火！比赛时间+抢票攻略速看\n•8月2日19:30 镇江VS南通，镇江市体育会展中心，“体育大市口”小程序抢票\n•8月3日19:00 连云港VS泰州，连云港市体育中心，“看个比赛”APP/小程序，今天15点开票，24点关，没卖完明天12点再开\n8月3日19:30 盐城VS常州，盐城市体育中心，“看个比赛”能买，江苏银行APP每周二还有免费票抽 \n抢票小窍门：提前填好信息，定好闹钟，手速要快！ \n赶紧冲现场，感受这份热闹！#苏超 #看球攻略#苏超抢票"}], "count_diff": -19}}, {"author_name": "村长的足球", "sec_uid": "MS4wLjABAAAAkdTIY4mQ-l7LQFpNvS4t6maB-ipXD2Y3ndAFWJ7ADFE", "original_api": {"works_count": 40, "top10_ids": ["7541373984497143078", "7539202617294540078", "7538680226538097971", "7535818513216310538", "7535297051640581426", "7533966533409656114", "7533851753801600266", "7533810072906206491", "7533625380772400411", "7533419705143479578"]}, "tikhub_api": {"works_count": 20, "top10_ids": ["7543055558770052379", "7542836400225586483", "7542560573004385546", "7542299317077773578", "7542182226558946570", "7541373984497143078", "7539202617294540078", "7538680226538097971", "7535818513216310538", "7535297051640581426"]}, "differences": {"missing_in_original": [{"position": 1, "aweme_id": "7543055558770052379", "title": "大家都有了吗。#苏超 #南通VS苏州"}, {"position": 2, "aweme_id": "7542836400225586483", "title": "足球就是有种神奇的力量 一场小比赛也能汇集在一起#苏超#南通队"}, {"position": 3, "aweme_id": "7542560573004385546", "title": "完成初次体验 期待8.31一起狂欢#南通队 #苏超 #南鸟巢"}, {"position": 4, "aweme_id": "7542299317077773578", "title": "表现值得尊敬 虽败犹荣 #珂缔缘 #中乙"}, {"position": 5, "aweme_id": "7542182226558946570", "title": "王博文 加油，缝了六针。一场比赛，三辆救护车#珂缔缘 #冲甲组"}], "missing_in_tikhub": [{"position": 6, "aweme_id": "7533966533409656114", "title": "致敬我们战斗的勇士没们 #苏超 #南通队 #东洲球迷会"}, {"position": 7, "aweme_id": "7533851753801600266", "title": "阿镇 你们准备好了吗 克服一切困难 必须把三分带回来#南通VS镇江 #苏超 #南通队"}, {"position": 8, "aweme_id": "7533810072906206491", "title": "宝剑锋从磨砺出 加油吧 少年 #珂缔缘 #足球少年 #南通足球"}, {"position": 9, "aweme_id": "7533625380772400411", "title": "欢迎来猜比分 赢得奖品 #南通队 #苏超 #泰州vs镇江 #足球的魅力远远超过你的想象"}, {"position": 10, "aweme_id": "7533419705143479578", "title": "镇江 准备好了吗 出发 出发#南通队 #苏超 #镇江队 #镇江VS南通"}], "count_diff": -20}}, {"author_name": "小p的普通日常", "sec_uid": "MS4wLjABAAAAvwUrKKH7tY4Bk3H04n8GCH9C8hMukHc2kV2lV3N7EGQ", "original_api": {"works_count": 38, "top10_ids": ["7374034499014905114", "7165901823130324224", "7182600942901103887", "7513537019072548107", "7500226650685607219", "7499835652670786853", "7489086677793721638", "7486880771999878450", "7485333022695345434", "7479442277656579378"]}, "tikhub_api": {"works_count": 22, "top10_ids": ["7374034499014905114", "7165901823130324224", "7182600942901103887", "7543657917535325459", "7524924098087456046", "7513537019072548107", "7500226650685607219", "7499835652670786853", "7489086677793721638", "7486880771999878450"]}, "differences": {"missing_in_original": [{"position": 4, "aweme_id": "7543657917535325459", "title": "给自己放个假吧 #随拍 #寸头 #旅行 #放松一下自己 #南京"}, {"position": 5, "aweme_id": "7524924098087456046", "title": "#凉买摇 #凉买摇双人舞 #浅跳一下 晚上进入你身体的不一定是困意"}], "missing_in_tikhub": [{"position": 9, "aweme_id": "7485333022695345434", "title": "一切都是最好的安排 3月5号从南京坐飞机来了厦门，在厦门找了几个民宿住了1个多星期，每天去面试两家以上的公司，我带着之前的的工作经验和所学的专业来这边找工作，大部分公司要我去做ip孵化和内容转型也有的是做电商类的。终于在3月14号那天我收到了一个满意的offer，并且也花了一天时间找到了一个满意的公寓。在感情、工作和租房上感觉一切来的都很顺，希望往后也能够一直顺利下去，也希望我身边的人和朋友们也能一直万事顺利。\n#人夫感 #单眼皮 #寸头 #一切都是最好的安排 #一切都在慢慢变好"}, {"position": 10, "aweme_id": "7479442277656579378", "title": "你喜欢在哪里做 #作业 #抽象 #随拍 #一起看海 #我们俩 @七杯茶"}], "count_diff": -16}}]}
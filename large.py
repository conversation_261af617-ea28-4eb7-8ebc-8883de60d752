#!/usr/bin/env python3
"""
Large任务处理程序
专门处理数据库中标记为large的作品
"""

import asyncio
import json
import logging
import os
import re
import shutil
import subprocess
import time
import uuid
import zipfile
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Union, Any
import random

import httpx
from pyrogram import Client
from pyrogram.types import InputMediaPhoto, InputMediaVideo, InputMediaDocument, InputMediaAudio
from pyrogram.types import InlineKeyboardMarkup, InlineKeyboardButton
from pyrogram.enums import ParseMode
from pyrogram.errors import FloodWait
from supabase import create_client, Client as SupabaseClient
import io
from dotenv import load_dotenv
from PIL import Image
import pillow_heif

# 注册HEIF格式支持
pillow_heif.register_heif_opener()

# 加载环境变量
load_dotenv(override=True)

# =============== 日志配置 ===============
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('large_tasks.log', encoding='utf-8')
    ]
)
logger = logging.getLogger(__name__)

# =============== 配置加载 ===============
config = {}
required_keys = [
    'TELEGRAM_API_ID',
    'TELEGRAM_API_HASH',
    'BOT_TOKEN',
    'TELEGRAM_CHAT_ID',
    'TIKHUB_TOKEN',
    'SUPABASE_URL',
    'SUPABASE_KEY'
]

# 从环境变量加载配置
missing_keys = []
for key in required_keys:
    value = os.getenv(key)
    if value:
        config[key] = value
    else:
        missing_keys.append(key)

if missing_keys:
    logger.error(f"缺少必要的环境变量: {', '.join(missing_keys)}")
    logger.error("请设置所有必要的环境变量")
    exit(1)

# 配置项
TELEGRAM_API_ID = int(config['TELEGRAM_API_ID'])
TELEGRAM_API_HASH = config['TELEGRAM_API_HASH']
BOT_TOKEN = config['BOT_TOKEN']
SUPABASE_URL = config['SUPABASE_URL']
SUPABASE_KEY = config['SUPABASE_KEY']
TELEGRAM_CHAT_ID = int(config['TELEGRAM_CHAT_ID'])
TIKHUB_TOKEN = config['TIKHUB_TOKEN']

# 其他配置
DELETE_FILES = os.getenv('DELETE_FILES', 'True').lower() == 'true'
PROCESS_DELAY = int(os.getenv('PROCESS_DELAY', '3'))
BATCH_SIZE = int(os.getenv('BATCH_SIZE', '5'))  # 每批处理的任务数

# 初始化Supabase客户端
supabase: SupabaseClient = create_client(SUPABASE_URL, SUPABASE_KEY)

# 端口配置
API_PORTS = [5677, 5678, 5679, 5680]
current_port_index = 0

def get_current_port():
    """获取当前使用的端口"""
    return API_PORTS[current_port_index]

def switch_to_next_port():
    """切换到下一个端口"""
    global current_port_index
    current_port_index = (current_port_index + 1) % len(API_PORTS)
    logger.info(f"切换到端口: {get_current_port()}")
    return get_current_port()

# =============== 数据库操作 ===============
async def check_and_get_large_tasks():
    """
    检查并获取所有 large 任务
    
    Returns:
        list: large 任务列表
    """
    try:
        response = supabase.table("douyin") \
                           .select("aweme_id, sec_uid") \
                           .not_.is_("large", "null") \
                           .execute()
        
        tasks = response.data or []
        logger.info(f"找到 {len(tasks)} 个待处理的 large 任务")
        return tasks
    except Exception as e:
        logger.error(f"查询 large 任务时发生错误: {str(e)}")
        return []

async def clear_large_flag(aweme_id):
    """清除作品的large标记"""
    try:
        supabase.table("douyin") \
                .update({"large": None}) \
                .eq("aweme_id", aweme_id) \
                .execute()
        logger.info(f"已清除作品 {aweme_id} 的 large 标记")
    except Exception as e:
        logger.error(f"清除 large 标记失败: {str(e)}")

def sanitize_and_merge(existing_row: dict, new_data: dict) -> dict:
    """合并现有数据和新数据，清理None值"""
    final_data = existing_row.copy() if existing_row else {}
    
    for key, value in new_data.items():
        if value is not None:
            final_data[key] = value
    
    keys_to_remove = [k for k, v in final_data.items() if v is None]
    for key in keys_to_remove:
        del final_data[key]
    
    return final_data

# =============== TikHub API 交互 ===============
async def fetch_single_aweme_from_tikhub(aweme_id):
    """从TikHub获取单个作品数据"""
    base_url = f"http://localhost:{get_current_port()}/api/v1/douyin/web/fetch_one_video_by_share_url"
    
    max_retries = 3
    for attempt in range(max_retries):
        try:
            # 构建分享链接
            share_url = f"https://www.douyin.com/video/{aweme_id}"
            params = {"share_url": share_url}
            url = f"{base_url}?{'&'.join([f'{k}={v}' for k, v in params.items()])}"
            
            headers = {
                "Authorization": f"Bearer {TIKHUB_TOKEN}",
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
                "Content-Type": "application/json"
            }
            
            async with httpx.AsyncClient(timeout=httpx.Timeout(30.0)) as client:
                response = await client.get(url, headers=headers)
                
                if response.status_code == 200:
                    data = response.json()
                    if data.get("code") == 200:
                        aweme_detail = data.get("data", {}).get("aweme_detail")
                        if aweme_detail:
                            return aweme_detail
                        else:
                            logger.error(f"API返回数据中没有aweme_detail: {json.dumps(data)[:200]}")
                    else:
                        logger.error(f"API返回错误码: {data.get('code')} - {data.get('message', 'No message')}")
                else:
                    logger.error(f"HTTP错误: {response.status_code}")
                    if response.status_code == 429:
                        if attempt < max_retries - 1:
                            wait_time = 2 ** (attempt + 1)
                            logger.warning(f"遇到限流，等待 {wait_time} 秒后重试...")
                            await asyncio.sleep(wait_time)
                            continue
                        else:
                            raise ConnectionError(f"API限流: {response.status_code}")
        
        except httpx.TimeoutException:
            logger.error(f"请求超时 (尝试 {attempt + 1}/{max_retries})")
        except Exception as e:
            logger.error(f"获取作品数据失败 (尝试 {attempt + 1}/{max_retries}): {str(e)}")
        
        if attempt < max_retries - 1:
            await asyncio.sleep(2)
    
    return None

# =============== 媒体下载 ===============
async def download_media_with_retry(url: str, media_type: str = "video", max_retries: int = 3) -> Optional[dict]:
    """下载媒体文件"""
    if not url:
        return None
    
    for attempt in range(max_retries):
        try:
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            }
            
            async with httpx.AsyncClient(timeout=httpx.Timeout(120.0), follow_redirects=True) as client:
                response = await client.get(url, headers=headers)
                
                if response.status_code == 200:
                    binary_data = response.content
                    content_type = response.headers.get('content-type', '')
                    
                    # 根据内容类型确定文件扩展名
                    if media_type == "image":
                        if 'webp' in content_type:
                            ext = 'webp'
                        elif 'png' in content_type:
                            ext = 'png'
                        elif 'gif' in content_type:
                            ext = 'gif'
                        else:
                            ext = 'jpg'
                    elif media_type == "audio":
                        ext = 'mp3'
                    else:
                        ext = 'mp4'
                    
                    return {
                        "binary_data": binary_data,
                        "extension": ext,
                        "content_type": content_type
                    }
                    
        except Exception as e:
            logger.error(f"下载媒体失败 (尝试 {attempt + 1}/{max_retries}): {str(e)}")
            if attempt < max_retries - 1:
                await asyncio.sleep(2)
    
    return None

# =============== 数据解析 ===============
def parse_douyin_data(data: dict) -> dict:
    """解析抖音作品数据"""
    if not data:
        return {}
    
    base_info = {
        'aweme_id': data.get('aweme_id'),
        'desc': data.get('desc', ''),
        'create_time': data.get('create_time'),
        'duration': data.get('duration', 0),
        'aweme_type': data.get('aweme_type', 0),
    }
    
    author = data.get('author', {})
    author_info = {
        'uid': author.get('uid'),
        'sec_uid': author.get('sec_uid', ""),
        'unique_id': author.get('unique_id', ""),
        'nickname': author.get('nickname', ""),
        'follower_count': author.get('follower_count', 0),
        'total_favorited': author.get('total_favorited', 0),
    }
    
    statistics = data.get('statistics', {})
    stats = {
        'comment_count': statistics.get('comment_count', 0),
        'digg_count': statistics.get('digg_count', 0),
        'collect_count': statistics.get('collect_count', 0),
        'share_count': statistics.get('share_count', 0)
    }
    
    # 解析视频/图片URL
    media_info = {'videos': [], 'images': []}
    
    # 视频类型
    if data.get('aweme_type') in [0, 61]:
        video = data.get('video', {})
        play_addr = video.get('play_addr', {})
        url_list = play_addr.get('url_list', [])
        if url_list:
            media_info['videos'].append(url_list[0])
    
    # 图片类型
    elif data.get('aweme_type') in [2, 68]:
        images = data.get('images', [])
        for img in images:
            if isinstance(img, dict):
                url_list = img.get('url_list', [])
                if url_list:
                    media_info['images'].append(url_list[0])
    
    # 解析音乐信息
    music_info = {}
    music = data.get('music', {})
    if music:
        play_url = music.get('play_url', {})
        url_list = play_url.get('url_list', [])
        if url_list:
            music_info = {
                'id': music.get('id'),
                'title': music.get('title', ''),
                'author': music.get('author', ''),
                'play_url': url_list[0]
            }
    
    return {
        'base_info': base_info,
        'author_info': author_info,
        'statistics': stats,
        'music_info': music_info,
        'media_info': media_info
    }

# =============== 视频处理 ===============
async def convert_videos_to_mp4(video_paths):
    """批量转换视频为MP4格式"""
    if not video_paths:
        return {}
    
    converted_paths = []
    for video_path in video_paths:
        try:
            output_path = str(video_path).replace('.mp4', '_converted.mp4')
            
            cmd = [
                'ffmpeg', '-i', str(video_path),
                '-c:v', 'libx264',
                '-c:a', 'aac',
                '-movflags', '+faststart',
                '-y',
                output_path
            ]
            
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            stdout, stderr = await process.communicate()
            
            if process.returncode == 0:
                converted_paths.append(output_path)
            else:
                logger.error(f"转换失败: {stderr.decode()}")
                converted_paths.append(Exception(f"转换失败"))
                
        except Exception as e:
            logger.error(f"转换视频时出错: {str(e)}")
            converted_paths.append(Exception(str(e)))
    
    result_map = {}
    for original_path, converted_path in zip(video_paths, converted_paths):
        if isinstance(converted_path, Exception):
            result_map[original_path] = None
        else:
            result_map[original_path] = converted_path
    
    return result_map

async def get_video_file_size_in_mb(file_path):
    """获取文件大小(MB)"""
    try:
        stat_result = os.stat(file_path)
        file_size_mb = stat_result.st_size / (1024 * 1024)
        return file_size_mb
    except Exception as e:
        logger.error(f"获取文件大小失败: {str(e)}")
        return 0

# =============== Telegram 发送 ===============
async def send_media_to_telegram(
    app,
    aweme_id,
    file_paths,
    description,
    is_large_task_mode=True,
    music_file_path=None
):
    """发送媒体到Telegram并更新数据库"""
    
    actual_bot_token = BOT_TOKEN
    all_file_ids = []
    music_processed = False
    db_updated = False
    
    try:
        # 获取现有数据
        existing_response = supabase.table("douyin").select("*").eq("aweme_id", aweme_id).execute()
        existing_row = existing_response.data[0] if existing_response.data else {}
        
        # 创建ZIP文件
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        zip_filename = f"{aweme_id}_{timestamp}.zip"
        zip_path = os.path.join('downloads', zip_filename)
        
        with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
            # 添加媒体文件
            for file_path in file_paths:
                if os.path.exists(file_path):
                    arcname = os.path.basename(file_path)
                    zipf.write(file_path, arcname=arcname)
            
            # 添加音频文件
            if music_file_path and os.path.exists(music_file_path):
                zipf.write(music_file_path, arcname=os.path.basename(music_file_path))
        
        # 发送ZIP文件到Telegram
        zip_caption = f"本作品（{aweme_id}）是large作品"
        
        for attempt in range(3):
            try:
                async with Client("my_bot", api_id=TELEGRAM_API_ID, api_hash=TELEGRAM_API_HASH, 
                                 bot_token=actual_bot_token) as bot_client:
                    
                    message = await bot_client.send_document(
                        chat_id=TELEGRAM_CHAT_ID,
                        document=zip_path,
                        caption=zip_caption,
                        parse_mode=ParseMode.HTML
                    )
                    
                    if message and message.document:
                        zip_file_id = message.document.file_id
                        all_file_ids.append(zip_file_id)
                        logger.info(f"成功发送 ZIP 文件，获得file_id: {zip_file_id[:20]}...")
                        
                break
            except FloodWait as e:
                wait_time = e.value
                logger.warning(f"Telegram API FloodWait: 要求等待 {wait_time} 秒")
                await asyncio.sleep(wait_time + 1)
            except Exception as e:
                logger.error(f"发送ZIP文件失败 (尝试 {attempt + 1}/3): {str(e)}")
                if attempt == 2:
                    return ""
        
        # 发送音频文件
        if music_file_path and os.path.exists(music_file_path):
            try:
                async with Client("my_bot", api_id=TELEGRAM_API_ID, api_hash=TELEGRAM_API_HASH,
                                 bot_token=actual_bot_token) as bot_client:
                    
                    message = await bot_client.send_audio(
                        chat_id=TELEGRAM_CHAT_ID,
                        audio=music_file_path,
                        caption=f"音频 - {aweme_id}"
                    )
                    
                    if message and message.audio:
                        music_file_id = message.audio.file_id
                        all_file_ids.append(music_file_id)
                        music_processed = True
                        logger.info(f"成功发送音频文件，获得file_id: {music_file_id[:20]}...")
                        
            except Exception as e:
                logger.error(f"发送音频文件失败: {str(e)}")
        
        # 删除临时文件
        if DELETE_FILES:
            try:
                if os.path.exists(zip_path):
                    os.remove(zip_path)
                for file_path in file_paths:
                    if os.path.exists(file_path):
                        os.remove(file_path)
                if music_file_path and os.path.exists(music_file_path):
                    os.remove(music_file_path)
            except Exception as e:
                logger.error(f"删除临时文件失败: {str(e)}")
        
        # 更新数据库 - Large任务清空并重写
        if all_file_ids:
            try:
                new_data = {
                    "aweme_id": aweme_id,
                    "file_id": ';'.join(all_file_ids),
                    "high_quality": ';'.join(all_file_ids),
                    "failed": None,  # 清空failed字段
                    "large": None,  # 清除large标记
                    "description": description
                }
                
                final_data = sanitize_and_merge(existing_row, new_data)
                result = supabase.table("douyin").upsert(final_data).execute()
                
                if result.data:
                    logger.info(f"Large任务：已更新作品 {aweme_id} 的数据")
                    db_updated = True
                    
            except Exception as e:
                logger.error(f"更新数据库失败: {str(e)}")
        
        # 返回结果
        result_parts = [';'.join(all_file_ids)]
        if db_updated:
            result_parts.append("UPDATED")
        if music_processed:
            result_parts.append("MUSIC_PROCESSED")
        result_parts.append("LARGE_TASK")
        
        return ';'.join(result_parts)
        
    except Exception as e:
        logger.error(f"发送媒体到Telegram时出错: {str(e)}")
        return ""

# =============== 主处理函数 ===============
async def process_single_large_task(app, task):
    """
    处理单个 large 任务
    
    Returns:
        tuple: (是否成功, 是否遇到限流)
    """
    aweme_id = task.get("aweme_id")
    
    if not aweme_id:
        return False, False
    
    logger.info(f"开始处理 large 任务: {aweme_id}")
    
    try:
        # 获取作品数据
        logger.info(f"正在获取作品 {aweme_id} 的数据...")
        data_json = await fetch_single_aweme_from_tikhub(aweme_id)
        
        if not data_json:
            logger.error(f"无法获取作品 {aweme_id} 的数据")
            return False, False
        
        # 解析数据
        parsed_data = parse_douyin_data(data_json)
        if not parsed_data:
            logger.error(f"解析作品 {aweme_id} 数据失败")
            return False, False
        
        # 获取媒体URL
        media_info = parsed_data.get('media_info', {})
        videos = media_info.get('videos', [])
        images = media_info.get('images', [])
        
        if not videos and not images:
            logger.warning(f"作品 {aweme_id} 没有媒体内容")
            # 清除large标记
            await clear_large_flag(aweme_id)
            return True, False
        
        # 下载媒体文件
        download_tasks = []
        file_paths = []
        
        # 下载视频
        for video_url in videos:
            task = download_media_with_retry(video_url, "video")
            download_tasks.append({'task': task, 'type': 'video', 'url': video_url})
        
        # 下载图片
        for image_url in images:
            task = download_media_with_retry(image_url, "image")
            download_tasks.append({'task': task, 'type': 'image', 'url': image_url})
        
        # 执行下载
        if download_tasks:
            tasks = [t['task'] for t in download_tasks]
            download_results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # 保存下载的文件
            for i, (task_info, result) in enumerate(zip(download_tasks, download_results)):
                if isinstance(result, Exception):
                    logger.error(f"下载失败: {str(result)}")
                    continue
                    
                if result and result.get("binary_data"):
                    media_type = task_info['type']
                    extension = result.get('extension', 'mp4' if media_type == 'video' else 'jpg')
                    
                    file_name = f"{aweme_id}_{i}.{extension}"
                    file_path = os.path.join('downloads', file_name)
                    
                    with open(file_path, 'wb') as f:
                        f.write(result['binary_data'])
                    
                    file_paths.append(file_path)
                    logger.info(f"已保存 {media_type} 文件: {file_path}")
        
        # 下载音频
        music_file_path = None
        music_info = parsed_data.get('music_info', {})
        if music_info and music_info.get('play_url'):
            music_result = await download_media_with_retry(music_info['play_url'], "audio")
            if music_result and music_result.get("binary_data"):
                music_file_path = os.path.join('downloads', f"{aweme_id}_audio.mp3")
                with open(music_file_path, 'wb') as f:
                    f.write(music_result['binary_data'])
                logger.info(f"已保存音频文件: {music_file_path}")
        
        # 检查是否需要转换视频
        if videos and file_paths:
            video_paths = [fp for fp in file_paths if fp.endswith('.mp4')]
            if video_paths:
                # 检查视频大小
                need_conversion = False
                for vp in video_paths:
                    size_mb = await get_video_file_size_in_mb(vp)
                    if size_mb > 50:
                        need_conversion = True
                        break
                
                if need_conversion:
                    logger.info(f"视频文件过大，需要转换...")
                    converted = await convert_videos_to_mp4(video_paths)
                    
                    # 替换原文件路径
                    new_file_paths = []
                    for fp in file_paths:
                        if fp in converted and converted[fp]:
                            new_file_paths.append(converted[fp])
                            # 删除原文件
                            if DELETE_FILES and os.path.exists(fp):
                                os.remove(fp)
                        else:
                            new_file_paths.append(fp)
                    file_paths = new_file_paths
        
        # 构建描述
        base_info = parsed_data.get('base_info', {})
        desc = base_info.get('desc', '无描述')
        description = f"Large任务 - {aweme_id}\n{desc}"
        
        # 发送到Telegram
        if file_paths:
            result = await send_media_to_telegram(
                app,
                aweme_id,
                file_paths,
                description,
                is_large_task_mode=True,
                music_file_path=music_file_path
            )
            
            if result and not result.startswith(";"):
                logger.info(f"Large 任务 {aweme_id} 处理成功")
                return True, False
            else:
                logger.error(f"Large 任务 {aweme_id} 发送失败")
                return False, False
        else:
            logger.warning(f"Large 任务 {aweme_id} 没有可发送的媒体文件")
            # 清除large标记
            await clear_large_flag(aweme_id)
            return True, False
            
    except ConnectionError:
        logger.warning(f"处理 large 任务 {aweme_id} 时遭遇限流")
        return False, True
    except Exception as e:
        if '429' in str(e) or 'rate limit' in str(e).lower():
            logger.warning(f"处理 large 任务 {aweme_id} 时遭遇限流: {str(e)}")
            return False, True
        logger.error(f"处理 large 任务 {aweme_id} 时发生错误: {str(e)}", exc_info=True)
        return False, False

# =============== 主循环 ===============
async def main():
    """主函数"""
    logger.info("Large任务处理程序启动")
    
    # 创建下载目录
    os.makedirs('downloads', exist_ok=True)
    
    # 初始化Telegram客户端
    app = Client(
        "large_bot",
        api_id=TELEGRAM_API_ID,
        api_hash=TELEGRAM_API_HASH,
        bot_token=BOT_TOKEN
    )
    
    await app.start()
    logger.info("Telegram客户端已启动")
    
    try:
        while True:
            # 获取所有large任务
            large_tasks = await check_and_get_large_tasks()
            
            if not large_tasks:
                logger.info("当前没有待处理的large任务，等待60秒...")
                await asyncio.sleep(60)
                continue
            
            # 统计信息
            total_tasks = len(large_tasks)
            processed = 0
            failed = 0
            
            logger.info(f"开始处理 {total_tasks} 个large任务")
            
            # 分批处理任务
            for i in range(0, total_tasks, BATCH_SIZE):
                batch = large_tasks[i:i+BATCH_SIZE]
                logger.info(f"处理第 {i//BATCH_SIZE + 1} 批任务 ({len(batch)} 个)")
                
                for task in batch:
                    aweme_id = task.get("aweme_id")
                    
                    try:
                        success, hit_rate_limit = await process_single_large_task(app, task)
                        
                        if hit_rate_limit:
                            logger.warning(f"遭遇限流，暂停60秒...")
                            await asyncio.sleep(60)
                            # 切换端口
                            switch_to_next_port()
                            continue
                            
                        if success:
                            processed += 1
                            logger.info(f"✅ 成功处理: {aweme_id} ({processed}/{total_tasks})")
                        else:
                            failed += 1
                            logger.error(f"❌ 处理失败: {aweme_id} ({failed} 个失败)")
                            
                    except Exception as e:
                        failed += 1
                        logger.error(f"处理任务 {aweme_id} 时出错: {str(e)}")
                    
                    # 任务间延迟
                    await asyncio.sleep(PROCESS_DELAY)
                
                # 批次间延迟
                if i + BATCH_SIZE < total_tasks:
                    logger.info(f"批次处理完成，等待5秒后继续...")
                    await asyncio.sleep(5)
            
            # 统计报告
            logger.info(f"""
=============== Large任务处理报告 ===============
📊 总任务数: {total_tasks}
✅ 成功处理: {processed}
❌ 处理失败: {failed}
⏰ 完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
================================================
            """)
            
            # 循环间隔
            logger.info("本轮处理完成，等待30秒后检查新任务...")
            await asyncio.sleep(30)
            
    except KeyboardInterrupt:
        logger.info("收到中断信号，正在关闭...")
    except Exception as e:
        logger.error(f"主循环出错: {str(e)}", exc_info=True)
    finally:
        await app.stop()
        logger.info("程序已关闭")

if __name__ == "__main__":
    asyncio.run(main())
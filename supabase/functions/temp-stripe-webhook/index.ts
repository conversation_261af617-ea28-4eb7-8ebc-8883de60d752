import { serve } from 'https://deno.land/std@0.168.0/http/server.ts';
import Stripe from 'https://esm.sh/stripe@11.15.0?target=deno';
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';

serve(async (req) => {
  try {
    // 获取环境变量
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!;
    const supabaseKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!;
    const stripeSecretKey = Deno.env.get('STRIPE_SECRET_KEY')!;

    // 初始化 Supabase 和 Stripe 客户端
    const supabase = createClient(supabaseUrl, supabaseKey);
    const stripe = new Stripe(stripeSecretKey);

    // 处理 Supabase Webhook 请求
    const contentType = req.headers.get('content-type') || '';
    if (!contentType.includes('application/json')) {
      return new Response('Unsupported Media Type', { status: 415 });
    }

    const payload = await req.json();

    // 检查事件类型和表名
    if (payload.table === 'users' && payload.type === 'UPDATE') {
      const newUser = payload.record;
      const oldUser = payload.old_record;

      if (!newUser || !newUser.user_id) {
        return new Response('Missing user data', { status: 400 });
      }

      const userId = String(newUser.user_id).trim();

      // 初始化标志位
      let tierChanged = false;
      let manageChanged = false;

      // 检查字段是否发生了变化
      if (newUser.tier !== oldUser.tier) {
        tierChanged = true;
        console.log(`用户 "${userId}" 的 tier 从 "${oldUser.tier}" 变为 "${newUser.tier}"`);
      }

      if (newUser.manage !== oldUser.manage) {
        manageChanged = true;
        console.log(`用户 "${userId}" 的 manage 从 "${oldUser.manage}" 变为 "${newUser.manage}"`);
      }

      // 如果既没有 manage 变化，也没有 tier 变化，不执行任何操作
      if (!tierChanged && !manageChanged) {
        return new Response(JSON.stringify({ status: 'no_change' }), { status: 200 });
      }

      // 获取用户的 stripe_customer_id 和 stripe_subscription_id
      let stripeCustomerId = newUser.stripe_customer_id;
      let stripeSubscriptionId = newUser.stripe_subscription_id;

      // 如果 manage 发生了变化，且存在 stripe_customer_id，则从 Stripe 同步订阅情况
      if (manageChanged && stripeCustomerId) {
        try {
          // 从 Stripe 获取最新的订阅状态
          const subscriptions = await stripe.subscriptions.list({
            customer: stripeCustomerId,
            status: 'all',
            limit: 1,
          });

          if (subscriptions.data.length > 0) {
            const subscription = subscriptions.data[0];
            stripeSubscriptionId = subscription.id;

            // 根据订阅的产品 ID，确定用户的 tier 和 allowance
            let tier = 'free';
            let allowance = 2;

            if (subscription.status === 'active' || subscription.status === 'trialing') {
              const productId = subscription.items.data[0].price.product as string;

              switch (productId) {
                case 'prod_Qz9OC3PN85NJ2B': // 2.98 美元，8 个链接
                  tier = '2.98';
                  allowance = 8;
                  break;
                case 'prod_Qz9MvUG0am9a1I': // 4.98 美元，18 个链接
                  tier = '4.98';
                  allowance = 18;
                  break;
                case 'prod_PqpYUl02R87Ymg': // 9.98 美元，40 个链接
                  tier = '9.98';
                  allowance = 40;
                  break;
                default:
                  console.error('未知的产品 ID:', productId);
                  return new Response('Unknown product ID', { status: 400 });
              }

              // 更新数据库中的用户信息
              await supabase
                .from('users')
                .update({
                  stripe_subscription_id: stripeSubscriptionId,
                  tier,
                  allowance,
                })
                .eq('user_id', userId);

              console.log(`同步用户 "${userId}" 的订阅信息：订阅 ID 为 ${stripeSubscriptionId}，tier 为 "${tier}"，allowance 为 ${allowance}`);
            } else {
              // 订阅存在但不活跃，将 stripe_subscription_id 设为 null，tier 设为 free
              await supabase
                .from('users')
                .update({
                  stripe_subscription_id: null,
                  tier: 'free',
                  allowance: 2,
                })
                .eq('user_id', userId);

              console.log(`用户 "${userId}" 的订阅不活跃，已将 stripe_subscription_id 清除，tier 设置为 "free"`);
            }
          } else {
            // 用户在 Stripe 上没有订阅，清除 stripe_subscription_id
            await supabase
              .from('users')
              .update({
                stripe_subscription_id: null,
                tier: 'free',
                allowance: 2,
              })
              .eq('user_id', userId);

            console.log(`用户 "${userId}" 在 Stripe 上没有订阅，已将 stripe_subscription_id 清除，tier 设置为 "free"`);
          }
        } catch (err) {
          console.error(`同步用户 "${userId}" 的订阅信息时出错：`, err);
        }
      }

      // 如果 tier 发生了变化，则根据新的 tier 生成订阅链接
      if (tierChanged) {
        // 如果没有 stripe_customer_id，创建一个新的客户
        if (!stripeCustomerId) {
          const customer = await stripe.customers.create({
            metadata: { user_id: userId },
            email: newUser.email || '',
          });
          stripeCustomerId = customer.id;

          // 更新数据库中的 stripe_customer_id
          await supabase
            .from('users')
            .update({ stripe_customer_id: stripeCustomerId })
            .eq('user_id', userId);

          console.log(`为用户 "${userId}" 创建了 Stripe 客户：${stripeCustomerId}`);
        }

        try {
          if (stripeSubscriptionId) {
            // 如果用户有活跃的订阅，无论 tier 变为 free 还是其他，需要引导用户进入 Customer Portal
            const portalSession = await stripe.billingPortal.sessions.create({
              customer: stripeCustomerId,
              return_url: 'https://t.me/LNotifierBot', // 您的返回页面
            });

            // 将链接存储到 sublink 字段
            await supabase
              .from('users')
              .update({ sublink: portalSession.url })
              .eq('user_id', userId);

            console.log(`为用户 "${userId}" 生成了 Customer Portal 链接`);
          } else if (newUser.tier !== 'free') {
            // 如果没有现有订阅，且 tier 不是 'free'，创建新的 Checkout Session
            let priceId = '';
            switch (newUser.tier) {
              case '2.98':
                priceId = 'price_1QA6IDFWPWRTidsh79RB7fUe'; // 2.98 美元的价格 ID
                break;
              case '4.98':
                priceId = 'price_1Q7B48FWPWRTidsh2PDaR2HM'; // 4.98 美元的价格 ID
                break;
              case '9.98':
                priceId = 'price_1QAwm6FWPWRTidshuqw1U5xT'; // 9.98 美元的价格 ID
                break;
              default:
                console.error(`未知的 tier "${newUser.tier}"，用户 "${userId}"`);
                return new Response('Unknown tier', { status: 400 });
            }

            const sessionParams: any = {
              customer: stripeCustomerId,
              payment_method_types: ['card'],
              line_items: [{ price: priceId, quantity: 1 }],
              mode: 'subscription',
              success_url: 'https://t.me/LNotifierBot', // 您的成功页面
              cancel_url: 'https://t.me/LNotifierBot',  // 您的取消页面
            };

            const session = await stripe.checkout.sessions.create(sessionParams);

            // 将链接存储到 sublink 字段
            await supabase
              .from('users')
              .update({ sublink: session.url })
              .eq('user_id', userId);

            console.log(`为用户 "${userId}" 生成了新的订阅链接，并存储在 sublink`);
          } else {
            // 如果没有现有订阅，且 tier 为 'free'，不需要生成任何链接，清空 sublink
            await supabase
              .from('users')
              .update({ sublink: null })
              .eq('user_id', userId);

            console.log(`用户 "${userId}" 已是免费用户，无需生成链接`);
          }
        } catch (err) {
          console.error(`处理用户 "${userId}" 的订阅链接生成时出错：`, err);
          return new Response('Error processing subscription link', { status: 500 });
        }
      }

      return new Response(JSON.stringify({ status: 'success' }), { status: 200 });
    } else {
      // 未处理的事件类型
      return new Response('Event not handled', { status: 400 });
    }
  } catch (err) {
    console.error('未处理的错误：', err);
    return new Response('Internal Server Error', { status: 500 });
  }
});
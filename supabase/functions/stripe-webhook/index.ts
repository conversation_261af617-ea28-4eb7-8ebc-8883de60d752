import { serve } from 'https://deno.land/std@0.168.0/http/server.ts';
import Stripe from 'https://esm.sh/stripe@11.15.0?target=deno';
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';

serve(async (req) => {
  try {
    // 获取环境变量
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!;
    const supabaseKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!;
    const stripeSecretKey = Deno.env.get('STRIPE_SECRET_KEY')!;
    const stripeWebhookSecret = Deno.env.get('STRIPE_WEBHOOK_SECRET')!;

    // 初始化 Supabase 和 Stripe 客户端
    const supabase = createClient(supabaseUrl, supabaseKey);
    const stripe = new Stripe(stripeSecretKey);

    // 验证请求是否来自 Stripe
    const stripeSignature = req.headers.get('stripe-signature');

    if (!stripeSignature) {
      return new Response('Bad Request: Missing Stripe Signature', { status: 400 });
    }

    // 处理 Stripe Webhook 事件
    let event;

    try {
      const payload = await req.text(); // 获取原始请求体
      event = await stripe.webhooks.constructEventAsync(payload, stripeSignature, stripeWebhookSecret);
    } catch (err) {
      console.error('Error verifying Stripe webhook signature:', err);
      return new Response(`Webhook Error: ${err.message}`, { status: 400 });
    }

    // 根据事件类型处理
    console.log(`Processing Stripe event: ${event.type}`);

    if (
      event.type === 'customer.subscription.created' ||
      event.type === 'customer.subscription.updated' ||
      event.type === 'customer.subscription.deleted'
    ) {
      const subscription = event.data.object as Stripe.Subscription;

      const customerId = subscription.customer as string;

      // 获取客户信息，以获取 user_id 和 email
      const customer = (await stripe.customers.retrieve(customerId)) as Stripe.Customer;

      const userId = customer.metadata?.user_id;
      if (!userId) {
        console.error('No user_id found in customer metadata');
        return new Response('No user_id found', { status: 400 });
      }

      // 准备要更新的数据
      let updateData: any = {
        stripe_customer_id: customerId,
        email: customer.email || '', // 获取用户的 email
        sublink: null, // 清空 sublink 字段
      };

      if (subscription.status === 'active' || subscription.status === 'trialing') {
        // 订阅处于活跃或试用状态
        const productId = subscription.items.data[0].price.product as string;
        let tier = 'free';
        let allowance = 2;

        switch (productId) {
          case 'prod_Qz9OC3PN85NJ2B': // 2.98 美元，8 个链接
            tier = '2.98';
            allowance = 8;
            break;
          case 'prod_Qz9MvUG0am9a1I': // 4.98 美元，18 个链接
            tier = '4.98';
            allowance = 18;
            break;
          case 'prod_PqpYUl02R87Ymg': // 9.98 美元，40 个链接
            tier = '9.98';
            allowance = 40;
            break;
          default:
            console.error('未知的产品 ID:', productId);
            return new Response('Unknown product ID', { status: 400 });
        }

        updateData.tier = tier;
        updateData.allowance = allowance;
        updateData.stripe_subscription_id = subscription.id;

        console.log(`Updated user "${userId}" with tier "${tier}" and allowance "${allowance}".`);
      } else if (
        event.type === 'customer.subscription.deleted' ||
        subscription.status === 'canceled' ||
        subscription.status === 'incomplete_expired' ||
        subscription.status === 'unpaid' ||
        subscription.status === 'past_due'
      ) {
        // 订阅被取消或无效
        updateData.tier = 'free';
        updateData.allowance = 2;
        updateData.stripe_subscription_id = null; // 将订阅ID设为null
        console.log(`Subscription canceled or invalid for user "${userId}", set tier to "free" and cleared stripe_subscription_id.`);
      } else {
        // 对于其他状态，不修改 tier
        console.log(
          `Subscription status "${subscription.status}" for user "${userId}", not updating tier.`
        );
      }

      // 更新数据库
      const { error } = await supabase
        .from('users')
        .update(updateData)
        .eq('user_id', userId);

      if (error) {
        console.error(`Error updating user "${userId}":`, error);
        return new Response('Error updating user', { status: 500 });
      }
    }

    // 返回给 Stripe 一个成功的响应
    return new Response(JSON.stringify({ received: true }), { status: 200 });
  } catch (err) {
    console.error('Unhandled error:', err);
    return new Response('Internal Server Error', { status: 500 });
  }
});
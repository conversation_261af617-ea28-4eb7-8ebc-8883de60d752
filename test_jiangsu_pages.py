#!/usr/bin/env python3
"""
测试程序：获取江苏卫视的前10页作品数据
使用原API（端口8080）
"""

import asyncio
import httpx
import json
from datetime import datetime

# 江苏卫视的sec_user_id
JIANGSU_SEC_UID = "MS4wLjABAAAAkyaGcpuN7__XkDoxYv_0sYWp1s-kMwsbTiO46YrKHjI"

async def fetch_user_videos_page(sec_user_id, max_cursor=0, count=40, page_num=1):
    """获取指定页的用户作品"""
    url = f"http://localhost:8080/api/douyin/web/fetch_user_post_videos"
    
    params = {
        'sec_user_id': sec_user_id,
        'max_cursor': max_cursor,
        'count': count
    }
    
    print(f"  请求参数: max_cursor={max_cursor}, count={count}")
    
    try:
        async with httpx.AsyncClient(timeout=30) as client:
            response = await client.get(url, params=params)
            
            if response.status_code == 200:
                response_data = response.json()
                
                # API响应被包装在data字段里
                if 'data' in response_data:
                    data = response_data['data']
                else:
                    data = response_data
                
                if data.get('status_code') == 0:
                    aweme_list = data.get('aweme_list', [])
                    has_more = data.get('has_more', 0)
                    next_cursor = data.get('max_cursor', 0)
                    
                    print(f"第{page_num}页 - 获取到 {len(aweme_list)} 个作品, has_more={has_more}, next_cursor={next_cursor}")
                    
                    return {
                        'page': page_num,
                        'works': aweme_list,
                        'has_more': has_more,
                        'next_cursor': next_cursor,
                        'count': len(aweme_list)
                    }
                else:
                    print(f"第{page_num}页 - API返回错误: status_code={data.get('status_code')}, msg={data.get('status_msg', 'Unknown error')}")
                    return None
            else:
                print(f"第{page_num}页 - HTTP错误: {response.status_code}")
                return None
                
    except Exception as e:
        print(f"第{page_num}页 - 请求失败: {e}")
        return None

async def fetch_all_pages():
    """获取江苏卫视的前10页数据"""
    print(f"开始获取江苏卫视的前10页作品数据")
    print(f"SEC_UID: {JIANGSU_SEC_UID}")
    print("="*80)
    
    all_pages = []
    max_cursor = 0
    
    for page_num in range(1, 11):  # 获取前10页
        print(f"\n正在获取第{page_num}页...")
        
        page_data = await fetch_user_videos_page(
            JIANGSU_SEC_UID, 
            max_cursor=max_cursor,
            count=40,
            page_num=page_num
        )
        
        if page_data:
            all_pages.append(page_data)
            
            # 打印该页的作品信息
            for i, work in enumerate(page_data['works'][:5], 1):  # 每页只打印前5个作品
                create_time = work.get('create_time', 0)
                if create_time:
                    create_date = datetime.fromtimestamp(create_time).strftime('%Y-%m-%d %H:%M:%S')
                else:
                    create_date = 'N/A'
                    
                title = work.get('desc', 'No title')[:50]  # 限制标题长度
                aweme_id = work.get('aweme_id', 'N/A')
                
                print(f"  [{i}] ID:{aweme_id} | {create_date} | {title}")
            
            if len(page_data['works']) > 5:
                print(f"  ... 还有 {len(page_data['works']) - 5} 个作品")
            
            # 检查是否还有更多页
            if page_data['has_more'] == 0:
                print(f"\n没有更多作品了，在第{page_num}页停止")
                break
            
            # 更新cursor以获取下一页
            max_cursor = page_data['next_cursor']
            
            # 添加小延迟避免请求过快
            await asyncio.sleep(0.5)
        else:
            print(f"第{page_num}页获取失败，停止获取")
            break
    
    # 汇总统计
    print("\n" + "="*80)
    print("汇总统计:")
    total_works = sum(page['count'] for page in all_pages)
    print(f"成功获取页数: {len(all_pages)}/10")
    print(f"总作品数: {total_works}")
    
    # 保存到JSON文件
    result = {
        'sec_user_id': JIANGSU_SEC_UID,
        'name': '江苏卫视',
        'total_pages': len(all_pages),
        'total_works': total_works,
        'fetch_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        'pages': all_pages
    }
    
    with open('jiangsu_10pages.json', 'w', encoding='utf-8') as f:
        json.dump(result, f, ensure_ascii=False, indent=2)
    
    print(f"\n数据已保存到 jiangsu_10pages.json")
    
    # 分析作品时间分布
    all_works = []
    for page in all_pages:
        all_works.extend(page['works'])
    
    if all_works:
        timestamps = [w.get('create_time', 0) for w in all_works if w.get('create_time')]
        if timestamps:
            latest = datetime.fromtimestamp(max(timestamps))
            oldest = datetime.fromtimestamp(min(timestamps))
            print(f"\n作品时间跨度:")
            print(f"最新作品: {latest.strftime('%Y-%m-%d %H:%M:%S')}")
            print(f"最早作品: {oldest.strftime('%Y-%m-%d %H:%M:%S')}")
            print(f"时间跨度: {(latest - oldest).days} 天")

if __name__ == "__main__":
    asyncio.run(fetch_all_pages())
from pyrogram import Client, filters
from pyrogram.errors import BadRequest
import re
import logging
import asyncio
from pyrogram.types import *
import config_handler

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

api_id = '25432929'
api_hash = '965c5d22f0b9d1d0326e84bbb2bb18c1'
bot_token = '7148756337:AAHYnQGq18_D9GVhWfj9pzSELIUJxdPtQxA'

app = Client("my_bot", api_id=api_id, api_hash=api_hash, bot_token=bot_token)

IGNORE_PATTERNS = [
    r'^(Notification|Alert)',
    r'click to pay',
    r'click to manage'
]

def should_ignore_message(text):
    text_lower = text.lower()
    if re.search(r'^(Notification|Alert)', text, re.IGNORECASE):
        return True
    if 'click to pay' in text_lower or 'click to manage' in text_lower:
        return True
    return False

@app.on_message(filters.text & filters.private)
async def handle_all_inputs(client, message):
    user_input = message.text.strip()
    user_id = message.from_user.id

    if should_ignore_message(user_input):
        await message.delete()
        return

    if "is on live" in user_input or "ID Type: tsid" in user_input or "ID Type: mp4id" in user_input:
        return

    if user_input == "⚙️Settings":
        push_settings = config_handler.get_push_settings(user_id)
        current_timezone = config_handler.get_user_timezone(user_id)
        allowance, tier = config_handler.get_user_allowance_and_tier(user_id)

        settings_text = "Your current settings:\n\n"
        settings_text += f"Timezone: {current_timezone}\n"
        settings_text += f"Notification Push: {'Enabled' if push_settings['push_notification'] else 'Disabled'}\n"
        settings_text += f"Video Push: {'Enabled' if push_settings['send_video'] else 'Disabled'}"

        keyboard = [
            [InlineKeyboardButton(
                f"{'Disable' if push_settings['push_notification'] else 'Enable'} Notification Push",
                callback_data=f"toggle_push_notification:{user_id}"
            )],
            [InlineKeyboardButton(
                f"{'Disable' if push_settings['send_video'] else 'Enable'} Video Push",
                callback_data=f"toggle_send_video:{user_id}"
            )],
            [InlineKeyboardButton("Change Timezone", callback_data=f"change_timezone:{user_id}")]
        ]

        if allowance == 2:
            keyboard.append([InlineKeyboardButton("Pay for more links", callback_data=f"pay_more_links:{user_id}")])
        else:
            keyboard.append([InlineKeyboardButton("Manage Payment", callback_data=f"manage_payment:{user_id}")])

        keyboard.append([InlineKeyboardButton("Close", callback_data=f"cancel_settings:{user_id}")])

        reply_markup = InlineKeyboardMarkup(keyboard)
        settings_message = await message.reply_text(settings_text, reply_markup=reply_markup)
        asyncio.create_task(delete_message_after_delay(settings_message, 60))
        await message.delete()
        return

    if user_input == "📖Help":
        help_text = (
            "This is NotifierBot. You can send TikTok and Douyin share links to subscribe to anchors "
            "and get notified when they go live.\n\nYou can turn off notifications in settings or simply mute "
            "this bot when needed.\n\nFor any other questions, <NAME_EMAIL> and we'll "
            "reply within 5 business days."
        )
        help_message = await message.reply_text(help_text)
        asyncio.create_task(delete_message_after_delay(help_message, 20))
        await message.delete()
        return

    if user_input == "👥User Info":
        user_id = message.from_user.id
        username = message.from_user.username
        response_text = config_handler.start_command(user_id, username)
        info_message = await message.reply_text(response_text)
        asyncio.create_task(delete_message_after_delay(info_message, 20))
        await message.delete()
        return

    if user_input.startswith('/'):
        if user_input.startswith('/start'):
            await start_command(client, message)
        elif user_input.startswith('/query') or user_input.startswith('/check'):
            await query_subscription(client, message, user_id)
        elif user_input.startswith('/delete'):
            await query_for_deletion(client, message, user_id)
        elif user_input.startswith('/status'):
            await status_command(client, message)
        else:
            await message.reply_text("Unrecognized command.")
        await message.delete()
        return

    links = re.findall(r'http[s]?://(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\\(\\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+', user_input)

    if links:
        if len(links) > 1:
            reply_message = await message.reply_text("Please send only one link at a time. The message will be deleted in 10 seconds.")
            await asyncio.sleep(10)
            await reply_message.delete()
        else:
            await add_subscription(client, message, links[0], user_id)
        await message.delete()
    else:
        reply_message = await message.reply_text("Please send a valid command or link. The message will be deleted in 10 seconds.")
        await asyncio.sleep(10)
        await reply_message.delete()
        await message.delete()

async def update_settings_message(message, user_id):
    push_settings = config_handler.get_push_settings(user_id)
    current_timezone = config_handler.get_user_timezone(user_id)
    allowance, tier = config_handler.get_user_allowance_and_tier(user_id)

    settings_text = "Your current settings:\n\n"
    settings_text += f"Timezone: {current_timezone}\n"
    settings_text += f"Notification Push: {'Enabled' if push_settings['push_notification'] else 'Disabled'}\n"
    settings_text += f"Video Push: {'Enabled' if push_settings['send_video'] else 'Disabled'}"

    keyboard = [
        [InlineKeyboardButton(
            f"{'Disable' if push_settings['push_notification'] else 'Enable'} Notification Push",
            callback_data=f"toggle_push_notification:{user_id}"
        )],
        [InlineKeyboardButton(
            f"{'Disable' if push_settings['send_video'] else 'Enable'} Video Push",
            callback_data=f"toggle_send_video:{user_id}"
        )],
        [InlineKeyboardButton("Change Timezone", callback_data=f"change_timezone:{user_id}")]
    ]

    if allowance == 2:
        keyboard.append([InlineKeyboardButton("Pay for more links", callback_data=f"pay_more_links:{user_id}")])
    else:
        keyboard.append([InlineKeyboardButton("Manage Payment", callback_data=f"manage_payment:{user_id}")])

    keyboard.append([InlineKeyboardButton("Close", callback_data=f"cancel_settings:{user_id}")])

    reply_markup = InlineKeyboardMarkup(keyboard)

    await message.edit_text(settings_text, reply_markup=reply_markup)

@app.on_callback_query()
async def handle_callback_query(client, callback_query):
    data = callback_query.data

    if data.startswith(("toggle_push_notification:", "toggle_send_video:")):
        setting_type, user_id = data.split(":")
        setting_type = setting_type.replace("toggle_", "")
        current_settings = config_handler.get_push_settings(user_id)
        new_value = not current_settings[setting_type]
        success = config_handler.set_push_setting(user_id, setting_type, new_value)
        if success:
            await callback_query.answer(f"{setting_type.replace('_', ' ').title()} {'enabled' if new_value else 'disabled'}.")
            await update_settings_message(callback_query.message, user_id)
        else:
            await callback_query.answer("Failed to update setting. Please try again.")

    elif data.startswith("change_timezone:"):
        _, user_id = data.split(":")
        await show_timezone_options(callback_query.message, user_id)
    elif data.startswith("set_timezone:"):
        _, user_id, timezone = data.split(":", 2)
        success = config_handler.set_user_timezone(user_id, timezone)
        if success:
            await callback_query.answer(f"Timezone set to {timezone}")
            await update_settings_message(callback_query.message, user_id)
        else:
            await callback_query.answer("Failed to set timezone. Please try again.")
    elif data.startswith("cancel_settings:"):
        await callback_query.answer("Settings closed.")
        await callback_query.message.delete()

    elif data.startswith("pay_more_links:"):
        _, user_id = data.split(":")
        await show_upgrade_options(callback_query.message, user_id, initiated_by_user=True)

    elif data.startswith("upgrade_tier:"):
        _, user_id, tier_value = data.split(":")
        success = config_handler.set_user_tier(user_id, tier_value)
        if success:
            processing_message = await callback_query.message.reply_text("Processing your subscription upgrade...")
            await asyncio.sleep(2)
            await processing_message.edit_text(
                "Your subscription upgrade is being processed.\n"
                "You will receive a payment link shortly.\n\n"
                "For yearly subscription options or other payment methods, please contact <NAME_EMAIL>."
            )
            await callback_query.message.delete()
        else:
            await callback_query.answer("Failed to process your subscription upgrade. Please try again.")

    elif data.startswith("manage_payment:"):
        _, user_id = data.split(":")
        await callback_query.message.delete()
        config_handler.manage_payment(user_id)

    elif data.startswith("delete:"):
        _, user_id, anchor_id = data.split(":")
        anchor_name = config_handler.get_anchor_name(anchor_id)
        confirmation_keyboard = InlineKeyboardMarkup([
            [InlineKeyboardButton("Confirm Delete", callback_data=f"confirm:{user_id}:{anchor_id}")],
            [InlineKeyboardButton("Cancel", callback_data=f"cancel_confirm:{user_id}")]
        ])
        await callback_query.message.edit_text(
            f"Are you sure you want to delete the subscription for {anchor_name}?",
            reply_markup=confirmation_keyboard
        )
    elif data.startswith("confirm:"):
        _, user_id, anchor_id = data.split(":")
        result = config_handler.delete_anchor(user_id, anchor_id)
        if result:
            await callback_query.answer("Subscription deleted.")
            await callback_query.message.edit_text("Subscription has been successfully deleted.")
            await asyncio.sleep(50)
            await callback_query.message.delete()
        else:
            await callback_query.answer("Anchor not found.")
            await callback_query.message.edit_text("Subscription not found.")
    elif data.startswith("cancel_confirm:"):
        _, user_id = data.split(":")
        anchors = config_handler.list_anchors(user_id)
        buttons = [InlineKeyboardButton(anchor[1] if anchor[1] else anchor[2], callback_data=f"delete:{user_id}:{anchor[0]}") for anchor in anchors]
        keyboard_layout = [buttons[i:i + 2] for i in range(0, len(buttons), 2)]
        keyboard_layout.append([InlineKeyboardButton("Cancel", callback_data=f"cancel_list:{user_id}")])
        keyboard = InlineKeyboardMarkup(keyboard_layout)
        await callback_query.message.edit_text("Please select a subscription to delete:", reply_markup=keyboard)
    elif data.startswith("cancel_list:"):
        _, user_id = data.split(":")
        await callback_query.answer("Deletion operation cancelled")
        await callback_query.message.delete()
    elif data.startswith("previous_page:") or data.startswith("next_page:"):
        _, user_id, page = data.split(":")
        page = int(page)
        if data.startswith("previous_page:"):
            page -= 1
        else:
            page += 1
        await query_for_deletion(client, callback_query.message, user_id, page)

def generate_gmt_timezones():
    timezones = []
    for hour in range(-12, 15):
        if hour == 0:
            timezones.append("GMT")
        else:
            timezones.append(f"GMT{'+' if hour > 0 else ''}{hour:02d}")
    return timezones

GMT_TIMEZONES = generate_gmt_timezones()

async def show_timezone_options(message, user_id):
    timezones = ["GMT"] + [f"GMT{'+' if i > 0 else '-'}{abs(i)}" for i in range(-12, 13) if i != 0]

    keyboard = [
        [
            InlineKeyboardButton(timezones[i], callback_data=f"set_timezone:{user_id}:{timezones[i]}"),
            InlineKeyboardButton(timezones[i+1], callback_data=f"set_timezone:{user_id}:{timezones[i+1]}")
        ]
        for i in range(0, len(timezones) - 1, 2)
    ]

    if len(timezones) % 2 != 0:
        keyboard.append([InlineKeyboardButton(timezones[-1], callback_data=f"set_timezone:{user_id}:{timezones[-1]}")])

    cancel_button = [InlineKeyboardButton("Cancel", callback_data=f"cancel_settings:{user_id}")]
    keyboard.insert(0, cancel_button)
    keyboard.append(cancel_button)

    reply_markup = InlineKeyboardMarkup(keyboard)

    await message.edit_text("Please select your timezone:", reply_markup=reply_markup)

async def show_upgrade_options(message, user_id, initiated_by_user=False):
    current_tier = config_handler.get_user_tier(user_id)
    tiers = {
        'free': ['2.98 USD Monthly', '4.98 USD Monthly', '9.98 USD Monthly'],
        '2.98': ['4.98 USD Monthly', '9.98 USD Monthly'],
        '4.98': ['9.98 USD Monthly'],
        '9.98': []
    }
    options = tiers.get(current_tier, ['2.98 USD Monthly', '4.98 USD Monthly', '9.98 USD Monthly'])
    if not options:
        prompt_text = (
            "You have already subscribed to the highest tier.\n"
            "For more links quota, please contact <NAME_EMAIL>."
        )
        await message.reply_text(prompt_text)
        return

    keyboard = [
        [InlineKeyboardButton(option, callback_data=f"upgrade_tier:{user_id}:{option.split()[0]}")] for option in options
    ]
    keyboard.append([InlineKeyboardButton("Cancel", callback_data=f"cancel_settings:{user_id}")])

    reply_markup = InlineKeyboardMarkup(keyboard)

    if initiated_by_user:
        prompt_text = (
            "Please select a subscription plan to upgrade:\n\n"
            "For yearly subscription options or other payment methods, please contact <NAME_EMAIL>."
        )
    else:
        prompt_text = (
            "You have reached your subscription limit.\n"
            "Please consider upgrading your subscription plan.\n\n"
            "For yearly subscription options or other payment methods, please contact <NAME_EMAIL>."
        )

    await message.reply_text(prompt_text, reply_markup=reply_markup)

@app.on_message(filters.command("check") & filters.private)
async def query_subscription(client, message, user_id):
    anchors = config_handler.list_anchors(user_id)
    if anchors:
        max_results_per_message = 50
        num_messages = (len(anchors) + max_results_per_message - 1) // max_results_per_message

        for i in range(num_messages):
            start_index = i * max_results_per_message
            end_index = min((i + 1) * max_results_per_message, len(anchors))
            current_anchors = anchors[start_index:end_index]

            reply_message = f"Your current subscription list (Part {i + 1}/{num_messages}):\n"
            for j, anchor in enumerate(current_anchors, start=start_index+1):
                anchor_id, anchor_name, live_url = anchor
                if not anchor_name or anchor_name.upper() == "NULL":
                    reply_message += f"{j}. {live_url}\n"
                else:
                    reply_message += f"{j}. [{anchor_name}]({live_url})\n"

            await message.reply_text(reply_message, disable_web_page_preview=True)
    else:
        reply_message = "You currently have no subscriptions."
        await message.reply_text(reply_message)

@app.on_message(filters.command(["delete"]))
async def query_for_deletion(client, message, user_id, page=0):
    page_size = 40
    anchors = config_handler.list_anchors(user_id)
    total_pages = (len(anchors) + page_size - 1) // page_size

    start_index = page * page_size
    end_index = min((page + 1) * page_size, len(anchors))
    current_anchors = anchors[start_index:end_index]

    buttons = []
    for anchor in current_anchors:
        anchor_id, anchor_name, live_url = anchor
        button_text = anchor_name if anchor_name else live_url
        buttons.append(InlineKeyboardButton(button_text, callback_data=f"delete:{user_id}:{anchor_id}"))

    keyboard_layout = [buttons[i:i + 2] for i in range(0, len(buttons), 2)]

    if total_pages > 1:
        navigation_buttons = []
        if page > 0:
            navigation_buttons.append(InlineKeyboardButton("⬅️ Previous", callback_data=f"previous_page:{user_id}:{page}"))
        if page < total_pages - 1:
            navigation_buttons.append(InlineKeyboardButton("Next ➡️", callback_data=f"next_page:{user_id}:{page}"))
        keyboard_layout.append(navigation_buttons)

    keyboard_layout.append([InlineKeyboardButton("Cancel", callback_data=f"cancel_list:{user_id}")])
    keyboard = InlineKeyboardMarkup(keyboard_layout)

    if page == 0:
        await message.reply_text(f"Please select a subscription to delete (Page {page + 1}/{total_pages}):", reply_markup=keyboard)
    else:
        await message.edit_text(f"Please select a subscription to delete (Page {page + 1}/{total_pages}):", reply_markup=keyboard)

@app.on_message(filters.command(["status"]))
async def status_command(client, message):
    user_id = message.from_user.id
    live_anchors = config_handler.get_live_anchors(user_id)
    if live_anchors:
        response_text = "The following anchors are currently live:\n"
        for i, anchor in enumerate(live_anchors, start=1):
            response_text += f"{i}. {anchor[0]}:\n"
            response_text += f"[Click to watch]({anchor[1]})\n\n"
    else:
        response_text = "None of your subscribed anchors are currently live."

    await message.reply_text(response_text, disable_web_page_preview=True)

async def add_subscription(client, message, link, user_id):
    success, response_message, extra_data = config_handler.add_link(user_id, link)
    if success:
        reply_message = response_message
        await message.reply_text(reply_message)
    else:
        if extra_data and 'upgrade_options' in extra_data:
            await show_upgrade_options(message, user_id)
        else:
            reply_message = "Failed to add subscription. " + response_message
            await message.reply_text(reply_message)

@app.on_message(filters.command(["start"]))
async def start_command(client, message):
    custom_keyboard = create_custom_keyboard()
    await message.reply_text(
        "Welcome to LiveNotifierBot!\n\n"
        "We currently support TikTok and Douyin share links to subscribe to anchors and get notified when they go live.",
        reply_markup=custom_keyboard
    )

async def delete_message_after_delay(message, delay):
    await asyncio.sleep(delay)
    await message.delete()

def create_custom_keyboard():
    keyboard = [
        [KeyboardButton("⚙️Settings"), KeyboardButton("📖Help"), KeyboardButton("👥User Info")],
    ]
    return ReplyKeyboardMarkup(keyboard, resize_keyboard=True)

if __name__ == "__main__":
    app.run()

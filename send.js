const { Telegraf } = require('telegraf');
const { createClient } = require('@supabase/supabase-js');
const moment = require('moment-timezone');

const botToken = '7148756337:AAHYnQGq18_D9GVhWfj9pzSELIUJxdPtQxA';
const supabaseUrl = 'https://wjanjmsywbydjbfrdkaz.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6IndqYW5qbXN5d2J5ZGpiZnJka2F6Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3MjAxMjc1OTEsImV4cCI6MjAzNTcwMzU5MX0.ny-61EeDBsVRUr6BdWiamt9oV-6z2TC_f01FJff-9fE';

const bot = new Telegraf(botToken);
const supabase = createClient(supabaseUrl, supabaseKey);

// 获取订阅主播的用户 ID，并根据事件类型过滤
const fetchUserIds = async (anchorId, eventType) => {
  let query = supabase
    .from('user_subscriptions')
    .select('user_id, users(send_file, send_video, timezone)')
    .eq('anchor_id', anchorId);

  const { data, error } = await query;

  if (error) {
    console.error('Error fetching user IDs:', error);
    return [];
  }

  return data
    .filter(item => {
      if (eventType === 'tsid_change') {
        return item.users.send_file;
      } else if (eventType === 'mp4id_change') {
        return item.users.send_video;
      }
      return false;
    })
    .map(item => ({
      userId: item.user_id,
      timezone: item.users.timezone,
    }));
};

// 获取 anchor_name
const getAnchorName = async (anchorId) => {
  const { data, error } = await supabase
    .from('anchors')
    .select('anchor_name')
    .eq('anchor_id', anchorId)
    .single();

  if (error) {
    console.error('Error fetching anchor name:', error);
    return 'Unknown Anchor';
  }

  return data.anchor_name;
};

// 格式化时间戳为用户所在时区的日期时间字符串
const formatTimestampToUserDateTime = (timestamp, timezone) => {
  const timezoneOffset = timezone * 60; // 将时区转换为分钟偏移量
  return moment(timestamp).utcOffset(timezoneOffset).format('MM/DD/YYYY HH:mm:ss');
};

// 发送文档给用户
const sendDocumentToUser = async (userId, tsid, caption) => {
  try {
    await bot.telegram.sendDocument(userId, tsid, { caption });
    console.log(`Sent document ${tsid} to user ${userId}`);
  } catch (error) {
    console.error(`Failed to send document to user ${userId}:`, error);
  }
};

// 发送视频给用户
const sendVideoToUser = async (userId, mp4id, caption) => {
  try {
    await bot.telegram.sendVideo(userId, mp4id, { caption });
    console.log(`Sent video ${mp4id} to user ${userId}`);
  } catch (error) {
    console.error(`Failed to send video to user ${userId}:`, error);
  }
};

// 处理事件的函数
const processEvent = async (eventData, users) => {
  const eventType = eventData.type;

  if (eventType === 'tsid_change' || eventType === 'mp4id_change') {
    const { anchorName, recordTime } = eventData;
    console.log(`Processing ${eventType} event, users:`, users);

    const sendPromises = users.map(async ({ userId, timezone }) => {
      const userDateTime = formatTimestampToUserDateTime(recordTime, timezone);
      const caption = `${anchorName} - ${userDateTime}`;

      if (eventType === 'tsid_change') {
        await sendDocumentToUser(userId, eventData.tsid, caption);
      } else {
        await sendVideoToUser(userId, eventData.mp4id, caption);
      }
    });

    await Promise.all(sendPromises);
  }
};

// 处理 anchor_tsid 表的事件
const onTsidEvent = async (payload) => {
  const newRecord = payload.new;
  const oldRecord = payload.old;

  let tsidChanged = false;

  // 判断是 INSERT 还是 UPDATE 事件
  if (payload.eventType === 'INSERT') {
    console.log(`Detected new record in anchor_tsid table:`);
    tsidChanged = newRecord.norecord === true && newRecord.tsid;
  } else if (payload.eventType === 'UPDATE') {
    tsidChanged = oldRecord.tsid !== newRecord.tsid;
    if (tsidChanged) {
      console.log(`Detected tsid change in anchor_tsid table:`);
    }
  }

  if (tsidChanged) {
    const anchorId = newRecord.anchor_id;
    const recordTime = newRecord.record_time;

    // 获取 anchor_name
    const anchorName = await getAnchorName(anchorId);

    const users = await fetchUserIds(anchorId, 'tsid_change');
    console.log('Users subscribed to the anchor (tsid_change):', users);

    await processEvent(
      {
        type: 'tsid_change',
        anchorName,
        recordTime,
        tsid: newRecord.tsid,
      },
      users
    );
  }
};

// 处理 anchor_mp4id 表的事件
const onMp4idEvent = async (payload) => {
  const newRecord = payload.new;
  const oldRecord = payload.old;

  let mp4idChanged = false;

  // 判断是 INSERT 还是 UPDATE 事件
  if (payload.eventType === 'INSERT') {
    console.log(`Detected new record in anchor_mp4id table:`);
    mp4idChanged = newRecord.norecord === true && newRecord.mp4id;
  } else if (payload.eventType === 'UPDATE') {
    mp4idChanged = oldRecord.mp4id !== newRecord.mp4id;
    if (mp4idChanged) {
      console.log(`Detected mp4id change in anchor_mp4id table:`);
    }
  }

  if (mp4idChanged) {
    const anchorId = newRecord.anchor_id;
    const recordTime = newRecord.record_time;

    // 获取 anchor_name
    const anchorName = await getAnchorName(anchorId);

    const users = await fetchUserIds(anchorId, 'mp4id_change');
    console.log('Users subscribed to the anchor (mp4id_change):', users);

    await processEvent(
      {
        type: 'mp4id_change',
        anchorName,
        recordTime,
        mp4id: newRecord.mp4id,
      },
      users
    );
  }
};

// 创建频道，用于订阅 anchor_tsid 表的更新和插入
const tsidChannel = supabase.channel('anchor-tsid-channel');

// 订阅 anchor_tsid 表的更新和插入事件
tsidChannel.on(
  'postgres_changes',
  {
    event: '*', // 监听所有事件，包括 INSERT 和 UPDATE
    schema: 'public',
    table: 'anchor_tsid',
  },
  onTsidEvent
);

// 创建频道，用于订阅 anchor_mp4id 表的更新和插入
const mp4idChannel = supabase.channel('anchor-mp4id-channel');

// 订阅 anchor_mp4id 表的更新和插入事件
mp4idChannel.on(
  'postgres_changes',
  {
    event: '*', // 监听所有事件，包括 INSERT 和 UPDATE
    schema: 'public',
    table: 'anchor_mp4id',
  },
  onMp4idEvent
);

// 开始订阅
tsidChannel.subscribe((status) => {
  if (status === 'SUBSCRIBED') {
    console.log('Subscribed to anchor_tsid changes');
  }
});

mp4idChannel.subscribe((status) => {
  if (status === 'SUBSCRIBED') {
    console.log('Subscribed to anchor_mp4id changes');
  }
});

// 启动 bot
bot.launch();
console.log('Bot is running and waiting for events (Updated Program)...');

// 优雅停止
process.once('SIGINT', () => bot.stop('SIGINT'));
process.once('SIGTERM', () => bot.stop('SIGTERM'));

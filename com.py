import subprocess
import json
import re

def preprocess_url(url):
    if 'twitch.tv' in url:
        return 'Twitch', url
    elif 'youtube.com' in url or 'youtu.be' in url:
        return 'YouTube', url
    elif 'bilibili.com' in url:
        return 'Bilibili', url  # 修改:直接返回原始URL
    return None, url

def get_stream_info(url):
    platform, preprocessed_url = preprocess_url(url)
    if platform is None:
        return f"错误:不支持的链接格式\n{url}"
    try:
        result = subprocess.run(['streamlink', '--json', preprocessed_url, 'stream'], capture_output=True, text=True)
        data = json.loads(result.stdout)
        if platform == 'Twitch':
            streamer = data['metadata']['author']
            title = data['metadata']['title']
            best_quality = 'audio_only' if 'audio_only' in data['streams'] else list(data['streams'].keys())[-1]
            status = '直播中'
            return f"平台:{platform}\n主播:{streamer}\n标题:{title}\n状态:{status}\n最高画质:{best_quality}"  # 修改:去掉直播流地址
        elif platform == 'YouTube':
            channel = data['metadata']['author']
            vid = data['metadata']['id']
            title = data['metadata']['title']
            best_quality = list(data['streams'].keys())[-1]
            status = '直播中'
            return f"平台:{platform}\n频道:{channel}\n视频ID:{vid}\n标题:{title}\n状态:{status}\n最高画质:{best_quality}"  # 修改:去掉直播流地址
        elif platform == 'Bilibili':
            streamer = data['metadata']['author'] if data['metadata']['author'] else '未知主播'
            category = data['metadata']['category'] if data['metadata']['category'] else '未知分类'
            title = data['metadata']['title'] if data['metadata']['title'] else '未知标题'
            status = '直播中'
            return f"平台:{platform}\n主播:{streamer}\n分类:{category}\n标题:{title}\n状态:{status}"  # 修改:去掉直播间ID和直播流地址
    except:
        try:
            result = subprocess.run(['streamlink', preprocessed_url], capture_output=True, text=True)
            if 'No playable streams found' in result.stdout:
                return f"平台:{platform}\n状态:未开播\n{url}"
            else:
                return f"平台:{platform}\n状态:未开播\n{url}\n错误信息:{result.stdout}"
        except subprocess.CalledProcessError as e:
            return f"平台:{platform}\n状态:未开播\n{url}\n错误信息:{e.output}"

def get_stream_url(url):
    platform, preprocessed_url = preprocess_url(url)
    if platform is None:
        return f"错误:不支持的链接格式\n{url}"
    try:
        result = subprocess.run(['streamlink', preprocessed_url, 'best', '--stream-url'], capture_output=True, text=True)
        best_url = result.stdout.strip()
        return f"平台:{platform}\n最高画质直播流地址:{best_url}"
    except subprocess.CalledProcessError as e:
        return f"平台:{platform}\n错误:streamlink命令执行失败\n{e.output}"

def get_live_info(url):
    return get_stream_info(url)

def get_live_url(url):
    return get_stream_url(url)

def test_api():
    urls = [
        'https://www.twitch.tv/euriece',
        'https://live.bilibili.com/21654762',
        'https://www.youtube.com/watch?v=mKQlRQ-jbPM',
        'https://invalid.link.com'
    ]
    for url in urls:
        print(f"测试直播链接: {url}")
        print(get_live_info(url))
        print(get_live_url(url))
        print()

# 运行测试
test_api()
import asyncio
import logging
import json
import redis
import aiohttp

bot_token = '7148756337:AAHYnQGq18_D9GVhWfj9pzSELIUJxdPtQxA'

redis_config = {
    'host': 'localhost',
    'port': 6379,
    'db': 0
}

# 配置日志记录器
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def process_event(event_data):
    event_type = event_data['type']

    if event_type == 'tsid_change':
        tsid = event_data['tsid']
        user_details = event_data['user_details']
        for user_detail in user_details:
            user_id = user_detail['user_id']
            send_file = user_detail['send_file']
            if send_file:
                await send_document_to_user(user_id, tsid)
    elif event_type == 'mp4id_change':
        mp4id = event_data['mp4id']
        anchor_name = event_data['anchor_name']
        user_details = event_data['user_details']
        for user_detail in user_details:
            user_id = user_detail['user_id']
            send_video = user_detail['send_video']
            if send_video:
                await send_video_to_user(user_id, mp4id, anchor_name)

async def send_document_to_user(user_id, tsid):
    try:
        async with aiohttp.ClientSession() as session:
            url = f"https://api.telegram.org/bot{bot_token}/sendDocument"
            data = {'chat_id': user_id, 'document': tsid}
            async with session.post(url, data=data) as resp:
                if resp.status == 200:
                    logger.info(f"Sent document {tsid} to user {user_id}")
                else:
                    logger.error(f"Failed to send document to user {user_id}: {resp.reason}")
    except Exception as e:
        logger.error(f"Failed to send document to user {user_id}: {e}")

async def send_video_to_user(user_id, mp4id, anchor_name):
    try:
        async with aiohttp.ClientSession() as session:
            url = f"https://api.telegram.org/bot{bot_token}/sendVideo"
            data = {'chat_id': user_id, 'video': mp4id, 'caption': anchor_name}
            async with session.post(url, data=data) as resp:
                if resp.status == 200:
                    logger.info(f"Sent video {mp4id} to user {user_id}")
                else:
                    logger.error(f"Failed to send video to user {user_id}: {resp.reason}")
    except Exception as e:
        logger.error(f"Failed to send video to user {user_id}: {e}")

async def handle_message(message):
    event_data = json.loads(message['data'].decode('utf-8'))
    logger.info(f"Received event: {event_data}")
    await process_event(event_data)

async def main():
    logger.info("Python program is running and waiting for events...")
    r = redis.Redis(**redis_config)
    pubsub = r.pubsub()
    pubsub.subscribe('send')
    while True:
        message = pubsub.get_message()
        if message:
            if message['type'] == 'message':
                await handle_message(message)
        await asyncio.sleep(0.1)

if __name__ == "__main__":
    asyncio.run(main())
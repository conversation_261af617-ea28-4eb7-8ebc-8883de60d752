from pyrogram import Client
import asyncio
import os
import logging
from queue import Queue

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Use your own values for API ID, API Hash, and Bot Token
api_id = '25432929'
api_hash = '965c5d22f0b9d1d0326e84bbb2bb18c1'
bot_token = '6885885686:AAFzTrTl7nerNV9_m59TT9SQ_YKlrK5bKMM'
username = '@juaer' # 发送文件 ID 的目标用户

file_queue = Queue()

async def send_file_and_delete(file_path, anchor_id):
    try:
        logger.info(f"Uploading file: {file_path}")
        # 创建 Pyrogram 客户端
        async with Client("file3", api_id=api_id, api_hash=api_hash, bot_token=bot_token) as app:
            # 发送文件
            sent_message = await app.send_document(username, document=file_path, progress=progress_callback)
            file_id = sent_message.document.file_id
            logger.info(f"File uploaded successfully: {file_path}")
            logger.info(f"File ID: {file_id}")
            logger.info(f"Anchor ID: {anchor_id}")
            # 删除本地文件
            if os.path.exists(file_path):
                os.remove(file_path)
                logger.info(f"Deleted file: {file_path}")
            else:
                logger.warning(f"File not found: {file_path}")
    except Exception as e:
        logger.error(f"Error uploading file: {file_path}. Error: {str(e)}")

async def progress_callback(current, total):
    logger.info(f"Uploaded {current} bytes out of {total} bytes.")

async def main():
    test_files = [
        ('/home/<USER>/DLR/beforeput/recordings/九欧拆卡/九欧拆卡_1711727853.mp4', '1c0ec025-d2ed-4e5e-ad14-7322f1395268'),
        ('/home/<USER>/DLR/beforeput/recordings/九欧拆卡/九欧拆卡_1711727853.ts', '1c0ec025-d2ed-4e5e-ad14-7322f1395268'),# 添加更多的文件和锚点ID
    ]

    for file_path, anchor_id in test_files:
        file_queue.put((file_path, anchor_id))

    while not file_queue.empty():
        file_path, anchor_id = file_queue.get()
        await send_file_and_delete(file_path, anchor_id)
        file_queue.task_done()

if __name__ == '__main__':
    asyncio.run(main())
const { Telegraf } = require('telegraf');
const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

// 获取北京时间的辅助函数
function getBeijingTime() {
    const now = new Date();
    const beijingTime = new Date(now.getTime() + (8 * 60 * 60 * 1000) - (now.getTimezoneOffset() * 60 * 1000));
    return beijingTime.toISOString().replace('T', ' ').substring(0, 19) + ' CST';
}

// 带时间戳的日志函数
function log(...args) {
    console.log(`[${getBeijingTime()}]`, ...args);
}

function error(...args) {
    console.error(`[${getBeijingTime()}]`, ...args);
}

function warn(...args) {
    console.warn(`[${getBeijingTime()}]`, ...args);
}

// Supabase配置
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;
const supabase = createClient(supabaseUrl, supabaseKey, {
  realtime: {
    params: {
      eventsPerSecond: 10
    },
    reconnectAfterMs: (attempts) => {
      // 指数退避：1s, 2s, 4s, 8s, 16s, 最多30s
      const baseDelay = 1000;
      const maxDelay = 30000;
      const delay = Math.min(baseDelay * Math.pow(2, attempts - 1), maxDelay);
      log(`Supabase重连尝试 ${attempts}，等待 ${delay}ms`);
      return delay;
    }
  }
});

// 从环境变量获取tokens
const BOT_TOKENS = [
    process.env.BOT_TOKEN,
    process.env.BOT_TOKEN_SECONDARY,
    process.env.BUTHISBOT
].filter(token => token); // 过滤掉undefined或空值


// 从环境变量获取默认chat id
const DEFAULT_CHAT_ID = process.env.TELEGRAM_CHAT_ID;

// 新增：为beacon消息定义常量
const BUTHISBOT_TOKEN = process.env.BUTHISBOT;
const BEACON_CHAT_ID = '-1002212656401';

/**
 * 使用多个bot token尝试发送file_id
 * @param {string} fileId 要发送的文件ID
 * @param {number} [chatId=DEFAULT_CHAT_ID] 目标聊天ID，默认使用环境变量中的TELEGRAM_CHAT_ID
 * @param {string} mediaType 媒体类型 ('photo', 'video', 'document' 等)
 * @returns {Promise<{success: boolean, error?: Error, usedToken?: string}>}
 */
async function trySendFileId(fileId, chatId = DEFAULT_CHAT_ID, mediaType) {
    if (BOT_TOKENS.length === 0) {
        return {
            success: false,
            error: new Error('没有配置任何bot token')
        };
    }

    for (const token of BOT_TOKENS) {
        try {
            const bot = new Telegraf(token);
            let result;
            
            switch (mediaType) {
                case 'photo':
                    result = await bot.telegram.sendPhoto(chatId, fileId);
                    break;
                case 'video':
                    result = await bot.telegram.sendVideo(chatId, fileId);
                    break;
                case 'document':
                    result = await bot.telegram.sendDocument(chatId, fileId);
                    break;
                // 可以根据需要添加更多类型
            }
            
            return {
                success: true,
                usedToken: token
            };
        } catch (error) {
            log(`使用token ${token.substr(0, 10)}... 尝试失败:`, error.message);
            // 继续尝试下一个token
            continue;
        }
    }
    
    return {
        success: false,
        error: new Error('所有bot token都无法发送该file_id')
    };
}

/**
 * 发送媒体组（多个文件）
 * @param {Array<{type: string, media: string}>} mediaGroup 媒体组数组
 * @param {number} [chatId=DEFAULT_CHAT_ID] 目标聊天ID，默认使用环境变量中的TELEGRAM_CHAT_ID
 * @returns {Promise<{success: boolean, error?: Error, usedToken?: string}>}
 */
async function trySendMediaGroup(mediaGroup, chatId = DEFAULT_CHAT_ID) {
    if (BOT_TOKENS.length === 0) {
        return {
            success: false,
            error: new Error('没有配置任何bot token')
        };
    }

    for (const token of BOT_TOKENS) {
        try {
            const bot = new Telegraf(token);
            await bot.telegram.sendMediaGroup(chatId, mediaGroup);
            
            return {
                success: true,
                usedToken: token
            };
        } catch (error) {
            log(`使用token ${token.substr(0, 10)}... 尝试发送媒体组失败:`, error.message);
            continue;
        }
    }
    
    return {
        success: false,
        error: new Error('所有bot token都无法发送该媒体组')
    };
}

/**
 * 尝试发送未知类型的file_id
 * @param {string} fileId 要发送的文件ID
 * @param {string} token Bot Token
 * @param {number} chatId 目标聊天ID
 * @returns {Promise<boolean>}
 */
async function tryAllMethodsForFileId(fileId, token, chatId) {
    const bot = new Telegraf(token);
    const methods = [
        { name: 'sendDocument', fn: () => bot.telegram.sendDocument(chatId, fileId) },
        { name: 'sendPhoto', fn: () => bot.telegram.sendPhoto(chatId, fileId) },
        { name: 'sendVideo', fn: () => bot.telegram.sendVideo(chatId, fileId) },
        { name: 'sendAnimation', fn: () => bot.telegram.sendAnimation(chatId, fileId) },
        { name: 'sendAudio', fn: () => bot.telegram.sendAudio(chatId, fileId) },
        { name: 'sendVoice', fn: () => bot.telegram.sendVoice(chatId, fileId) },
        { name: 'sendVideoNote', fn: () => bot.telegram.sendVideoNote(chatId, fileId) },
        { name: 'sendSticker', fn: () => bot.telegram.sendSticker(chatId, fileId) }
    ];

    for (const method of methods) {
        try {
            await method.fn();
            log(`成功使用 ${method.name} 发送 file_id: ${fileId.substr(0, 20)}...`);
            return true;
        } catch (error) {
            // 继续尝试下一个方法
            continue;
        }
    }
    return false;
}

/**
 * 批量发送多个file_id
 * @param {string} fileIds 以分号分隔的file_id字符串
 * @param {number} [chatId=DEFAULT_CHAT_ID] 目标聊天ID
 * @returns {Promise<Array<{fileId: string, success: boolean, usedToken?: string}>>}
 */
async function sendMultipleFileIds(fileIds, chatId = DEFAULT_CHAT_ID) {
    const fileIdArray = fileIds.split(';').filter(id => id.trim());
    const results = [];

    for (const fileId of fileIdArray) {
        let success = false;
        let usedToken = null;

        for (const token of BOT_TOKENS) {
            try {
                const sent = await tryAllMethodsForFileId(fileId.trim(), token, chatId);
                if (sent) {
                    success = true;
                    usedToken = token;
                    break;
                }
            } catch (error) {
                log(`使用token ${token.substr(0, 10)}... 尝试失败:`, error.message);
                continue;
            }
        }

        results.push({
            fileId: fileId.trim(),
            success,
            usedToken
        });
    }

    return results;
}

/**
 * 使用指定的bot token发送所有file IDs
 * @param {string[]} fileIds 文件ID数组
 * @param {string} token Bot Token
 * @param {number} chatId 目标聊天ID
 * @returns {Promise<boolean>} 是否全部发送成功
 */
async function sendAllFileIdsWithToken(fileIds, token, chatId) {
    try {
        for (const fileId of fileIds) {
            const sent = await tryAllMethodsForFileId(fileId.trim(), token, chatId);
            if (!sent) {
                log(`使用token ${token.substr(0, 10)}... 发送file_id失败: ${fileId}`);
                return false;
            }
        }
        return true;
    } catch (error) {
        error('发送过程中发生错误:', error);
        return false;
    }
}

/**
 * 处理数据库记录
 * @param {Object} record 数据库记录
 */
async function handleRecord(record) {
    if (!record.failed || !record.failed.includes('file id pending')) {
        return;
    }

    log(`\n处理新记录: ${record.aweme_id}`);
    // 只读取file_id，不修改它
    const fileIds = record.file_id ? record.file_id.split(';').filter(id => id.trim()) : [];
    
    if (fileIds.length === 0) {
        log(`记录 ${record.aweme_id} 没有file_id，跳过处理`);
        return;
    }
    
    for (const token of BOT_TOKENS) {
        log(`尝试使用token: ${token.substr(0, 10)}...`);
        const success = await sendAllFileIdsWithToken(fileIds, token, DEFAULT_CHAT_ID);
        
        if (success) {
            log('所有文件发送成功，更新数据库...');
            try {
                // 只更新failed和bot_token字段
                const { error } = await supabase
                    .from('douyin')
                    .update({
                        failed: 'file id done',
                        bot_token: token
                    })
                    .eq('aweme_id', record.aweme_id);

                if (error) {
                    error('更新数据库失败:', error);
                } else {
                    log(`数据库更新成功：记录 ${record.aweme_id} 的failed字段更新为'file id done'，bot_token字段更新为使用的token`);
                }
            } catch (error) {
                error('数据库操作错误:', error);
            }
            return;
        }
        
        log(`使用token ${token.substr(0, 10)}... 发送失败，尝试下一个token`);
    }
    
    log(`所有token都无法成功发送所有文件，记录 ${record.aweme_id} 保持pending状态`);
}


// 存储订阅对象
let douyinSubscription = null;
let doesSubscription = null;

// 重连相关变量
let douyinReconnectAttempts = 0;
let doesReconnectAttempts = 0;
let douyinReconnectTimer = null;
let doesReconnectTimer = null;

// 计算重连延迟（指数退避）
function calculateReconnectDelay(attempt) {
  const baseDelay = 1000; // 1秒
  const maxDelay = 30000; // 30秒
  const delay = Math.min(baseDelay * Math.pow(2, attempt - 1), maxDelay);
  return delay;
}

// 清理并重新订阅douyin频道
async function reconnectDouyinChannel() {
    douyinReconnectAttempts++;
    const delay = calculateReconnectDelay(douyinReconnectAttempts);
    
    warn(`准备重连 douyin 频道，第 ${douyinReconnectAttempts} 次尝试，延迟 ${delay}ms`);
    
    // 清理现有频道
    if (douyinSubscription) {
        try {
            await supabase.removeChannel(douyinSubscription);
            log("已移除旧的 douyin 频道");
        } catch (e) {
            error(`移除 douyin 频道失败: ${e}`);
        }
        douyinSubscription = null;
    }
    
    // 延迟后重新连接
    douyinReconnectTimer = setTimeout(() => {
        subscribeDouyinTable();
    }, delay);
}

// 清理并重新订阅does频道
async function reconnectDoesChannel() {
    doesReconnectAttempts++;
    const delay = calculateReconnectDelay(doesReconnectAttempts);
    
    warn(`准备重连 does 频道，第 ${doesReconnectAttempts} 次尝试，延迟 ${delay}ms`);
    
    // 清理现有频道
    if (doesSubscription) {
        try {
            await supabase.removeChannel(doesSubscription);
            log("已移除旧的 does 频道");
        } catch (e) {
            error(`移除 does 频道失败: ${e}`);
        }
        doesSubscription = null;
    }
    
    // 延迟后重新连接
    doesReconnectTimer = setTimeout(() => {
        subscribeDoesTable();
    }, delay);
}

// 订阅douyin表
function subscribeDouyinTable() {
    // 清理重连定时器
    if (douyinReconnectTimer) {
        clearTimeout(douyinReconnectTimer);
        douyinReconnectTimer = null;
    }
    
    douyinSubscription = supabase
        .channel('douyin_changes')
        .on(
            'postgres_changes',
            {
                event: '*',
                schema: 'public',
                table: 'douyin'
            },
            async (payload) => {
                if (payload.new && payload.new.failed && payload.new.failed.includes('file id pending')) {
                    log('检测到新的pending记录 (douyin表)');
                    await handleRecord(payload.new);
                }
            }
        )
        .subscribe((status, err) => {
            if (err) {
                error('douyin表订阅失败:', err);
            } else {
                log('成功订阅douyin表变更，状态:', status);
                if (status === 'SUBSCRIBED') {
                    douyinReconnectAttempts = 0;  // 重置重连计数
                    log('douyin表订阅已成功建立连接');
                }
            }
        });
    
    // 监听频道状态变化
    douyinSubscription.on('system', {}, (payload) => {
        log(`douyin频道系统事件: ${JSON.stringify(payload)}`);
        
        // 处理错误状态
        if (payload.extension === 'system' && 
            ['CHANNEL_ERROR', 'TIMED_OUT', 'CLOSED'].includes(payload.status)) {
            error(`douyin频道异常状态: ${payload.status}`);
            reconnectDouyinChannel();
        }
    });
}

// 订阅does表
function subscribeDoesTable() {
    // 清理重连定时器
    if (doesReconnectTimer) {
        clearTimeout(doesReconnectTimer);
        doesReconnectTimer = null;
    }
    
    doesSubscription = supabase
        .channel('does_beacon_changes')
        .on(
            'postgres_changes',
            {
                event: '*',
                schema: 'public',
                table: 'does'
            },
            async (payload) => {
                let triggerBeacon = false;
                let triggerReason = "";

                if (payload.eventType === 'UPDATE') {
                    // 检查 keep 字段变化
                    if (payload.new && payload.old && payload.new.hasOwnProperty('keep') && 
                        payload.new.keep !== payload.old.keep && 
                        typeof payload.new.keep === 'boolean') {
                        
                        triggerBeacon = true;
                        const oldKeepInfo = payload.old.keep === null ? "null" : payload.old.keep.toString();
                        const newKeepInfo = payload.new.keep === null ? "null" : payload.new.keep.toString();
                        triggerReason = `keep ${oldKeepInfo} 变为 ${newKeepInfo}`;
                        log(`检测到does表keep字段更新: 从 ${oldKeepInfo} 到 ${newKeepInfo}`);
                    }
                    
                    // 检查 scanall 字段变化
                    if (payload.new && payload.old && payload.new.hasOwnProperty('scanall') && 
                        payload.new.scanall !== payload.old.scanall && 
                        typeof payload.new.scanall === 'boolean') {
                        
                        triggerBeacon = true;
                        const oldScanallInfo = payload.old.scanall === null ? "null" : payload.old.scanall.toString();
                        const newScanallInfo = payload.new.scanall === null ? "null" : payload.new.scanall.toString();
                        if (triggerReason) {
                            triggerReason += `, scanall ${oldScanallInfo} 变为 ${newScanallInfo}`;
                        } else {
                            triggerReason = `scanall ${oldScanallInfo} 变为 ${newScanallInfo}`;
                        }
                        log(`检测到does表scanall字段更新: 从 ${oldScanallInfo} 到 ${newScanallInfo}`);
                    }
                }

                if (triggerBeacon) {
                    try {
                        const bot = new Telegraf(BUTHISBOT_TOKEN);
                        await bot.telegram.sendMessage(BEACON_CHAT_ID, 'beacon test');
                        log(`成功发送 'beacon test' 消息到 ${BEACON_CHAT_ID} 使用 BUTHISBOT。触发原因: ${triggerReason}`);
                    } catch (err) {
                        error(`使用BUTHISBOT发送 'beacon test' 到 ${BEACON_CHAT_ID} 失败:`, err.message);
                    }
                }
            }
        )
        .subscribe((status, err) => {
            if (err) {
                error('does表订阅失败:', err);
            } else {
                log('成功订阅does表变更，状态:', status);
                if (status === 'SUBSCRIBED') {
                    doesReconnectAttempts = 0;  // 重置重连计数
                    log('does表订阅已成功建立连接');
                }
            }
        });
    
    // 监听频道状态变化
    doesSubscription.on('system', {}, (payload) => {
        log(`does频道系统事件: ${JSON.stringify(payload)}`);
        
        // 处理错误状态
        if (payload.extension === 'system' && 
            ['CHANNEL_ERROR', 'TIMED_OUT', 'CLOSED'].includes(payload.status)) {
            error(`does频道异常状态: ${payload.status}`);
            reconnectDoesChannel();
        }
    });
}

/**
 * 启动Supabase实时监听
 */
async function startRealtimeListener() {
    log('启动Supabase实时监听...');
    
    // 订阅douyin表的变更
    subscribeDouyinTable();

    // 新增：订阅does表的变更以发送beacon消息
    if (BUTHISBOT_TOKEN) {
        subscribeDoesTable();
    } else {
        warn('警告: 未配置BUTHISBOT token，无法监听does表变更并发送beacon消息。');
    }


    // 检查现有的pending记录
    try {
        // 检查douyin表
        const { data, error } = await supabase
            .from('douyin')
            .select('*')
            .ilike('failed', '%file id pending%');

        if (error) {
            error('查询现有pending记录失败:', error);
        } else if (data && data.length > 0) {
            log(`发现${data.length}条现有pending记录(douyin表)，开始处理...`);
            for (const record of data) {
                await handleRecord(record);
            }
        }

    } catch (error) {
        error('查询数据库错误:', error);
    }
}

/**
 * 主函数
 */
async function main() {
    if (!supabaseUrl || !supabaseKey) {
        error('错误: 缺少Supabase配置');
        return;
    }

    // 移除了对BOT_TOKENS.length的严格检查，因为某些功能可能只需要特定的token
    // if (!BOT_TOKENS.length) {
    //     console.error('错误: 没有配置bot tokens');
    //     return;
    // }

    if (!DEFAULT_CHAT_ID && !BUTHISBOT_TOKEN) { // 检查是否至少配置了一个必要的ID或token
        error('错误: TELEGRAM_CHAT_ID 和 BUTHISBOT 均未配置，程序可能无法正常运行核心功能。');
        // return; // 根据需求决定是否在此处退出
    } else if (!DEFAULT_CHAT_ID) {
        warn('警告: 未配置TELEGRAM_CHAT_ID，部分功能可能受限。');
    }
    

    log('配置检查基本通过，开始运行...');
    if (BOT_TOKENS.length > 0) {
        log('可用的bot tokens数量:', BOT_TOKENS.length, '个');
    } else {
        warn('警告: 未配置任何 BOT_TOKENS (BOT_TOKEN, BOT_TOKEN_SECONDARY)。');
    }
    if (DEFAULT_CHAT_ID) {
        log('默认目标chat ID (TELEGRAM_CHAT_ID):', DEFAULT_CHAT_ID);
    }
    if (BUTHISBOT_TOKEN && BEACON_CHAT_ID) {
        log(`Beacon消息将使用BUTHISBOT token发送到Chat ID: ${BEACON_CHAT_ID}`);
    }


    await startRealtimeListener();
    
    // 添加状态监控定时器（每30秒输出一次状态）
    setInterval(() => {
        const douyinStatus = douyinSubscription ? douyinSubscription._state : 'NOT_INITIALIZED';
        const doesStatus = doesSubscription ? doesSubscription._state : 'NOT_INITIALIZED';
        const douyinRetries = douyinReconnectAttempts > 0 ? `(重连次数: ${douyinReconnectAttempts})` : '';
        const doesRetries = doesReconnectAttempts > 0 ? `(重连次数: ${doesReconnectAttempts})` : '';
        log(`订阅状态检查 - douyin表: ${douyinStatus} ${douyinRetries}, does表: ${doesStatus} ${doesRetries}`);
    }, 30 * 1000);

    // 保持程序运行
    process.on('SIGINT', async () => {
        log('正在关闭程序...');
        
        // 清理重连定时器
        if (douyinReconnectTimer) {
            clearTimeout(douyinReconnectTimer);
        }
        if (doesReconnectTimer) {
            clearTimeout(doesReconnectTimer);
        }
        
        // 清理订阅
        if (douyinSubscription) {
            try {
                await supabase.removeChannel(douyinSubscription);
                log('已清理douyin频道');
            } catch (e) {
                error(`清理douyin频道失败: ${e}`);
            }
        }
        if (doesSubscription) {
            try {
                await supabase.removeChannel(doesSubscription);
                log('已清理does频道');
            } catch (e) {
                error(`清理does频道失败: ${e}`);
            }
        }
        
        process.exit();
    });
    
    // 处理未捕获的异常
    process.on('uncaughtException', (err) => {
        error('未捕获的异常:', err);
    });
    
    // 处理未处理的Promise拒绝
    process.on('unhandledRejection', (reason, promise) => {
        error('未处理的Promise拒绝:', reason);
    });
}

// 如果直接运行此文件，则执行main函数
if (require.main === module) {
    main().catch(error);
}

// 导出函数供其他文件使用
module.exports = {
    trySendFileId,
    trySendMediaGroup,
    sendMultipleFileIds,
    BOT_TOKENS,
    DEFAULT_CHAT_ID
}; 
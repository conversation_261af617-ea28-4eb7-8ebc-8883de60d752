import os
import subprocess
import time
import threading
import queue
import redis
import logging

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def get_video_info(video_path):
    # 生成缩略图
    thumb_path = video_path + ".jpg"  # 缩略图的文件路径
    cmd_thumbnail = [
        "ffmpeg", "-i", video_path, "-ss", "00:00:05", "-vframes", "1", 
        thumb_path
    ]
    subprocess.run(cmd_thumbnail, stdout=subprocess.PIPE, stderr=subprocess.PIPE)

    # 获取视频的宽度、高度和持续时间
    cmd_dimensions = [
        "ffprobe", "-v", "error", "-select_streams", "v:0", 
        "-show_entries", "stream=width,height", 
        "-of", "default=noprint_wrappers=1:nokey=1", video_path
    ]
    dimensions_output = subprocess.run(cmd_dimensions, stdout=subprocess.PIPE, text=True).stdout.splitlines()

    cmd_duration = [
        "ffprobe", "-v", "error", "-show_entries", "format=duration", 
        "-of", "default=noprint_wrappers=1:nokey=1", video_path
    ]
    duration_output = subprocess.run(cmd_duration, stdout=subprocess.PIPE, text=True).stdout.strip()

    try:
        if dimensions_output and duration_output:
            return {
                'width': int(dimensions_output[0]),
                'height': int(dimensions_output[1]),
                'duration': int(float(duration_output)),
                'thumb_path': thumb_path if os.path.exists(thumb_path) else None
            }
        else:
            return None
    except Exception as e:
        logger.error(f"获取视频信息失败: {e}")
        return None

# 创建一个队列来存储录制任务
record_queue = queue.Queue()

def record_stream(stream_url, anchor_id, anchor_name, output_dir, max_size_ts=2*1000*1000*1000):
    logger.info(f"开始录制主播 {anchor_name} (ID: {anchor_id}) 的直播流")

    # 发送开始录制的消息到 "id" 频道
    start_message = f"start,{anchor_id}"
    r.publish("id", start_message)
    logger.info(f"Sent message to Redis channel 'id': {start_message}")

    # 创建输出目录
    subdir = os.path.join(output_dir, anchor_name)
    os.makedirs(subdir, exist_ok=True)

    # 生成输出文件名
    timestamp = int(time.time())
    output_file_ts = os.path.join(subdir, f"{anchor_name}_{timestamp}.ts")
    output_file_mp4 = os.path.join(subdir, f"{anchor_name}_{timestamp}.mp4")

    max_retries = 3
    retry_delay = 5

    for retry in range(max_retries):
        # 构建 ffmpeg 命令
        cmd_ts = [
            "ffmpeg",
            "-i", stream_url,
            "-c:v", "copy",
            "-c:a", "copy",
            "-map", "0",
            "-f", "mpegts",
            "-fs", str(max_size_ts),
            "-timeout", "10000000",  # 设置超时时间为 10000 秒
            "-bufsize", "2000k",  # 设置网络缓冲区大小为 2000KB
            output_file_ts
        ]

        logger.info(f"开始录制 TS 文件: {output_file_ts}")
        start_time = time.time()
        process_ts = subprocess.Popen(cmd_ts, stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
        process_ts.wait()
        end_time = time.time()

        if process_ts.returncode == 0 or os.path.exists(output_file_ts):
            logger.info(f"TS 录制完成,耗时: {end_time - start_time:.2f} 秒")
            print(f"TS 录制完成: {output_file_ts}")

            # 发送 TS 文件生成消息到 Redis
            message = f'{anchor_id},{output_file_ts}'
            channel_index = record_queue.counter % 4
            channel_name = f'file{channel_index + 1}'
            r.publish(channel_name, message)
            logger.info(f"Sent message to Redis channel {channel_name}: {message}")
            record_queue.counter += 1
            
            # 获取 TS 文件信息
            ts_info = get_video_info(output_file_ts)
            
            # 转换为 MP4 格式
            cmd_mp4 = [
                "ffmpeg",
                "-i", output_file_ts,
                "-c", "copy",
                output_file_mp4
            ]
            try:
                logger.info(f"开始转换为 MP4 格式: {output_file_mp4}")
                start_time = time.time()
                process_mp4 = subprocess.Popen(cmd_mp4, stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
                process_mp4.wait()
                end_time = time.time()
            except Exception as e:
                error_message = f"MP4 转换错误: {str(e)}"
                print(error_message)
                logger.error(error_message)
                return

            if process_mp4.returncode == 0:
                logger.info(f"MP4 转换完成,耗时: {end_time - start_time:.2f} 秒")
                print(f"MP4 转换完成: {output_file_mp4}")

                if ts_info:
                    # 发送 Anchor ID、MP4 文件路径和文件信息到 Redis 的对应频道
                    file_info = f"{anchor_id},{output_file_mp4},{ts_info['width']},{ts_info['height']},{ts_info['duration']},{ts_info['thumb_path']}"
                    
                    # 使用轮询调度策略选择频道
                    channel_index = record_queue.counter % 4
                    channel_name = f'file{channel_index + 1}'
                    r.publish(channel_name, file_info)
                    logger.info(f"Sent message to Redis channel {channel_name}: {file_info}")
                    
                    record_queue.counter += 1
                else:
                    error_message = f"获取 TS 文件信息失败: {output_file_ts}"
                    print(error_message)
                    logger.error(error_message)
            else:
                error_message = f"MP4 转换错误: {process_mp4.returncode}"
                print(error_message)
                logger.error(error_message)
            break
        else:
            error_message = f"TS 录制错误: {process_ts.returncode},重试 {retry + 1}/{max_retries}"
            print(error_message)
            logger.error(error_message)
            time.sleep(retry_delay)
    else:
        error_message = f"TS 录制错误: {process_ts.returncode},已达到最大重试次数"
        print(error_message)
        logger.error(error_message)

    # 发送结束录制的消息到 "id" 频道
    end_message = f"end,{anchor_id}"
    r.publish("id", end_message)
    logger.info(f"Sent message to Redis channel 'id': {end_message}")

def worker():
    while True:
        item = record_queue.get()
        if item is None:
            break
        record_stream(*item)
        record_queue.task_done()

if __name__ == "__main__":
    output_dir = "./recordings"
    num_worker_threads = 50

    # 连接 Redis
    r = redis.Redis(host='localhost', port=6379, db=0)

    threads = []
    for i in range(num_worker_threads):
        t = threading.Thread(target=worker)
        t.start()
        threads.append(t)

    # 初始化计数器
    record_queue.counter = 0

    # 订阅 Redis 频道
    pubsub = r.pubsub()
    pubsub.subscribe('record')

    # 监听 Redis 消息
    for message in pubsub.listen():
        if message['type'] == 'message':
            data = message['data'].decode('utf-8')
            logger.info(f"Received message from Redis: {data}")
            if data.startswith('start'):
                _, anchor_id, anchor_name, stream_url = data.split(',')
                record_queue.put((stream_url, anchor_id, anchor_name, output_dir))
            elif data == 'stop':
                # 停止工作线程
                for i in range(num_worker_threads):
                    record_queue.put(None)
                for t in threads:
                    t.join()
                break

    # 取消订阅 Redis 频道
    pubsub.unsubscribe('record')
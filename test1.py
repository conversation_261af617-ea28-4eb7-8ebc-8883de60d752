from typing import Union, Dict, Any

import re
import json
import urllib.request

def get_req(
    url: str,
    proxy_addr: Union[str, None] = None,
    headers: Union[dict, None] = None,
    data: Union[dict, bytes, None] = None,
    json_data: dict = None,
    timeout: int = 20,
    abroad: bool = False
) -> Union[str, Any]:

    if headers is None:
        headers = {}
    try:
        if proxy_addr:
            proxies = {
                'http': proxy_addr,
                'https': proxy_addr
            }
            if data or json_data:
                response = requests.post(url, data=data, json=json_data, headers=headers, proxies=proxies, timeout=timeout)
            else:
                response = requests.get(url, headers=headers, proxies=proxies, timeout=timeout)
            resp_str = response.text
        else:
            if data and not isinstance(data, bytes):
                data = urllib.parse.urlencode(data).encode('utf-8')
            if json_data and isinstance(json_data, dict):
                data = json.dumps(json_data).encode('utf-8')

            req = urllib.request.Request(url, data=data, headers=headers)

            try:
                if abroad:
                    with urllib.request.urlopen(req, timeout=timeout) as response:
                        resp_str = response.read().decode('utf-8')
                else:
                    with opener.open(req, timeout=timeout) as response:
                        resp_str = response.read().decode('utf-8')
            except urllib.error.HTTPError as e:
                if e.code == 400:
                    resp_str = e.read().decode('utf-8')
                else:
                    raise
            except urllib.error.URLError as e:
                print("URL Error:", e)
                raise
            except Exception as e:
                print("An error occurred:", e)
                raise

    except Exception as e:
        resp_str = str(e)

    return resp_str


from web_rid import get_live_room_id, get_sec_user_id

def get_douyin_live_info(url: str, cookies: Union[str, None] = None, proxy_addr: Union[str, None] = None, anchor_id: str = '') -> dict:
    try:
        if url.startswith('https://live.douyin.com/'):
            douyin_url = url
        elif url.startswith('https://v.douyin.com/'):
            room_id, sec_user_id = get_sec_user_id(url)
            web_rid = get_live_room_id(room_id, sec_user_id)
            douyin_url = "https://live.douyin.com/" + str(web_rid)
        else:
            return {'error': '链接不支持'}

        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/115.0',
            'Accept-Language': 'zh-CN,zh;q=0.8,zh-TW;q=0.7,zh-HK;q=0.5,en-US;q=0.3,en;q=0.2',
            'Referer': 'https://live.douyin.com/',
            'Cookie': cookies if cookies else ''
        }
        html_str = get_req(url=douyin_url, proxy_addr=proxy_addr, headers=headers)
        match_json_str = re.search(r'(\{\\"state\\":.*?)]\\n"]\)', html_str)
        if match_json_str:
            json_str = match_json_str.group(1)
            json_str = json_str.replace('\\', '').replace(r'u0026', r'&')
            room_store = re.search('"roomStore":(.*?),"linkmicStore"', json_str, re.S).group(1)
            anchor_name = re.search('"nickname":"(.*?)","avatar_thumb', room_store, re.S).group(1)
            room_store = room_store.split(',"has_commerce_goods"')[0] + '}}}'
            json_data = json.loads(room_store)['roomInfo']['room']
            room_status = json_data['status']
            status = True if room_status == 2 else False
            title = json_data['title'] if status else ''
            stream_url = re.sub(r'_\w+\.m3u8', '_or4.m3u8', json_data['stream_url']['hls_pull_url_map']['BD']) if status else ''

            result = {
                'anchor_id': anchor_id,
                'new_url': url,
                'anchor_name': anchor_name,
                'status': status,
                'title': title,
                'stream_url': stream_url
            }
            return result
        else:
            return {'error': 'Failed to parse Douyin live data'}
    
    except Exception as e:
        return {'error': str(e)}


def get_tiktok_live_info(url: str, cookies: Union[str, None] = None, proxy_addr: Union[str, None] = None, anchor_id: str = '') -> dict:
    try:
        if not re.match(r'https://www.tiktok.com/@[\w.-]+/live', url):
            return {'error': '链接不支持'}

        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/114.0.1823.79',
            'Cookie': cookies if cookies else ''
        }
        html_str = get_req(url=url, proxy_addr=proxy_addr, headers=headers, abroad=True)
        json_str = re.findall('<script id="SIGI_STATE" type="application/json">(.*?)</script><script id="SIGI_RETRY" type="application/json">', html_str)[0]
        json_data = json.loads(json_str)
        
        if isinstance(json_data, dict) and 'LiveRoom' in json_data:
            live_room = json_data['LiveRoom']['liveRoomUserInfo']
            unique_id = live_room['user']['uniqueId'] if 'user' in live_room else ''
            anchor_name = live_room['user']['nickname'] if 'user' in live_room else ''
            status = live_room['user']['status'] == 2 if 'user' in live_room else False
            title = live_room['liveRoom']['title'] if 'liveRoom' in live_room else ''
            url = f"https://www.tiktok.com/@{unique_id}/live"
            stream_url = live_room.get('liveRoom', {}).get('streamData', {}).get('pull_data', {}).get('stream_data', {}).get('data', {}).get('origin', {}).get('main', {}).get('hls', '')

            result = {
                'anchor_id': anchor_id,
                'new_url': url,
                'anchor_name': anchor_name,
                'status': status,
                'title': title,
                'stream_url': stream_url
            }
            return result
        else:
            return {'error': 'Failed to parse TikTok live data'}
    
    except Exception as e:
        return {'error': str(e)}


def parse_live_url(url: str, cookies: Union[str, None] = None, proxy_addr: Union[str, None] = None, anchor_id: str = '') -> Dict[str, Union[str, bool]]:
    if 'douyin.com' in url:
        result = get_douyin_live_info(url, cookies=cookies, proxy_addr=proxy_addr, anchor_id=anchor_id)
    elif 'tiktok.com/' in url:
        result = get_tiktok_live_info(url, cookies=cookies, proxy_addr=proxy_addr, anchor_id=anchor_id)
    else:
        result = {'error': '链接不支持'}

    return result


# 测试链接
test_urls = [
    'https://live.douyin.com/123456',                        # 抖音直播间长链接
    'https://v.douyin.com/abc123/',                          # 抖音短链接
    'https://www.douyin.com/video/7360320152107339046',      # 抖音视频链接(返回错误)
    'https://www.tiktok.com/@username/live',                 # TikTok直播间链接
    'https://www.tiktok.com/@seraahr/video/',                # TikTok视频链接(返回错误)
    'https://example.com/invalid'                            # 无效链接(返回错误)
]

for url in test_urls:
    result = parse_live_url(url)
    print(f"链接: {url}")
    print(f"解析结果: {result}")
    print("------------------------")


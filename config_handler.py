import re
import logging
import requests
from urllib.parse import quote
from supabase import create_client, Client

logging.basicConfig(filename='app.log', level=logging.ERROR,
                    format='%(asctime)s - %(levelname)s - %(message)s')

# 初始化 Supabase 客户端
supabase_url = 'https://wjanjmsywbydjbfrdkaz.supabase.co'  # 替换为您的 Supabase 项目 URL
supabase_key = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6IndqYW5qbXN5d2J5ZGpiZnJka2F6Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3MjAxMjc1OTEsImV4cCI6MjAzNTcwMzU5MX0.ny-61EeDBsVRUr6BdWiamt9oV-6z2TC_f01FJff-9fE'  # 替换为您的 Supabase 服务密钥

supabase: Client = create_client(supabase_url, supabase_key)

PATTERNS = {
    "Douyin": r"https://(?:live\.douyin\.com|v\.douyin\.com)/[^\s]+",
    "TikTok": r"https://(?:www\.tiktok\.com/(?:@[^\s]+/live|t/[^\s]+)|vt\.tiktok\.com/[^\s]+)",
    "Bilibili": r"https://live\.bilibili\.com/\d+",
    "Twitch": r"https://(?:www\.)?twitch\.tv/[^\s]+",
    "YouTube": r"https://(?:www\.)?youtube\.com/watch\?v=[\w-]+",
}

def check_subscription_limit(user_id, allowance):
    response = supabase.table('user_subscriptions').select('user_id', count='exact').eq('user_id', str(user_id)).execute()
    count = response.count
    return count >= allowance

def is_douyin_video(url):
    encoded_url = quote(url, safe='')
    api_url = f"http://127.0.0.1:8080/api/hybrid/video_data?url={encoded_url}&minimal=true"
    try:
        response = requests.get(api_url)
        if response.status_code == 400:
            return False  # Not a video
        return True  # Is a video
    except requests.exceptions.RequestException as e:
        logging.error(f"Error checking if Douyin link is a video: {e}")
        return True

def add_link(user_id, user_input):
    for platform, pattern in PATTERNS.items():
        match = re.search(pattern, user_input)
        if match:
            url = match.group()
            if platform in ["Douyin", "TikTok"] and is_douyin_video(url):
                return False, "Sorry, video links are not allowed. Please provide a live stream share link.", None
            try:
                # 不用upsert，每次都insert，new_url和live_url都填入相同的链接
                response = supabase.table('anchors').insert({'live_url': url, 'new_url': url}).execute()
                anchor_id = response.data[0]['anchor_id']

                response_sub = supabase.table('user_subscriptions').select('*').eq('user_id', str(user_id)).eq('anchor_id', anchor_id).execute()
                # 不需要去重逻辑，即使用户重复添加同样的链接也会生成新anchor_id
                response_user = supabase.table('users').select('allowance', 'tier').eq('user_id', str(user_id)).execute()
                if response_user.data:
                    allowance = response_user.data[0]['allowance']
                    tier = response_user.data[0]['tier']
                else:
                    # 默认插入用户数据
                    allowance = 2
                    tier = 'free'
                    supabase.table('users').insert({'user_id': str(user_id), 'tier': 'free', 'allowance':2, 'manage':0}).execute()

                if check_subscription_limit(user_id, allowance):
                    return False, f"You have reached your subscription limit of {allowance}. Please consider upgrading your subscription plan.", {'upgrade_options': True}

                supabase.table('user_subscriptions').insert({'user_id': str(user_id), 'anchor_id': anchor_id}).execute()
            except Exception as e:
                logging.error(f"Error adding link: {e}")
                return False, "An error occurred while adding the link.", None
            return True, "Live stream link has been successfully added to your subscription list.", None
    return False, "No valid link detected. Please ensure you are pasting a TikTok or Douyin share link.", None

def list_anchors(user_id):
    response = supabase.table('user_subscriptions').select('anchor_id').eq('user_id', str(user_id)).execute()
    anchor_ids = [item['anchor_id'] for item in response.data]
    if anchor_ids:
        response = supabase.table('anchors').select('anchor_id', 'anchor_name', 'live_url').in_('anchor_id', anchor_ids).execute()
        anchors = [(row['anchor_id'], row.get('anchor_name'), row['live_url']) for row in response.data]
    else:
        anchors = []
    return anchors

def delete_anchor(user_id, anchor_id):
    try:
        supabase.table('user_subscriptions').delete().eq('user_id', str(user_id)).eq('anchor_id', anchor_id).execute()
        # 不需要对anchors表检查订阅数为0后更新标记，这里忽略
        logging.info(f"Anchor with ID {anchor_id} has been deleted from the subscription list for user with ID {user_id}.")
        return True
    except Exception as e:
        logging.error(f"Error deleting anchor: {e}")
        return False

def get_anchor_name(anchor_id):
    response = supabase.table('anchors').select('anchor_name').eq('anchor_id', anchor_id).execute()
    if response.data:
        return response.data[0].get('anchor_name')
    else:
        return None

def get_live_url(anchor_id):
    response = supabase.table('anchors').select('live_url').eq('anchor_id', anchor_id).execute()
    if response.data:
        return response.data[0]['live_url']
    else:
        return None

def start_command(user_id, username):
    try:
        response = supabase.table('users').select('*').eq('user_id', str(user_id)).execute()
        if not response.data:
            supabase.table('users').insert({'user_id': str(user_id), 'username': username, 'tier': 'free', 'allowance': 2, 'manage':0}).execute()
            links_quota = 2
            response_text = f"Welcome! You have successfully registered.\n\nYour Information:\nUser ID: {user_id}\nUsername: {username}\nlinks quota: {links_quota}"
        else:
            user_data = response.data[0]
            links_quota = user_data.get('allowance', 2)
            response_text = f"Welcome back!\n\nYour Information:\nUser ID: {user_id}\nUsername: {username}\nlinks quota: {links_quota}"
        return response_text
    except Exception as e:
        logging.error(f"Error in start_command: {e}")
        return "An error occurred while processing your request."

def get_live_anchors(user_id):
    try:
        response = supabase.table('user_subscriptions').select('anchor_id').eq('user_id', str(user_id)).execute()
        anchor_ids = [item['anchor_id'] for item in response.data]
        if anchor_ids:
            response = supabase.table('anchors').select('anchor_name', 'live_url', 'status').in_('anchor_id', anchor_ids).execute()
            # status为True表示正在直播
            live_anchors = [(row['anchor_name'], row['live_url']) for row in response.data if row.get('status') == True]
        else:
            live_anchors = []
        return live_anchors
    except Exception as e:
        logging.error(f"Error getting live anchors: {e}")
        return []

def get_user_allowance_and_tier(user_id):
    response = supabase.table('users').select('allowance', 'tier').eq('user_id', str(user_id)).execute()
    if response.data:
        allowance = response.data[0]['allowance']
        tier = response.data[0]['tier']
    else:
        allowance = 2
        tier = 'free'
        supabase.table('users').insert({'user_id': str(user_id), 'tier': 'free', 'allowance':2, 'manage':0}).execute()
    return allowance, tier

def get_push_settings(user_id):
    response = supabase.table('users').select('push_notification', 'send_video').eq('user_id', str(user_id)).execute()
    if response.data:
        result = response.data[0]
        return {
            "push_notification": result.get('push_notification', False),
            "send_video": result.get('send_video', False)
        }
    return {"push_notification": False, "send_video": False}

def set_push_setting(user_id, setting_type, value):
    valid_settings = ["push_notification", "send_video"]
    if setting_type not in valid_settings:
        return False
    supabase.table('users').update({setting_type: value}).eq('user_id', str(user_id)).execute()
    return True

def set_user_tier(user_id, tier_value):
    try:
        allowance_map = {
            'free': 2,
            '2.98': 5,
            '4.98': 10,
            '9.98': 20
        }
        new_allowance = allowance_map.get(tier_value, 2)
        supabase.table('users').update({'tier': tier_value, 'allowance': new_allowance}).eq('user_id', str(user_id)).execute()
        return True
    except Exception as e:
        logging.error(f"Error setting user tier: {e}")
        return False

def get_user_tier(user_id):
    response = supabase.table('users').select('tier').eq('user_id', str(user_id)).execute()
    if response.data:
        return response.data[0]['tier']
    else:
        return 'free'

def set_user_timezone(user_id, timezone):
    offset = timezone_to_offset(timezone)
    supabase.table('users').update({'timezone': offset}).eq('user_id', str(user_id)).execute()
    return True

def get_user_timezone(user_id):
    response = supabase.table('users').select('timezone').eq('user_id', str(user_id)).execute()
    if response.data:
        offset = response.data[0]['timezone']
    else:
        offset = 0
    return offset_to_timezone(offset)

def offset_to_timezone(offset):
    if offset == 0:
        return "GMT"
    sign = "+" if offset > 0 else "-"
    return f"GMT{sign}{abs(offset)}"

def timezone_to_offset(timezone):
    if timezone == "GMT":
        return 0
    offset_str = timezone.replace(" ", "")[3:]
    if offset_str.startswith("+"):
        offset_str = offset_str[1:]
    return int(offset_str)

def manage_payment(user_id):
    response = supabase.table('users').select('manage').eq('user_id', str(user_id)).execute()
    if response.data:
        manage_val = response.data[0].get('manage',0)
    else:
        manage_val = 0

    manage_val += 1
    supabase.table('users').update({'tier':'free', 'allowance':2, 'manage':manage_val}).eq('user_id', str(user_id)).execute()

generate_gmt_timezones = lambda: [f"GMT{'+' if i > 0 else ''}{i:+d}" for i in range(-12, 15)]
GMT_TIMEZONES = generate_gmt_timezones()

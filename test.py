import requests
from urllib.parse import quote

def is_douyin_video(url):
    if not url:
        return False

    encoded_url = quote(url, safe='')
    api_url = f"http://127.0.0.1:8080/api/hybrid/video_data?url={encoded_url}&minimal=true"
    try:
        response = requests.get(api_url)
        response.raise_for_status()  # 如果返回的状态码不是200,则抛出异常
        data = response.json()
        print("API Response:", data)  # 打印API响应数据
        print("Data:", data.get('data', {}))  # 打印data字段的值
        return data.get('data', {}).get('type') == 'video'
    except requests.exceptions.RequestException as e:
        print("Error occurred:", e)  # 打印出错误信息
        return False

def test_is_douyin_video():
    # 测试用例1: 有效的抖音视频链接
    url1 = "https://v.douyin.com/i2V3d9fW/"
    assert is_douyin_video(url1) == True
    print("All test cases passed!")

# 运行测试
test_is_douyin_video()
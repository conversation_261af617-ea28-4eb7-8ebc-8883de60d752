import os
from pyrogram import Client
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Telegram API配置
api_id = '25432929'
api_hash = '965c5d22f0b9d1d0326e84bbb2bb18c1'
bot_token = '6885885686:AAFzTrTl7nerNV9_m59TT9SQ_YKlrK5bKMM'

# 视频文件配置
video_file_path = "/home/<USER>/DLR/beforeput/recordings/林沐泽/林沐泽_1711788264.mp4"
caption = "JXST1N23_1711781582.mp4"  # 视频标题
thumbnail_file_path = "/home/<USER>/DLR/beforeput/recordings/エムドッグス動物プロダクション/エムドッグス動物プロダクション_1711788221.mp4.jpg"
# 视频参数
duration = 31  # 视频时长(秒)
width = 1920  # 视频宽度
height = 1080  # 视频高度

# Telegram目标配置
target_chat_id = "@juaer"  # 可以是用户ID或频道/群组的username

async def progress_callback(current, total, file_path):
    logger.info(f"Uploaded {current} bytes out of {total} bytes for file: {file_path}")

async def upload_video():
    try:
        # 创建Pyrogram客户端
        async with Client("my_account", api_id=api_id, api_hash=api_hash, bot_token=bot_token) as app:
            logger.info("Connected to Telegram")

            # 检查视频文件是否存在
            if not os.path.exists(video_file_path):
                logger.error(f"Video file does not exist: {video_file_path}")
                return

            logger.info(f"Uploading video: {video_file_path}")

            # 上传视频文件
            sent_message = await app.send_video(
                chat_id=target_chat_id,
                video=video_file_path,
                caption=caption,
                duration=duration,
                width=width,
                height=height,
                thumb=thumbnail_file_path,
                supports_streaming=True,
                progress=progress_callback,
                progress_args=(video_file_path,)
            )

            logger.info(f"Video uploaded successfully. Message ID: {sent_message.message_id}")

    except Exception as e:
        logger.error(f"Error uploading video: {str(e)}")

# 运行上传视频的函数
if __name__ == "__main__":
    import asyncio
    asyncio.run(upload_video())
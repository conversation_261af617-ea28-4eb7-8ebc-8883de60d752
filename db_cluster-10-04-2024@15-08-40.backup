--
-- PostgreSQL database cluster dump
--

SET default_transaction_read_only = off;

SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;

--
-- Roles
--
pg_restore -h aws-0-ap-northeast-1.pooler.supabase.com -p 5432 -U postgres.xbwcdxnyonmfbjkcldji -W jewzut-tobFev-1vunmo -d postgres -Fc /home/<USER>/DLR/beforeput/<EMAIL>
CREATE ROLE anon;
ALTER ROLE anon WITH NOSUPERUSER INHERIT NOCREATEROLE NOCREATEDB NOLOGIN NOREPLICATION NOBYPASSRLS;
CREATE ROLE authenticated;
ALTER ROLE authenticated WITH NOSUPERUSER INHERIT NOCREATEROLE NOCREATEDB NOLOGIN NOREPLICATION NOBYPASSRLS;
CREATE ROLE authenticator;
ALTER ROLE authenticator WITH NOSUPERUSER NOINHERIT NOCREATEROLE NOCREATEDB LOGIN NOREPLICATION NOBYPASSRLS;
CREATE ROLE dashboard_user;
ALTER ROLE dashboard_user WITH NOSUPERUSER INHERIT CREATEROLE CREATEDB NOLOGIN REPLICATION NOBYPASSRLS;
CREATE ROLE pgbouncer;
ALTER ROLE pgbouncer WITH NOSUPERUSER INHERIT NOCREATEROLE NOCREATEDB LOGIN NOREPLICATION NOBYPASSRLS;
CREATE ROLE pgsodium_keyholder;
ALTER ROLE pgsodium_keyholder WITH NOSUPERUSER INHERIT NOCREATEROLE NOCREATEDB NOLOGIN NOREPLICATION NOBYPASSRLS;
CREATE ROLE pgsodium_keyiduser;
ALTER ROLE pgsodium_keyiduser WITH NOSUPERUSER INHERIT NOCREATEROLE NOCREATEDB NOLOGIN NOREPLICATION NOBYPASSRLS;
CREATE ROLE pgsodium_keymaker;
ALTER ROLE pgsodium_keymaker WITH NOSUPERUSER INHERIT NOCREATEROLE NOCREATEDB NOLOGIN NOREPLICATION NOBYPASSRLS;
CREATE ROLE postgres;
ALTER ROLE postgres WITH NOSUPERUSER INHERIT CREATEROLE CREATEDB LOGIN REPLICATION BYPASSRLS;
CREATE ROLE service_role;
ALTER ROLE service_role WITH NOSUPERUSER INHERIT NOCREATEROLE NOCREATEDB NOLOGIN NOREPLICATION BYPASSRLS;
CREATE ROLE supabase_admin;
ALTER ROLE supabase_admin WITH SUPERUSER INHERIT CREATEROLE CREATEDB LOGIN REPLICATION BYPASSRLS;
CREATE ROLE supabase_auth_admin;
ALTER ROLE supabase_auth_admin WITH NOSUPERUSER NOINHERIT CREATEROLE NOCREATEDB LOGIN NOREPLICATION NOBYPASSRLS;
CREATE ROLE supabase_functions_admin;
ALTER ROLE supabase_functions_admin WITH NOSUPERUSER NOINHERIT CREATEROLE NOCREATEDB LOGIN NOREPLICATION NOBYPASSRLS;
CREATE ROLE supabase_read_only_user;
ALTER ROLE supabase_read_only_user WITH NOSUPERUSER INHERIT NOCREATEROLE NOCREATEDB LOGIN NOREPLICATION BYPASSRLS;
CREATE ROLE supabase_realtime_admin;
ALTER ROLE supabase_realtime_admin WITH NOSUPERUSER NOINHERIT NOCREATEROLE NOCREATEDB NOLOGIN NOREPLICATION NOBYPASSRLS;
CREATE ROLE supabase_replication_admin;
ALTER ROLE supabase_replication_admin WITH NOSUPERUSER INHERIT NOCREATEROLE NOCREATEDB LOGIN REPLICATION NOBYPASSRLS;
CREATE ROLE supabase_storage_admin;
ALTER ROLE supabase_storage_admin WITH NOSUPERUSER NOINHERIT CREATEROLE NOCREATEDB LOGIN NOREPLICATION NOBYPASSRLS;

--
-- User Configurations
--

--
-- User Config "anon"
--

ALTER ROLE anon SET statement_timeout TO '3s';

--
-- User Config "authenticated"
--

ALTER ROLE authenticated SET statement_timeout TO '8s';

--
-- User Config "authenticator"
--

ALTER ROLE authenticator SET session_preload_libraries TO 'safeupdate';
ALTER ROLE authenticator SET statement_timeout TO '8s';
ALTER ROLE authenticator SET lock_timeout TO '8s';

--
-- User Config "postgres"
--

ALTER ROLE postgres SET search_path TO E'\\$user', 'public', 'extensions';

--
-- User Config "supabase_admin"
--

ALTER ROLE supabase_admin SET search_path TO '$user', 'public', 'auth', 'extensions';

--
-- User Config "supabase_auth_admin"
--

ALTER ROLE supabase_auth_admin SET search_path TO 'auth';
ALTER ROLE supabase_auth_admin SET idle_in_transaction_session_timeout TO '60000';

--
-- User Config "supabase_functions_admin"
--

ALTER ROLE supabase_functions_admin SET search_path TO 'supabase_functions';

--
-- User Config "supabase_storage_admin"
--

ALTER ROLE supabase_storage_admin SET search_path TO 'storage';


--
-- Role memberships
--

GRANT anon TO authenticator GRANTED BY postgres;
GRANT anon TO postgres GRANTED BY supabase_admin;
GRANT authenticated TO authenticator GRANTED BY postgres;
GRANT authenticated TO postgres GRANTED BY supabase_admin;
GRANT authenticator TO supabase_storage_admin GRANTED BY supabase_admin;
GRANT pg_monitor TO postgres GRANTED BY supabase_admin;
GRANT pg_read_all_data TO supabase_read_only_user GRANTED BY postgres;
GRANT pgsodium_keyholder TO pgsodium_keymaker GRANTED BY supabase_admin;
GRANT pgsodium_keyholder TO postgres WITH ADMIN OPTION GRANTED BY supabase_admin;
GRANT pgsodium_keyholder TO service_role GRANTED BY supabase_admin;
GRANT pgsodium_keyiduser TO pgsodium_keyholder GRANTED BY supabase_admin;
GRANT pgsodium_keyiduser TO pgsodium_keymaker GRANTED BY supabase_admin;
GRANT pgsodium_keyiduser TO postgres WITH ADMIN OPTION GRANTED BY supabase_admin;
GRANT pgsodium_keymaker TO postgres WITH ADMIN OPTION GRANTED BY supabase_admin;
GRANT service_role TO authenticator GRANTED BY postgres;
GRANT service_role TO postgres GRANTED BY supabase_admin;
GRANT supabase_auth_admin TO postgres GRANTED BY supabase_admin;
GRANT supabase_functions_admin TO postgres GRANTED BY supabase_admin;
GRANT supabase_realtime_admin TO postgres GRANTED BY supabase_admin;
GRANT supabase_storage_admin TO postgres GRANTED BY supabase_admin;






--
-- Databases
--

--
-- Database "template1" dump
--

\connect template1

--
-- PostgreSQL database dump
--

-- Dumped from database version 15.1 (Ubuntu 15.1-1.pgdg20.04+1)
-- Dumped by pg_dump version 15.6 (Debian 15.6-1.pgdg110+2)

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

--
-- PostgreSQL database dump complete
--

--
-- Database "postgres" dump
--

\connect postgres

--
-- PostgreSQL database dump
--

-- Dumped from database version 15.1 (Ubuntu 15.1-1.pgdg20.04+1)
-- Dumped by pg_dump version 15.6 (Debian 15.6-1.pgdg110+2)

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

--
-- Name: auth; Type: SCHEMA; Schema: -; Owner: supabase_admin
--

CREATE SCHEMA auth;


ALTER SCHEMA auth OWNER TO supabase_admin;

--
-- Name: extensions; Type: SCHEMA; Schema: -; Owner: postgres
--

CREATE SCHEMA extensions;


ALTER SCHEMA extensions OWNER TO postgres;

--
-- Name: graphql; Type: SCHEMA; Schema: -; Owner: supabase_admin
--

CREATE SCHEMA graphql;


ALTER SCHEMA graphql OWNER TO supabase_admin;

--
-- Name: graphql_public; Type: SCHEMA; Schema: -; Owner: supabase_admin
--

CREATE SCHEMA graphql_public;


ALTER SCHEMA graphql_public OWNER TO supabase_admin;

--
-- Name: pg_net; Type: EXTENSION; Schema: -; Owner: -
--

CREATE EXTENSION IF NOT EXISTS pg_net WITH SCHEMA extensions;


--
-- Name: EXTENSION pg_net; Type: COMMENT; Schema: -; Owner: 
--

COMMENT ON EXTENSION pg_net IS 'Async HTTP';


--
-- Name: pgbouncer; Type: SCHEMA; Schema: -; Owner: pgbouncer
--

CREATE SCHEMA pgbouncer;


ALTER SCHEMA pgbouncer OWNER TO pgbouncer;

--
-- Name: pgsodium; Type: SCHEMA; Schema: -; Owner: supabase_admin
--

CREATE SCHEMA pgsodium;


ALTER SCHEMA pgsodium OWNER TO supabase_admin;

--
-- Name: pgsodium; Type: EXTENSION; Schema: -; Owner: -
--

CREATE EXTENSION IF NOT EXISTS pgsodium WITH SCHEMA pgsodium;


--
-- Name: EXTENSION pgsodium; Type: COMMENT; Schema: -; Owner: 
--

COMMENT ON EXTENSION pgsodium IS 'Pgsodium is a modern cryptography library for Postgres.';


--
-- Name: realtime; Type: SCHEMA; Schema: -; Owner: supabase_admin
--

CREATE SCHEMA realtime;


ALTER SCHEMA realtime OWNER TO supabase_admin;

--
-- Name: storage; Type: SCHEMA; Schema: -; Owner: supabase_admin
--

CREATE SCHEMA storage;


ALTER SCHEMA storage OWNER TO supabase_admin;

--
-- Name: supabase_functions; Type: SCHEMA; Schema: -; Owner: supabase_admin
--

CREATE SCHEMA supabase_functions;


ALTER SCHEMA supabase_functions OWNER TO supabase_admin;

--
-- Name: vault; Type: SCHEMA; Schema: -; Owner: supabase_admin
--

CREATE SCHEMA vault;


ALTER SCHEMA vault OWNER TO supabase_admin;

--
-- Name: pg_graphql; Type: EXTENSION; Schema: -; Owner: -
--

CREATE EXTENSION IF NOT EXISTS pg_graphql WITH SCHEMA graphql;


--
-- Name: EXTENSION pg_graphql; Type: COMMENT; Schema: -; Owner: 
--

COMMENT ON EXTENSION pg_graphql IS 'pg_graphql: GraphQL support';


--
-- Name: pg_stat_statements; Type: EXTENSION; Schema: -; Owner: -
--

CREATE EXTENSION IF NOT EXISTS pg_stat_statements WITH SCHEMA extensions;


--
-- Name: EXTENSION pg_stat_statements; Type: COMMENT; Schema: -; Owner: 
--

COMMENT ON EXTENSION pg_stat_statements IS 'track planning and execution statistics of all SQL statements executed';


--
-- Name: pgcrypto; Type: EXTENSION; Schema: -; Owner: -
--

CREATE EXTENSION IF NOT EXISTS pgcrypto WITH SCHEMA extensions;


--
-- Name: EXTENSION pgcrypto; Type: COMMENT; Schema: -; Owner: 
--

COMMENT ON EXTENSION pgcrypto IS 'cryptographic functions';


--
-- Name: pgjwt; Type: EXTENSION; Schema: -; Owner: -
--

CREATE EXTENSION IF NOT EXISTS pgjwt WITH SCHEMA extensions;


--
-- Name: EXTENSION pgjwt; Type: COMMENT; Schema: -; Owner: 
--

COMMENT ON EXTENSION pgjwt IS 'JSON Web Token API for Postgresql';


--
-- Name: supabase_vault; Type: EXTENSION; Schema: -; Owner: -
--

CREATE EXTENSION IF NOT EXISTS supabase_vault WITH SCHEMA vault;


--
-- Name: EXTENSION supabase_vault; Type: COMMENT; Schema: -; Owner: 
--

COMMENT ON EXTENSION supabase_vault IS 'Supabase Vault Extension';


--
-- Name: uuid-ossp; Type: EXTENSION; Schema: -; Owner: -
--

CREATE EXTENSION IF NOT EXISTS "uuid-ossp" WITH SCHEMA extensions;


--
-- Name: EXTENSION "uuid-ossp"; Type: COMMENT; Schema: -; Owner: 
--

COMMENT ON EXTENSION "uuid-ossp" IS 'generate universally unique identifiers (UUIDs)';


--
-- Name: aal_level; Type: TYPE; Schema: auth; Owner: supabase_auth_admin
--

CREATE TYPE auth.aal_level AS ENUM (
    'aal1',
    'aal2',
    'aal3'
);


ALTER TYPE auth.aal_level OWNER TO supabase_auth_admin;

--
-- Name: code_challenge_method; Type: TYPE; Schema: auth; Owner: supabase_auth_admin
--

CREATE TYPE auth.code_challenge_method AS ENUM (
    's256',
    'plain'
);


ALTER TYPE auth.code_challenge_method OWNER TO supabase_auth_admin;

--
-- Name: factor_status; Type: TYPE; Schema: auth; Owner: supabase_auth_admin
--

CREATE TYPE auth.factor_status AS ENUM (
    'unverified',
    'verified'
);


ALTER TYPE auth.factor_status OWNER TO supabase_auth_admin;

--
-- Name: factor_type; Type: TYPE; Schema: auth; Owner: supabase_auth_admin
--

CREATE TYPE auth.factor_type AS ENUM (
    'totp',
    'webauthn'
);


ALTER TYPE auth.factor_type OWNER TO supabase_auth_admin;

--
-- Name: action; Type: TYPE; Schema: realtime; Owner: supabase_admin
--

CREATE TYPE realtime.action AS ENUM (
    'INSERT',
    'UPDATE',
    'DELETE',
    'TRUNCATE',
    'ERROR'
);


ALTER TYPE realtime.action OWNER TO supabase_admin;

--
-- Name: equality_op; Type: TYPE; Schema: realtime; Owner: supabase_admin
--

CREATE TYPE realtime.equality_op AS ENUM (
    'eq',
    'neq',
    'lt',
    'lte',
    'gt',
    'gte',
    'in'
);


ALTER TYPE realtime.equality_op OWNER TO supabase_admin;

--
-- Name: user_defined_filter; Type: TYPE; Schema: realtime; Owner: supabase_admin
--

CREATE TYPE realtime.user_defined_filter AS (
	column_name text,
	op realtime.equality_op,
	value text
);


ALTER TYPE realtime.user_defined_filter OWNER TO supabase_admin;

--
-- Name: wal_column; Type: TYPE; Schema: realtime; Owner: supabase_admin
--

CREATE TYPE realtime.wal_column AS (
	name text,
	type_name text,
	type_oid oid,
	value jsonb,
	is_pkey boolean,
	is_selectable boolean
);


ALTER TYPE realtime.wal_column OWNER TO supabase_admin;

--
-- Name: wal_rls; Type: TYPE; Schema: realtime; Owner: supabase_admin
--

CREATE TYPE realtime.wal_rls AS (
	wal jsonb,
	is_rls_enabled boolean,
	subscription_ids uuid[],
	errors text[]
);


ALTER TYPE realtime.wal_rls OWNER TO supabase_admin;

--
-- Name: email(); Type: FUNCTION; Schema: auth; Owner: supabase_auth_admin
--

CREATE FUNCTION auth.email() RETURNS text
    LANGUAGE sql STABLE
    AS $$
  select 
  coalesce(
    nullif(current_setting('request.jwt.claim.email', true), ''),
    (nullif(current_setting('request.jwt.claims', true), '')::jsonb ->> 'email')
  )::text
$$;


ALTER FUNCTION auth.email() OWNER TO supabase_auth_admin;

--
-- Name: FUNCTION email(); Type: COMMENT; Schema: auth; Owner: supabase_auth_admin
--

COMMENT ON FUNCTION auth.email() IS 'Deprecated. Use auth.jwt() -> ''email'' instead.';


--
-- Name: jwt(); Type: FUNCTION; Schema: auth; Owner: supabase_auth_admin
--

CREATE FUNCTION auth.jwt() RETURNS jsonb
    LANGUAGE sql STABLE
    AS $$
  select 
    coalesce(
        nullif(current_setting('request.jwt.claim', true), ''),
        nullif(current_setting('request.jwt.claims', true), '')
    )::jsonb
$$;


ALTER FUNCTION auth.jwt() OWNER TO supabase_auth_admin;

--
-- Name: role(); Type: FUNCTION; Schema: auth; Owner: supabase_auth_admin
--

CREATE FUNCTION auth.role() RETURNS text
    LANGUAGE sql STABLE
    AS $$
  select 
  coalesce(
    nullif(current_setting('request.jwt.claim.role', true), ''),
    (nullif(current_setting('request.jwt.claims', true), '')::jsonb ->> 'role')
  )::text
$$;


ALTER FUNCTION auth.role() OWNER TO supabase_auth_admin;

--
-- Name: FUNCTION role(); Type: COMMENT; Schema: auth; Owner: supabase_auth_admin
--

COMMENT ON FUNCTION auth.role() IS 'Deprecated. Use auth.jwt() -> ''role'' instead.';


--
-- Name: uid(); Type: FUNCTION; Schema: auth; Owner: supabase_auth_admin
--

CREATE FUNCTION auth.uid() RETURNS uuid
    LANGUAGE sql STABLE
    AS $$
  select 
  coalesce(
    nullif(current_setting('request.jwt.claim.sub', true), ''),
    (nullif(current_setting('request.jwt.claims', true), '')::jsonb ->> 'sub')
  )::uuid
$$;


ALTER FUNCTION auth.uid() OWNER TO supabase_auth_admin;

--
-- Name: FUNCTION uid(); Type: COMMENT; Schema: auth; Owner: supabase_auth_admin
--

COMMENT ON FUNCTION auth.uid() IS 'Deprecated. Use auth.jwt() -> ''sub'' instead.';


--
-- Name: grant_pg_cron_access(); Type: FUNCTION; Schema: extensions; Owner: postgres
--

CREATE FUNCTION extensions.grant_pg_cron_access() RETURNS event_trigger
    LANGUAGE plpgsql
    AS $$
BEGIN
  IF EXISTS (
    SELECT
    FROM pg_event_trigger_ddl_commands() AS ev
    JOIN pg_extension AS ext
    ON ev.objid = ext.oid
    WHERE ext.extname = 'pg_cron'
  )
  THEN
    grant usage on schema cron to postgres with grant option;

    alter default privileges in schema cron grant all on tables to postgres with grant option;
    alter default privileges in schema cron grant all on functions to postgres with grant option;
    alter default privileges in schema cron grant all on sequences to postgres with grant option;

    alter default privileges for user supabase_admin in schema cron grant all
        on sequences to postgres with grant option;
    alter default privileges for user supabase_admin in schema cron grant all
        on tables to postgres with grant option;
    alter default privileges for user supabase_admin in schema cron grant all
        on functions to postgres with grant option;

    grant all privileges on all tables in schema cron to postgres with grant option;
    revoke all on table cron.job from postgres;
    grant select on table cron.job to postgres with grant option;
  END IF;
END;
$$;


ALTER FUNCTION extensions.grant_pg_cron_access() OWNER TO postgres;

--
-- Name: FUNCTION grant_pg_cron_access(); Type: COMMENT; Schema: extensions; Owner: postgres
--

COMMENT ON FUNCTION extensions.grant_pg_cron_access() IS 'Grants access to pg_cron';


--
-- Name: grant_pg_graphql_access(); Type: FUNCTION; Schema: extensions; Owner: supabase_admin
--

CREATE FUNCTION extensions.grant_pg_graphql_access() RETURNS event_trigger
    LANGUAGE plpgsql
    AS $_$
DECLARE
    func_is_graphql_resolve bool;
BEGIN
    func_is_graphql_resolve = (
        SELECT n.proname = 'resolve'
        FROM pg_event_trigger_ddl_commands() AS ev
        LEFT JOIN pg_catalog.pg_proc AS n
        ON ev.objid = n.oid
    );

    IF func_is_graphql_resolve
    THEN
        -- Update public wrapper to pass all arguments through to the pg_graphql resolve func
        DROP FUNCTION IF EXISTS graphql_public.graphql;
        create or replace function graphql_public.graphql(
            "operationName" text default null,
            query text default null,
            variables jsonb default null,
            extensions jsonb default null
        )
            returns jsonb
            language sql
        as $$
            select graphql.resolve(
                query := query,
                variables := coalesce(variables, '{}'),
                "operationName" := "operationName",
                extensions := extensions
            );
        $$;

        -- This hook executes when `graphql.resolve` is created. That is not necessarily the last
        -- function in the extension so we need to grant permissions on existing entities AND
        -- update default permissions to any others that are created after `graphql.resolve`
        grant usage on schema graphql to postgres, anon, authenticated, service_role;
        grant select on all tables in schema graphql to postgres, anon, authenticated, service_role;
        grant execute on all functions in schema graphql to postgres, anon, authenticated, service_role;
        grant all on all sequences in schema graphql to postgres, anon, authenticated, service_role;
        alter default privileges in schema graphql grant all on tables to postgres, anon, authenticated, service_role;
        alter default privileges in schema graphql grant all on functions to postgres, anon, authenticated, service_role;
        alter default privileges in schema graphql grant all on sequences to postgres, anon, authenticated, service_role;

        -- Allow postgres role to allow granting usage on graphql and graphql_public schemas to custom roles
        grant usage on schema graphql_public to postgres with grant option;
        grant usage on schema graphql to postgres with grant option;
    END IF;

END;
$_$;


ALTER FUNCTION extensions.grant_pg_graphql_access() OWNER TO supabase_admin;

--
-- Name: FUNCTION grant_pg_graphql_access(); Type: COMMENT; Schema: extensions; Owner: supabase_admin
--

COMMENT ON FUNCTION extensions.grant_pg_graphql_access() IS 'Grants access to pg_graphql';


--
-- Name: grant_pg_net_access(); Type: FUNCTION; Schema: extensions; Owner: postgres
--

CREATE FUNCTION extensions.grant_pg_net_access() RETURNS event_trigger
    LANGUAGE plpgsql
    AS $$
  BEGIN
    IF EXISTS (
      SELECT 1
      FROM pg_event_trigger_ddl_commands() AS ev
      JOIN pg_extension AS ext
      ON ev.objid = ext.oid
      WHERE ext.extname = 'pg_net'
    )
    THEN
      GRANT USAGE ON SCHEMA net TO supabase_functions_admin, postgres, anon, authenticated, service_role;

      ALTER function net.http_get(url text, params jsonb, headers jsonb, timeout_milliseconds integer) SECURITY DEFINER;
      ALTER function net.http_post(url text, body jsonb, params jsonb, headers jsonb, timeout_milliseconds integer) SECURITY DEFINER;

      ALTER function net.http_get(url text, params jsonb, headers jsonb, timeout_milliseconds integer) SET search_path = net;
      ALTER function net.http_post(url text, body jsonb, params jsonb, headers jsonb, timeout_milliseconds integer) SET search_path = net;

      REVOKE ALL ON FUNCTION net.http_get(url text, params jsonb, headers jsonb, timeout_milliseconds integer) FROM PUBLIC;
      REVOKE ALL ON FUNCTION net.http_post(url text, body jsonb, params jsonb, headers jsonb, timeout_milliseconds integer) FROM PUBLIC;

      GRANT EXECUTE ON FUNCTION net.http_get(url text, params jsonb, headers jsonb, timeout_milliseconds integer) TO supabase_functions_admin, postgres, anon, authenticated, service_role;
      GRANT EXECUTE ON FUNCTION net.http_post(url text, body jsonb, params jsonb, headers jsonb, timeout_milliseconds integer) TO supabase_functions_admin, postgres, anon, authenticated, service_role;
    END IF;
  END;
  $$;


ALTER FUNCTION extensions.grant_pg_net_access() OWNER TO postgres;

--
-- Name: FUNCTION grant_pg_net_access(); Type: COMMENT; Schema: extensions; Owner: postgres
--

COMMENT ON FUNCTION extensions.grant_pg_net_access() IS 'Grants access to pg_net';


--
-- Name: pgrst_ddl_watch(); Type: FUNCTION; Schema: extensions; Owner: supabase_admin
--

CREATE FUNCTION extensions.pgrst_ddl_watch() RETURNS event_trigger
    LANGUAGE plpgsql
    AS $$
DECLARE
  cmd record;
BEGIN
  FOR cmd IN SELECT * FROM pg_event_trigger_ddl_commands()
  LOOP
    IF cmd.command_tag IN (
      'CREATE SCHEMA', 'ALTER SCHEMA'
    , 'CREATE TABLE', 'CREATE TABLE AS', 'SELECT INTO', 'ALTER TABLE'
    , 'CREATE FOREIGN TABLE', 'ALTER FOREIGN TABLE'
    , 'CREATE VIEW', 'ALTER VIEW'
    , 'CREATE MATERIALIZED VIEW', 'ALTER MATERIALIZED VIEW'
    , 'CREATE FUNCTION', 'ALTER FUNCTION'
    , 'CREATE TRIGGER'
    , 'CREATE TYPE', 'ALTER TYPE'
    , 'CREATE RULE'
    , 'COMMENT'
    )
    -- don't notify in case of CREATE TEMP table or other objects created on pg_temp
    AND cmd.schema_name is distinct from 'pg_temp'
    THEN
      NOTIFY pgrst, 'reload schema';
    END IF;
  END LOOP;
END; $$;


ALTER FUNCTION extensions.pgrst_ddl_watch() OWNER TO supabase_admin;

--
-- Name: pgrst_drop_watch(); Type: FUNCTION; Schema: extensions; Owner: supabase_admin
--

CREATE FUNCTION extensions.pgrst_drop_watch() RETURNS event_trigger
    LANGUAGE plpgsql
    AS $$
DECLARE
  obj record;
BEGIN
  FOR obj IN SELECT * FROM pg_event_trigger_dropped_objects()
  LOOP
    IF obj.object_type IN (
      'schema'
    , 'table'
    , 'foreign table'
    , 'view'
    , 'materialized view'
    , 'function'
    , 'trigger'
    , 'type'
    , 'rule'
    )
    AND obj.is_temporary IS false -- no pg_temp objects
    THEN
      NOTIFY pgrst, 'reload schema';
    END IF;
  END LOOP;
END; $$;


ALTER FUNCTION extensions.pgrst_drop_watch() OWNER TO supabase_admin;

--
-- Name: set_graphql_placeholder(); Type: FUNCTION; Schema: extensions; Owner: supabase_admin
--

CREATE FUNCTION extensions.set_graphql_placeholder() RETURNS event_trigger
    LANGUAGE plpgsql
    AS $_$
    DECLARE
    graphql_is_dropped bool;
    BEGIN
    graphql_is_dropped = (
        SELECT ev.schema_name = 'graphql_public'
        FROM pg_event_trigger_dropped_objects() AS ev
        WHERE ev.schema_name = 'graphql_public'
    );

    IF graphql_is_dropped
    THEN
        create or replace function graphql_public.graphql(
            "operationName" text default null,
            query text default null,
            variables jsonb default null,
            extensions jsonb default null
        )
            returns jsonb
            language plpgsql
        as $$
            DECLARE
                server_version float;
            BEGIN
                server_version = (SELECT (SPLIT_PART((select version()), ' ', 2))::float);

                IF server_version >= 14 THEN
                    RETURN jsonb_build_object(
                        'errors', jsonb_build_array(
                            jsonb_build_object(
                                'message', 'pg_graphql extension is not enabled.'
                            )
                        )
                    );
                ELSE
                    RETURN jsonb_build_object(
                        'errors', jsonb_build_array(
                            jsonb_build_object(
                                'message', 'pg_graphql is only available on projects running Postgres 14 onwards.'
                            )
                        )
                    );
                END IF;
            END;
        $$;
    END IF;

    END;
$_$;


ALTER FUNCTION extensions.set_graphql_placeholder() OWNER TO supabase_admin;

--
-- Name: FUNCTION set_graphql_placeholder(); Type: COMMENT; Schema: extensions; Owner: supabase_admin
--

COMMENT ON FUNCTION extensions.set_graphql_placeholder() IS 'Reintroduces placeholder function for graphql_public.graphql';


--
-- Name: get_auth(text); Type: FUNCTION; Schema: pgbouncer; Owner: postgres
--

CREATE FUNCTION pgbouncer.get_auth(p_usename text) RETURNS TABLE(username text, password text)
    LANGUAGE plpgsql SECURITY DEFINER
    AS $$
BEGIN
    RAISE WARNING 'PgBouncer auth request: %', p_usename;

    RETURN QUERY
    SELECT usename::TEXT, passwd::TEXT FROM pg_catalog.pg_shadow
    WHERE usename = p_usename;
END;
$$;


ALTER FUNCTION pgbouncer.get_auth(p_usename text) OWNER TO postgres;

--
-- Name: get_platform_from_url(text); Type: FUNCTION; Schema: public; Owner: postgres
--

CREATE FUNCTION public.get_platform_from_url(url text) RETURNS text
    LANGUAGE plpgsql
    AS $$
BEGIN
    IF url ILIKE '%tiktok%' THEN
        RETURN 'TikTok';
    ELSIF url ILIKE '%douyin%' THEN
        RETURN 'Douyin';
    ELSIF url ILIKE '%twitch%' THEN
        RETURN 'Twitch';
    ELSIF url ILIKE '%bilibili%' THEN
        RETURN 'Bilibili';
    ELSIF url ILIKE '%youtube%' OR url ILIKE '%youtu.be%' THEN
        RETURN 'YouTube';
    ELSE
        RETURN 'Unknown';
    END IF;
END;
$$;


ALTER FUNCTION public.get_platform_from_url(url text) OWNER TO postgres;

--
-- Name: update_platform_on_insert(); Type: FUNCTION; Schema: public; Owner: postgres
--

CREATE FUNCTION public.update_platform_on_insert() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
BEGIN
    -- 使用之前创建的函数来获取live_url字段的平台
    NEW.platform := get_platform_from_url(NEW.live_url);
    RETURN NEW;
END;
$$;


ALTER FUNCTION public.update_platform_on_insert() OWNER TO postgres;

--
-- Name: apply_rls(jsonb, integer); Type: FUNCTION; Schema: realtime; Owner: supabase_admin
--

CREATE FUNCTION realtime.apply_rls(wal jsonb, max_record_bytes integer DEFAULT (1024 * 1024)) RETURNS SETOF realtime.wal_rls
    LANGUAGE plpgsql
    AS $$
    declare
        -- Regclass of the table e.g. public.notes
        entity_ regclass = (quote_ident(wal ->> 'schema') || '.' || quote_ident(wal ->> 'table'))::regclass;

        -- I, U, D, T: insert, update ...
        action realtime.action = (
            case wal ->> 'action'
                when 'I' then 'INSERT'
                when 'U' then 'UPDATE'
                when 'D' then 'DELETE'
                else 'ERROR'
            end
        );

        -- Is row level security enabled for the table
        is_rls_enabled bool = relrowsecurity from pg_class where oid = entity_;

        subscriptions realtime.subscription[] = array_agg(subs)
            from
                realtime.subscription subs
            where
                subs.entity = entity_;

        -- Subscription vars
        roles regrole[] = array_agg(distinct us.claims_role)
            from
                unnest(subscriptions) us;

        working_role regrole;
        claimed_role regrole;
        claims jsonb;

        subscription_id uuid;
        subscription_has_access bool;
        visible_to_subscription_ids uuid[] = '{}';

        -- structured info for wal's columns
        columns realtime.wal_column[];
        -- previous identity values for update/delete
        old_columns realtime.wal_column[];

        error_record_exceeds_max_size boolean = octet_length(wal::text) > max_record_bytes;

        -- Primary jsonb output for record
        output jsonb;

    begin
        perform set_config('role', null, true);

        columns =
            array_agg(
                (
                    x->>'name',
                    x->>'type',
                    x->>'typeoid',
                    realtime.cast(
                        (x->'value') #>> '{}',
                        coalesce(
                            (x->>'typeoid')::regtype, -- null when wal2json version <= 2.4
                            (x->>'type')::regtype
                        )
                    ),
                    (pks ->> 'name') is not null,
                    true
                )::realtime.wal_column
            )
            from
                jsonb_array_elements(wal -> 'columns') x
                left join jsonb_array_elements(wal -> 'pk') pks
                    on (x ->> 'name') = (pks ->> 'name');

        old_columns =
            array_agg(
                (
                    x->>'name',
                    x->>'type',
                    x->>'typeoid',
                    realtime.cast(
                        (x->'value') #>> '{}',
                        coalesce(
                            (x->>'typeoid')::regtype, -- null when wal2json version <= 2.4
                            (x->>'type')::regtype
                        )
                    ),
                    (pks ->> 'name') is not null,
                    true
                )::realtime.wal_column
            )
            from
                jsonb_array_elements(wal -> 'identity') x
                left join jsonb_array_elements(wal -> 'pk') pks
                    on (x ->> 'name') = (pks ->> 'name');

        for working_role in select * from unnest(roles) loop

            -- Update `is_selectable` for columns and old_columns
            columns =
                array_agg(
                    (
                        c.name,
                        c.type_name,
                        c.type_oid,
                        c.value,
                        c.is_pkey,
                        pg_catalog.has_column_privilege(working_role, entity_, c.name, 'SELECT')
                    )::realtime.wal_column
                )
                from
                    unnest(columns) c;

            old_columns =
                    array_agg(
                        (
                            c.name,
                            c.type_name,
                            c.type_oid,
                            c.value,
                            c.is_pkey,
                            pg_catalog.has_column_privilege(working_role, entity_, c.name, 'SELECT')
                        )::realtime.wal_column
                    )
                    from
                        unnest(old_columns) c;

            if action <> 'DELETE' and count(1) = 0 from unnest(columns) c where c.is_pkey then
                return next (
                    jsonb_build_object(
                        'schema', wal ->> 'schema',
                        'table', wal ->> 'table',
                        'type', action
                    ),
                    is_rls_enabled,
                    -- subscriptions is already filtered by entity
                    (select array_agg(s.subscription_id) from unnest(subscriptions) as s where claims_role = working_role),
                    array['Error 400: Bad Request, no primary key']
                )::realtime.wal_rls;

            -- The claims role does not have SELECT permission to the primary key of entity
            elsif action <> 'DELETE' and sum(c.is_selectable::int) <> count(1) from unnest(columns) c where c.is_pkey then
                return next (
                    jsonb_build_object(
                        'schema', wal ->> 'schema',
                        'table', wal ->> 'table',
                        'type', action
                    ),
                    is_rls_enabled,
                    (select array_agg(s.subscription_id) from unnest(subscriptions) as s where claims_role = working_role),
                    array['Error 401: Unauthorized']
                )::realtime.wal_rls;

            else
                output = jsonb_build_object(
                    'schema', wal ->> 'schema',
                    'table', wal ->> 'table',
                    'type', action,
                    'commit_timestamp', to_char(
                        ((wal ->> 'timestamp')::timestamptz at time zone 'utc'),
                        'YYYY-MM-DD"T"HH24:MI:SS.MS"Z"'
                    ),
                    'columns', (
                        select
                            jsonb_agg(
                                jsonb_build_object(
                                    'name', pa.attname,
                                    'type', pt.typname
                                )
                                order by pa.attnum asc
                            )
                        from
                            pg_attribute pa
                            join pg_type pt
                                on pa.atttypid = pt.oid
                        where
                            attrelid = entity_
                            and attnum > 0
                            and pg_catalog.has_column_privilege(working_role, entity_, pa.attname, 'SELECT')
                    )
                )
                -- Add "record" key for insert and update
                || case
                    when action in ('INSERT', 'UPDATE') then
                        jsonb_build_object(
                            'record',
                            (
                                select
                                    jsonb_object_agg(
                                        -- if unchanged toast, get column name and value from old record
                                        coalesce((c).name, (oc).name),
                                        case
                                            when (c).name is null then (oc).value
                                            else (c).value
                                        end
                                    )
                                from
                                    unnest(columns) c
                                    full outer join unnest(old_columns) oc
                                        on (c).name = (oc).name
                                where
                                    coalesce((c).is_selectable, (oc).is_selectable)
                                    and ( not error_record_exceeds_max_size or (octet_length((c).value::text) <= 64))
                            )
                        )
                    else '{}'::jsonb
                end
                -- Add "old_record" key for update and delete
                || case
                    when action = 'UPDATE' then
                        jsonb_build_object(
                                'old_record',
                                (
                                    select jsonb_object_agg((c).name, (c).value)
                                    from unnest(old_columns) c
                                    where
                                        (c).is_selectable
                                        and ( not error_record_exceeds_max_size or (octet_length((c).value::text) <= 64))
                                )
                            )
                    when action = 'DELETE' then
                        jsonb_build_object(
                            'old_record',
                            (
                                select jsonb_object_agg((c).name, (c).value)
                                from unnest(old_columns) c
                                where
                                    (c).is_selectable
                                    and ( not error_record_exceeds_max_size or (octet_length((c).value::text) <= 64))
                                    and ( not is_rls_enabled or (c).is_pkey ) -- if RLS enabled, we can't secure deletes so filter to pkey
                            )
                        )
                    else '{}'::jsonb
                end;

                -- Create the prepared statement
                if is_rls_enabled and action <> 'DELETE' then
                    if (select 1 from pg_prepared_statements where name = 'walrus_rls_stmt' limit 1) > 0 then
                        deallocate walrus_rls_stmt;
                    end if;
                    execute realtime.build_prepared_statement_sql('walrus_rls_stmt', entity_, columns);
                end if;

                visible_to_subscription_ids = '{}';

                for subscription_id, claims in (
                        select
                            subs.subscription_id,
                            subs.claims
                        from
                            unnest(subscriptions) subs
                        where
                            subs.entity = entity_
                            and subs.claims_role = working_role
                            and (
                                realtime.is_visible_through_filters(columns, subs.filters)
                                or action = 'DELETE'
                            )
                ) loop

                    if not is_rls_enabled or action = 'DELETE' then
                        visible_to_subscription_ids = visible_to_subscription_ids || subscription_id;
                    else
                        -- Check if RLS allows the role to see the record
                        perform
                            set_config('role', working_role::text, true),
                            set_config('request.jwt.claims', claims::text, true);

                        execute 'execute walrus_rls_stmt' into subscription_has_access;

                        if subscription_has_access then
                            visible_to_subscription_ids = visible_to_subscription_ids || subscription_id;
                        end if;
                    end if;
                end loop;

                perform set_config('role', null, true);

                return next (
                    output,
                    is_rls_enabled,
                    visible_to_subscription_ids,
                    case
                        when error_record_exceeds_max_size then array['Error 413: Payload Too Large']
                        else '{}'
                    end
                )::realtime.wal_rls;

            end if;
        end loop;

        perform set_config('role', null, true);
    end;
    $$;


ALTER FUNCTION realtime.apply_rls(wal jsonb, max_record_bytes integer) OWNER TO supabase_admin;

--
-- Name: build_prepared_statement_sql(text, regclass, realtime.wal_column[]); Type: FUNCTION; Schema: realtime; Owner: supabase_admin
--

CREATE FUNCTION realtime.build_prepared_statement_sql(prepared_statement_name text, entity regclass, columns realtime.wal_column[]) RETURNS text
    LANGUAGE sql
    AS $$
      /*
      Builds a sql string that, if executed, creates a prepared statement to
      tests retrive a row from *entity* by its primary key columns.
      Example
          select realtime.build_prepared_statement_sql('public.notes', '{"id"}'::text[], '{"bigint"}'::text[])
      */
          select
      'prepare ' || prepared_statement_name || ' as
          select
              exists(
                  select
                      1
                  from
                      ' || entity || '
                  where
                      ' || string_agg(quote_ident(pkc.name) || '=' || quote_nullable(pkc.value #>> '{}') , ' and ') || '
              )'
          from
              unnest(columns) pkc
          where
              pkc.is_pkey
          group by
              entity
      $$;


ALTER FUNCTION realtime.build_prepared_statement_sql(prepared_statement_name text, entity regclass, columns realtime.wal_column[]) OWNER TO supabase_admin;

--
-- Name: cast(text, regtype); Type: FUNCTION; Schema: realtime; Owner: supabase_admin
--

CREATE FUNCTION realtime."cast"(val text, type_ regtype) RETURNS jsonb
    LANGUAGE plpgsql IMMUTABLE
    AS $$
    declare
      res jsonb;
    begin
      execute format('select to_jsonb(%L::'|| type_::text || ')', val)  into res;
      return res;
    end
    $$;


ALTER FUNCTION realtime."cast"(val text, type_ regtype) OWNER TO supabase_admin;

--
-- Name: channel_name(); Type: FUNCTION; Schema: realtime; Owner: supabase_realtime_admin
--

CREATE FUNCTION realtime.channel_name() RETURNS text
    LANGUAGE sql STABLE
    AS $$
select nullif(current_setting('realtime.channel_name', true), '')::text;
$$;


ALTER FUNCTION realtime.channel_name() OWNER TO supabase_realtime_admin;

--
-- Name: check_equality_op(realtime.equality_op, regtype, text, text); Type: FUNCTION; Schema: realtime; Owner: supabase_admin
--

CREATE FUNCTION realtime.check_equality_op(op realtime.equality_op, type_ regtype, val_1 text, val_2 text) RETURNS boolean
    LANGUAGE plpgsql IMMUTABLE
    AS $$
      /*
      Casts *val_1* and *val_2* as type *type_* and check the *op* condition for truthiness
      */
      declare
          op_symbol text = (
              case
                  when op = 'eq' then '='
                  when op = 'neq' then '!='
                  when op = 'lt' then '<'
                  when op = 'lte' then '<='
                  when op = 'gt' then '>'
                  when op = 'gte' then '>='
                  when op = 'in' then '= any'
                  else 'UNKNOWN OP'
              end
          );
          res boolean;
      begin
          execute format(
              'select %L::'|| type_::text || ' ' || op_symbol
              || ' ( %L::'
              || (
                  case
                      when op = 'in' then type_::text || '[]'
                      else type_::text end
              )
              || ')', val_1, val_2) into res;
          return res;
      end;
      $$;


ALTER FUNCTION realtime.check_equality_op(op realtime.equality_op, type_ regtype, val_1 text, val_2 text) OWNER TO supabase_admin;

--
-- Name: is_visible_through_filters(realtime.wal_column[], realtime.user_defined_filter[]); Type: FUNCTION; Schema: realtime; Owner: supabase_admin
--

CREATE FUNCTION realtime.is_visible_through_filters(columns realtime.wal_column[], filters realtime.user_defined_filter[]) RETURNS boolean
    LANGUAGE sql IMMUTABLE
    AS $_$
    /*
    Should the record be visible (true) or filtered out (false) after *filters* are applied
    */
        select
            -- Default to allowed when no filters present
            $2 is null -- no filters. this should not happen because subscriptions has a default
            or array_length($2, 1) is null -- array length of an empty array is null
            or bool_and(
                coalesce(
                    realtime.check_equality_op(
                        op:=f.op,
                        type_:=coalesce(
                            col.type_oid::regtype, -- null when wal2json version <= 2.4
                            col.type_name::regtype
                        ),
                        -- cast jsonb to text
                        val_1:=col.value #>> '{}',
                        val_2:=f.value
                    ),
                    false -- if null, filter does not match
                )
            )
        from
            unnest(filters) f
            join unnest(columns) col
                on f.column_name = col.name;
    $_$;


ALTER FUNCTION realtime.is_visible_through_filters(columns realtime.wal_column[], filters realtime.user_defined_filter[]) OWNER TO supabase_admin;

--
-- Name: list_changes(name, name, integer, integer); Type: FUNCTION; Schema: realtime; Owner: supabase_admin
--

CREATE FUNCTION realtime.list_changes(publication name, slot_name name, max_changes integer, max_record_bytes integer) RETURNS SETOF realtime.wal_rls
    LANGUAGE sql
    SET log_min_messages TO 'fatal'
    AS $$
      with pub as (
        select
          concat_ws(
            ',',
            case when bool_or(pubinsert) then 'insert' else null end,
            case when bool_or(pubupdate) then 'update' else null end,
            case when bool_or(pubdelete) then 'delete' else null end
          ) as w2j_actions,
          coalesce(
            string_agg(
              realtime.quote_wal2json(format('%I.%I', schemaname, tablename)::regclass),
              ','
            ) filter (where ppt.tablename is not null and ppt.tablename not like '% %'),
            ''
          ) w2j_add_tables
        from
          pg_publication pp
          left join pg_publication_tables ppt
            on pp.pubname = ppt.pubname
        where
          pp.pubname = publication
        group by
          pp.pubname
        limit 1
      ),
      w2j as (
        select
          x.*, pub.w2j_add_tables
        from
          pub,
          pg_logical_slot_get_changes(
            slot_name, null, max_changes,
            'include-pk', 'true',
            'include-transaction', 'false',
            'include-timestamp', 'true',
            'include-type-oids', 'true',
            'format-version', '2',
            'actions', pub.w2j_actions,
            'add-tables', pub.w2j_add_tables
          ) x
      )
      select
        xyz.wal,
        xyz.is_rls_enabled,
        xyz.subscription_ids,
        xyz.errors
      from
        w2j,
        realtime.apply_rls(
          wal := w2j.data::jsonb,
          max_record_bytes := max_record_bytes
        ) xyz(wal, is_rls_enabled, subscription_ids, errors)
      where
        w2j.w2j_add_tables <> ''
        and xyz.subscription_ids[1] is not null
    $$;


ALTER FUNCTION realtime.list_changes(publication name, slot_name name, max_changes integer, max_record_bytes integer) OWNER TO supabase_admin;

--
-- Name: quote_wal2json(regclass); Type: FUNCTION; Schema: realtime; Owner: supabase_admin
--

CREATE FUNCTION realtime.quote_wal2json(entity regclass) RETURNS text
    LANGUAGE sql IMMUTABLE STRICT
    AS $$
      select
        (
          select string_agg('' || ch,'')
          from unnest(string_to_array(nsp.nspname::text, null)) with ordinality x(ch, idx)
          where
            not (x.idx = 1 and x.ch = '"')
            and not (
              x.idx = array_length(string_to_array(nsp.nspname::text, null), 1)
              and x.ch = '"'
            )
        )
        || '.'
        || (
          select string_agg('' || ch,'')
          from unnest(string_to_array(pc.relname::text, null)) with ordinality x(ch, idx)
          where
            not (x.idx = 1 and x.ch = '"')
            and not (
              x.idx = array_length(string_to_array(nsp.nspname::text, null), 1)
              and x.ch = '"'
            )
          )
      from
        pg_class pc
        join pg_namespace nsp
          on pc.relnamespace = nsp.oid
      where
        pc.oid = entity
    $$;


ALTER FUNCTION realtime.quote_wal2json(entity regclass) OWNER TO supabase_admin;

--
-- Name: subscription_check_filters(); Type: FUNCTION; Schema: realtime; Owner: supabase_admin
--

CREATE FUNCTION realtime.subscription_check_filters() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
    /*
    Validates that the user defined filters for a subscription:
    - refer to valid columns that the claimed role may access
    - values are coercable to the correct column type
    */
    declare
        col_names text[] = coalesce(
                array_agg(c.column_name order by c.ordinal_position),
                '{}'::text[]
            )
            from
                information_schema.columns c
            where
                format('%I.%I', c.table_schema, c.table_name)::regclass = new.entity
                and pg_catalog.has_column_privilege(
                    (new.claims ->> 'role'),
                    format('%I.%I', c.table_schema, c.table_name)::regclass,
                    c.column_name,
                    'SELECT'
                );
        filter realtime.user_defined_filter;
        col_type regtype;

        in_val jsonb;
    begin
        for filter in select * from unnest(new.filters) loop
            -- Filtered column is valid
            if not filter.column_name = any(col_names) then
                raise exception 'invalid column for filter %', filter.column_name;
            end if;

            -- Type is sanitized and safe for string interpolation
            col_type = (
                select atttypid::regtype
                from pg_catalog.pg_attribute
                where attrelid = new.entity
                      and attname = filter.column_name
            );
            if col_type is null then
                raise exception 'failed to lookup type for column %', filter.column_name;
            end if;

            -- Set maximum number of entries for in filter
            if filter.op = 'in'::realtime.equality_op then
                in_val = realtime.cast(filter.value, (col_type::text || '[]')::regtype);
                if coalesce(jsonb_array_length(in_val), 0) > 100 then
                    raise exception 'too many values for `in` filter. Maximum 100';
                end if;
            else
                -- raises an exception if value is not coercable to type
                perform realtime.cast(filter.value, col_type);
            end if;

        end loop;

        -- Apply consistent order to filters so the unique constraint on
        -- (subscription_id, entity, filters) can't be tricked by a different filter order
        new.filters = coalesce(
            array_agg(f order by f.column_name, f.op, f.value),
            '{}'
        ) from unnest(new.filters) f;

        return new;
    end;
    $$;


ALTER FUNCTION realtime.subscription_check_filters() OWNER TO supabase_admin;

--
-- Name: to_regrole(text); Type: FUNCTION; Schema: realtime; Owner: supabase_admin
--

CREATE FUNCTION realtime.to_regrole(role_name text) RETURNS regrole
    LANGUAGE sql IMMUTABLE
    AS $$ select role_name::regrole $$;


ALTER FUNCTION realtime.to_regrole(role_name text) OWNER TO supabase_admin;

--
-- Name: can_insert_object(text, text, uuid, jsonb); Type: FUNCTION; Schema: storage; Owner: supabase_storage_admin
--

CREATE FUNCTION storage.can_insert_object(bucketid text, name text, owner uuid, metadata jsonb) RETURNS void
    LANGUAGE plpgsql
    AS $$
BEGIN
  INSERT INTO "storage"."objects" ("bucket_id", "name", "owner", "metadata") VALUES (bucketid, name, owner, metadata);
  -- hack to rollback the successful insert
  RAISE sqlstate 'PT200' using
  message = 'ROLLBACK',
  detail = 'rollback successful insert';
END
$$;


ALTER FUNCTION storage.can_insert_object(bucketid text, name text, owner uuid, metadata jsonb) OWNER TO supabase_storage_admin;

--
-- Name: extension(text); Type: FUNCTION; Schema: storage; Owner: supabase_storage_admin
--

CREATE FUNCTION storage.extension(name text) RETURNS text
    LANGUAGE plpgsql
    AS $$
DECLARE
_parts text[];
_filename text;
BEGIN
	select string_to_array(name, '/') into _parts;
	select _parts[array_length(_parts,1)] into _filename;
	-- @todo return the last part instead of 2
	return reverse(split_part(reverse(_filename), '.', 1));
END
$$;


ALTER FUNCTION storage.extension(name text) OWNER TO supabase_storage_admin;

--
-- Name: filename(text); Type: FUNCTION; Schema: storage; Owner: supabase_storage_admin
--

CREATE FUNCTION storage.filename(name text) RETURNS text
    LANGUAGE plpgsql
    AS $$
DECLARE
_parts text[];
BEGIN
	select string_to_array(name, '/') into _parts;
	return _parts[array_length(_parts,1)];
END
$$;


ALTER FUNCTION storage.filename(name text) OWNER TO supabase_storage_admin;

--
-- Name: foldername(text); Type: FUNCTION; Schema: storage; Owner: supabase_storage_admin
--

CREATE FUNCTION storage.foldername(name text) RETURNS text[]
    LANGUAGE plpgsql
    AS $$
DECLARE
_parts text[];
BEGIN
	select string_to_array(name, '/') into _parts;
	return _parts[1:array_length(_parts,1)-1];
END
$$;


ALTER FUNCTION storage.foldername(name text) OWNER TO supabase_storage_admin;

--
-- Name: get_size_by_bucket(); Type: FUNCTION; Schema: storage; Owner: supabase_storage_admin
--

CREATE FUNCTION storage.get_size_by_bucket() RETURNS TABLE(size bigint, bucket_id text)
    LANGUAGE plpgsql
    AS $$
BEGIN
    return query
        select sum((metadata->>'size')::int) as size, obj.bucket_id
        from "storage".objects as obj
        group by obj.bucket_id;
END
$$;


ALTER FUNCTION storage.get_size_by_bucket() OWNER TO supabase_storage_admin;

--
-- Name: search(text, text, integer, integer, integer, text, text, text); Type: FUNCTION; Schema: storage; Owner: supabase_storage_admin
--

CREATE FUNCTION storage.search(prefix text, bucketname text, limits integer DEFAULT 100, levels integer DEFAULT 1, offsets integer DEFAULT 0, search text DEFAULT ''::text, sortcolumn text DEFAULT 'name'::text, sortorder text DEFAULT 'asc'::text) RETURNS TABLE(name text, id uuid, updated_at timestamp with time zone, created_at timestamp with time zone, last_accessed_at timestamp with time zone, metadata jsonb)
    LANGUAGE plpgsql STABLE
    AS $_$
declare
  v_order_by text;
  v_sort_order text;
begin
  case
    when sortcolumn = 'name' then
      v_order_by = 'name';
    when sortcolumn = 'updated_at' then
      v_order_by = 'updated_at';
    when sortcolumn = 'created_at' then
      v_order_by = 'created_at';
    when sortcolumn = 'last_accessed_at' then
      v_order_by = 'last_accessed_at';
    else
      v_order_by = 'name';
  end case;

  case
    when sortorder = 'asc' then
      v_sort_order = 'asc';
    when sortorder = 'desc' then
      v_sort_order = 'desc';
    else
      v_sort_order = 'asc';
  end case;

  v_order_by = v_order_by || ' ' || v_sort_order;

  return query execute
    'with folders as (
       select path_tokens[$1] as folder
       from storage.objects
         where objects.name ilike $2 || $3 || ''%''
           and bucket_id = $4
           and array_length(regexp_split_to_array(objects.name, ''/''), 1) <> $1
       group by folder
       order by folder ' || v_sort_order || '
     )
     (select folder as "name",
            null as id,
            null as updated_at,
            null as created_at,
            null as last_accessed_at,
            null as metadata from folders)
     union all
     (select path_tokens[$1] as "name",
            id,
            updated_at,
            created_at,
            last_accessed_at,
            metadata
     from storage.objects
     where objects.name ilike $2 || $3 || ''%''
       and bucket_id = $4
       and array_length(regexp_split_to_array(objects.name, ''/''), 1) = $1
     order by ' || v_order_by || ')
     limit $5
     offset $6' using levels, prefix, search, bucketname, limits, offsets;
end;
$_$;


ALTER FUNCTION storage.search(prefix text, bucketname text, limits integer, levels integer, offsets integer, search text, sortcolumn text, sortorder text) OWNER TO supabase_storage_admin;

--
-- Name: update_updated_at_column(); Type: FUNCTION; Schema: storage; Owner: supabase_storage_admin
--

CREATE FUNCTION storage.update_updated_at_column() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW; 
END;
$$;


ALTER FUNCTION storage.update_updated_at_column() OWNER TO supabase_storage_admin;

--
-- Name: http_request(); Type: FUNCTION; Schema: supabase_functions; Owner: supabase_functions_admin
--

CREATE FUNCTION supabase_functions.http_request() RETURNS trigger
    LANGUAGE plpgsql SECURITY DEFINER
    SET search_path TO 'supabase_functions'
    AS $$
    DECLARE
      request_id bigint;
      payload jsonb;
      url text := TG_ARGV[0]::text;
      method text := TG_ARGV[1]::text;
      headers jsonb DEFAULT '{}'::jsonb;
      params jsonb DEFAULT '{}'::jsonb;
      timeout_ms integer DEFAULT 1000;
    BEGIN
      IF url IS NULL OR url = 'null' THEN
        RAISE EXCEPTION 'url argument is missing';
      END IF;

      IF method IS NULL OR method = 'null' THEN
        RAISE EXCEPTION 'method argument is missing';
      END IF;

      IF TG_ARGV[2] IS NULL OR TG_ARGV[2] = 'null' THEN
        headers = '{"Content-Type": "application/json"}'::jsonb;
      ELSE
        headers = TG_ARGV[2]::jsonb;
      END IF;

      IF TG_ARGV[3] IS NULL OR TG_ARGV[3] = 'null' THEN
        params = '{}'::jsonb;
      ELSE
        params = TG_ARGV[3]::jsonb;
      END IF;

      IF TG_ARGV[4] IS NULL OR TG_ARGV[4] = 'null' THEN
        timeout_ms = 1000;
      ELSE
        timeout_ms = TG_ARGV[4]::integer;
      END IF;

      CASE
        WHEN method = 'GET' THEN
          SELECT http_get INTO request_id FROM net.http_get(
            url,
            params,
            headers,
            timeout_ms
          );
        WHEN method = 'POST' THEN
          payload = jsonb_build_object(
            'old_record', OLD,
            'record', NEW,
            'type', TG_OP,
            'table', TG_TABLE_NAME,
            'schema', TG_TABLE_SCHEMA
          );

          SELECT http_post INTO request_id FROM net.http_post(
            url,
            payload,
            params,
            headers,
            timeout_ms
          );
        ELSE
          RAISE EXCEPTION 'method argument % is invalid', method;
      END CASE;

      INSERT INTO supabase_functions.hooks
        (hook_table_id, hook_name, request_id)
      VALUES
        (TG_RELID, TG_NAME, request_id);

      RETURN NEW;
    END
  $$;


ALTER FUNCTION supabase_functions.http_request() OWNER TO supabase_functions_admin;

--
-- Name: secrets_encrypt_secret_secret(); Type: FUNCTION; Schema: vault; Owner: supabase_admin
--

CREATE FUNCTION vault.secrets_encrypt_secret_secret() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
		BEGIN
		        new.secret = CASE WHEN new.secret IS NULL THEN NULL ELSE
			CASE WHEN new.key_id IS NULL THEN NULL ELSE pg_catalog.encode(
			  pgsodium.crypto_aead_det_encrypt(
				pg_catalog.convert_to(new.secret, 'utf8'),
				pg_catalog.convert_to((new.id::text || new.description::text || new.created_at::text || new.updated_at::text)::text, 'utf8'),
				new.key_id::uuid,
				new.nonce
			  ),
				'base64') END END;
		RETURN new;
		END;
		$$;


ALTER FUNCTION vault.secrets_encrypt_secret_secret() OWNER TO supabase_admin;

SET default_tablespace = '';

SET default_table_access_method = heap;

--
-- Name: audit_log_entries; Type: TABLE; Schema: auth; Owner: supabase_auth_admin
--

CREATE TABLE auth.audit_log_entries (
    instance_id uuid,
    id uuid NOT NULL,
    payload json,
    created_at timestamp with time zone,
    ip_address character varying(64) DEFAULT ''::character varying NOT NULL
);


ALTER TABLE auth.audit_log_entries OWNER TO supabase_auth_admin;

--
-- Name: TABLE audit_log_entries; Type: COMMENT; Schema: auth; Owner: supabase_auth_admin
--

COMMENT ON TABLE auth.audit_log_entries IS 'Auth: Audit trail for user actions.';


--
-- Name: flow_state; Type: TABLE; Schema: auth; Owner: supabase_auth_admin
--

CREATE TABLE auth.flow_state (
    id uuid NOT NULL,
    user_id uuid,
    auth_code text NOT NULL,
    code_challenge_method auth.code_challenge_method NOT NULL,
    code_challenge text NOT NULL,
    provider_type text NOT NULL,
    provider_access_token text,
    provider_refresh_token text,
    created_at timestamp with time zone,
    updated_at timestamp with time zone,
    authentication_method text NOT NULL,
    auth_code_issued_at timestamp with time zone
);


ALTER TABLE auth.flow_state OWNER TO supabase_auth_admin;

--
-- Name: TABLE flow_state; Type: COMMENT; Schema: auth; Owner: supabase_auth_admin
--

COMMENT ON TABLE auth.flow_state IS 'stores metadata for pkce logins';


--
-- Name: identities; Type: TABLE; Schema: auth; Owner: supabase_auth_admin
--

CREATE TABLE auth.identities (
    provider_id text NOT NULL,
    user_id uuid NOT NULL,
    identity_data jsonb NOT NULL,
    provider text NOT NULL,
    last_sign_in_at timestamp with time zone,
    created_at timestamp with time zone,
    updated_at timestamp with time zone,
    email text GENERATED ALWAYS AS (lower((identity_data ->> 'email'::text))) STORED,
    id uuid DEFAULT gen_random_uuid() NOT NULL
);


ALTER TABLE auth.identities OWNER TO supabase_auth_admin;

--
-- Name: TABLE identities; Type: COMMENT; Schema: auth; Owner: supabase_auth_admin
--

COMMENT ON TABLE auth.identities IS 'Auth: Stores identities associated to a user.';


--
-- Name: COLUMN identities.email; Type: COMMENT; Schema: auth; Owner: supabase_auth_admin
--

COMMENT ON COLUMN auth.identities.email IS 'Auth: Email is a generated column that references the optional email property in the identity_data';


--
-- Name: instances; Type: TABLE; Schema: auth; Owner: supabase_auth_admin
--

CREATE TABLE auth.instances (
    id uuid NOT NULL,
    uuid uuid,
    raw_base_config text,
    created_at timestamp with time zone,
    updated_at timestamp with time zone
);


ALTER TABLE auth.instances OWNER TO supabase_auth_admin;

--
-- Name: TABLE instances; Type: COMMENT; Schema: auth; Owner: supabase_auth_admin
--

COMMENT ON TABLE auth.instances IS 'Auth: Manages users across multiple sites.';


--
-- Name: mfa_amr_claims; Type: TABLE; Schema: auth; Owner: supabase_auth_admin
--

CREATE TABLE auth.mfa_amr_claims (
    session_id uuid NOT NULL,
    created_at timestamp with time zone NOT NULL,
    updated_at timestamp with time zone NOT NULL,
    authentication_method text NOT NULL,
    id uuid NOT NULL
);


ALTER TABLE auth.mfa_amr_claims OWNER TO supabase_auth_admin;

--
-- Name: TABLE mfa_amr_claims; Type: COMMENT; Schema: auth; Owner: supabase_auth_admin
--

COMMENT ON TABLE auth.mfa_amr_claims IS 'auth: stores authenticator method reference claims for multi factor authentication';


--
-- Name: mfa_challenges; Type: TABLE; Schema: auth; Owner: supabase_auth_admin
--

CREATE TABLE auth.mfa_challenges (
    id uuid NOT NULL,
    factor_id uuid NOT NULL,
    created_at timestamp with time zone NOT NULL,
    verified_at timestamp with time zone,
    ip_address inet NOT NULL
);


ALTER TABLE auth.mfa_challenges OWNER TO supabase_auth_admin;

--
-- Name: TABLE mfa_challenges; Type: COMMENT; Schema: auth; Owner: supabase_auth_admin
--

COMMENT ON TABLE auth.mfa_challenges IS 'auth: stores metadata about challenge requests made';


--
-- Name: mfa_factors; Type: TABLE; Schema: auth; Owner: supabase_auth_admin
--

CREATE TABLE auth.mfa_factors (
    id uuid NOT NULL,
    user_id uuid NOT NULL,
    friendly_name text,
    factor_type auth.factor_type NOT NULL,
    status auth.factor_status NOT NULL,
    created_at timestamp with time zone NOT NULL,
    updated_at timestamp with time zone NOT NULL,
    secret text
);


ALTER TABLE auth.mfa_factors OWNER TO supabase_auth_admin;

--
-- Name: TABLE mfa_factors; Type: COMMENT; Schema: auth; Owner: supabase_auth_admin
--

COMMENT ON TABLE auth.mfa_factors IS 'auth: stores metadata about factors';


--
-- Name: refresh_tokens; Type: TABLE; Schema: auth; Owner: supabase_auth_admin
--

CREATE TABLE auth.refresh_tokens (
    instance_id uuid,
    id bigint NOT NULL,
    token character varying(255),
    user_id character varying(255),
    revoked boolean,
    created_at timestamp with time zone,
    updated_at timestamp with time zone,
    parent character varying(255),
    session_id uuid
);


ALTER TABLE auth.refresh_tokens OWNER TO supabase_auth_admin;

--
-- Name: TABLE refresh_tokens; Type: COMMENT; Schema: auth; Owner: supabase_auth_admin
--

COMMENT ON TABLE auth.refresh_tokens IS 'Auth: Store of tokens used to refresh JWT tokens once they expire.';


--
-- Name: refresh_tokens_id_seq; Type: SEQUENCE; Schema: auth; Owner: supabase_auth_admin
--

CREATE SEQUENCE auth.refresh_tokens_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE auth.refresh_tokens_id_seq OWNER TO supabase_auth_admin;

--
-- Name: refresh_tokens_id_seq; Type: SEQUENCE OWNED BY; Schema: auth; Owner: supabase_auth_admin
--

ALTER SEQUENCE auth.refresh_tokens_id_seq OWNED BY auth.refresh_tokens.id;


--
-- Name: saml_providers; Type: TABLE; Schema: auth; Owner: supabase_auth_admin
--

CREATE TABLE auth.saml_providers (
    id uuid NOT NULL,
    sso_provider_id uuid NOT NULL,
    entity_id text NOT NULL,
    metadata_xml text NOT NULL,
    metadata_url text,
    attribute_mapping jsonb,
    created_at timestamp with time zone,
    updated_at timestamp with time zone,
    name_id_format text,
    CONSTRAINT "entity_id not empty" CHECK ((char_length(entity_id) > 0)),
    CONSTRAINT "metadata_url not empty" CHECK (((metadata_url = NULL::text) OR (char_length(metadata_url) > 0))),
    CONSTRAINT "metadata_xml not empty" CHECK ((char_length(metadata_xml) > 0))
);


ALTER TABLE auth.saml_providers OWNER TO supabase_auth_admin;

--
-- Name: TABLE saml_providers; Type: COMMENT; Schema: auth; Owner: supabase_auth_admin
--

COMMENT ON TABLE auth.saml_providers IS 'Auth: Manages SAML Identity Provider connections.';


--
-- Name: saml_relay_states; Type: TABLE; Schema: auth; Owner: supabase_auth_admin
--

CREATE TABLE auth.saml_relay_states (
    id uuid NOT NULL,
    sso_provider_id uuid NOT NULL,
    request_id text NOT NULL,
    for_email text,
    redirect_to text,
    created_at timestamp with time zone,
    updated_at timestamp with time zone,
    flow_state_id uuid,
    CONSTRAINT "request_id not empty" CHECK ((char_length(request_id) > 0))
);


ALTER TABLE auth.saml_relay_states OWNER TO supabase_auth_admin;

--
-- Name: TABLE saml_relay_states; Type: COMMENT; Schema: auth; Owner: supabase_auth_admin
--

COMMENT ON TABLE auth.saml_relay_states IS 'Auth: Contains SAML Relay State information for each Service Provider initiated login.';


--
-- Name: schema_migrations; Type: TABLE; Schema: auth; Owner: supabase_auth_admin
--

CREATE TABLE auth.schema_migrations (
    version character varying(255) NOT NULL
);


ALTER TABLE auth.schema_migrations OWNER TO supabase_auth_admin;

--
-- Name: TABLE schema_migrations; Type: COMMENT; Schema: auth; Owner: supabase_auth_admin
--

COMMENT ON TABLE auth.schema_migrations IS 'Auth: Manages updates to the auth system.';


--
-- Name: sessions; Type: TABLE; Schema: auth; Owner: supabase_auth_admin
--

CREATE TABLE auth.sessions (
    id uuid NOT NULL,
    user_id uuid NOT NULL,
    created_at timestamp with time zone,
    updated_at timestamp with time zone,
    factor_id uuid,
    aal auth.aal_level,
    not_after timestamp with time zone,
    refreshed_at timestamp without time zone,
    user_agent text,
    ip inet,
    tag text
);


ALTER TABLE auth.sessions OWNER TO supabase_auth_admin;

--
-- Name: TABLE sessions; Type: COMMENT; Schema: auth; Owner: supabase_auth_admin
--

COMMENT ON TABLE auth.sessions IS 'Auth: Stores session data associated to a user.';


--
-- Name: COLUMN sessions.not_after; Type: COMMENT; Schema: auth; Owner: supabase_auth_admin
--

COMMENT ON COLUMN auth.sessions.not_after IS 'Auth: Not after is a nullable column that contains a timestamp after which the session should be regarded as expired.';


--
-- Name: sso_domains; Type: TABLE; Schema: auth; Owner: supabase_auth_admin
--

CREATE TABLE auth.sso_domains (
    id uuid NOT NULL,
    sso_provider_id uuid NOT NULL,
    domain text NOT NULL,
    created_at timestamp with time zone,
    updated_at timestamp with time zone,
    CONSTRAINT "domain not empty" CHECK ((char_length(domain) > 0))
);


ALTER TABLE auth.sso_domains OWNER TO supabase_auth_admin;

--
-- Name: TABLE sso_domains; Type: COMMENT; Schema: auth; Owner: supabase_auth_admin
--

COMMENT ON TABLE auth.sso_domains IS 'Auth: Manages SSO email address domain mapping to an SSO Identity Provider.';


--
-- Name: sso_providers; Type: TABLE; Schema: auth; Owner: supabase_auth_admin
--

CREATE TABLE auth.sso_providers (
    id uuid NOT NULL,
    resource_id text,
    created_at timestamp with time zone,
    updated_at timestamp with time zone,
    CONSTRAINT "resource_id not empty" CHECK (((resource_id = NULL::text) OR (char_length(resource_id) > 0)))
);


ALTER TABLE auth.sso_providers OWNER TO supabase_auth_admin;

--
-- Name: TABLE sso_providers; Type: COMMENT; Schema: auth; Owner: supabase_auth_admin
--

COMMENT ON TABLE auth.sso_providers IS 'Auth: Manages SSO identity provider information; see saml_providers for SAML.';


--
-- Name: COLUMN sso_providers.resource_id; Type: COMMENT; Schema: auth; Owner: supabase_auth_admin
--

COMMENT ON COLUMN auth.sso_providers.resource_id IS 'Auth: Uniquely identifies a SSO provider according to a user-chosen resource ID (case insensitive), useful in infrastructure as code.';


--
-- Name: users; Type: TABLE; Schema: auth; Owner: supabase_auth_admin
--

CREATE TABLE auth.users (
    instance_id uuid,
    id uuid NOT NULL,
    aud character varying(255),
    role character varying(255),
    email character varying(255),
    encrypted_password character varying(255),
    email_confirmed_at timestamp with time zone,
    invited_at timestamp with time zone,
    confirmation_token character varying(255),
    confirmation_sent_at timestamp with time zone,
    recovery_token character varying(255),
    recovery_sent_at timestamp with time zone,
    email_change_token_new character varying(255),
    email_change character varying(255),
    email_change_sent_at timestamp with time zone,
    last_sign_in_at timestamp with time zone,
    raw_app_meta_data jsonb,
    raw_user_meta_data jsonb,
    is_super_admin boolean,
    created_at timestamp with time zone,
    updated_at timestamp with time zone,
    phone text DEFAULT NULL::character varying,
    phone_confirmed_at timestamp with time zone,
    phone_change text DEFAULT ''::character varying,
    phone_change_token character varying(255) DEFAULT ''::character varying,
    phone_change_sent_at timestamp with time zone,
    confirmed_at timestamp with time zone GENERATED ALWAYS AS (LEAST(email_confirmed_at, phone_confirmed_at)) STORED,
    email_change_token_current character varying(255) DEFAULT ''::character varying,
    email_change_confirm_status smallint DEFAULT 0,
    banned_until timestamp with time zone,
    reauthentication_token character varying(255) DEFAULT ''::character varying,
    reauthentication_sent_at timestamp with time zone,
    is_sso_user boolean DEFAULT false NOT NULL,
    deleted_at timestamp with time zone,
    is_anonymous boolean DEFAULT false NOT NULL,
    CONSTRAINT users_email_change_confirm_status_check CHECK (((email_change_confirm_status >= 0) AND (email_change_confirm_status <= 2)))
);


ALTER TABLE auth.users OWNER TO supabase_auth_admin;

--
-- Name: TABLE users; Type: COMMENT; Schema: auth; Owner: supabase_auth_admin
--

COMMENT ON TABLE auth.users IS 'Auth: Stores user login data within a secure schema.';


--
-- Name: COLUMN users.is_sso_user; Type: COMMENT; Schema: auth; Owner: supabase_auth_admin
--

COMMENT ON COLUMN auth.users.is_sso_user IS 'Auth: Set this column to true when the account comes from SSO. These accounts can have duplicate emails.';


--
-- Name: anchors; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.anchors (
    anchor_name character varying(255),
    live_url character varying(255),
    new_url character varying(255),
    tsid character varying(255),
    mp4id character varying(255),
    status boolean DEFAULT false NOT NULL,
    is_recording boolean DEFAULT false,
    anchor_id uuid DEFAULT gen_random_uuid() NOT NULL,
    "timestamp" timestamp with time zone DEFAULT now(),
    nosub boolean DEFAULT false NOT NULL,
    record boolean DEFAULT true NOT NULL,
    platform text,
    title text,
    record_url text
);

ALTER TABLE ONLY public.anchors REPLICA IDENTITY FULL;


ALTER TABLE public.anchors OWNER TO postgres;

--
-- Name: file; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.file (
    anchor_name text,
    anchor_id uuid NOT NULL,
    id text,
    "timestamp" timestamp without time zone DEFAULT now(),
    timestampz timestamp with time zone DEFAULT now(),
    date date DEFAULT now(),
    type boolean
);


ALTER TABLE public.file OWNER TO postgres;

--
-- Name: user_subscriptions; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.user_subscriptions (
    user_id text NOT NULL,
    anchor_id uuid NOT NULL,
    send_file boolean DEFAULT true NOT NULL,
    send_video boolean DEFAULT true NOT NULL,
    push_notification boolean DEFAULT true NOT NULL
);


ALTER TABLE public.user_subscriptions OWNER TO postgres;

--
-- Name: users; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.users (
    user_id text NOT NULL,
    username character varying(255),
    allowance integer DEFAULT 2 NOT NULL,
    tier character varying(20) DEFAULT 'free'::character varying NOT NULL,
    push_notification boolean DEFAULT true,
    send_file boolean DEFAULT false NOT NULL,
    send_video boolean DEFAULT false NOT NULL
);


ALTER TABLE public.users OWNER TO postgres;

--
-- Name: users_user_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.users_user_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.users_user_id_seq OWNER TO postgres;

--
-- Name: users_user_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.users_user_id_seq OWNED BY public.users.user_id;


--
-- Name: broadcasts; Type: TABLE; Schema: realtime; Owner: supabase_realtime_admin
--

CREATE TABLE realtime.broadcasts (
    id bigint NOT NULL,
    channel_id bigint NOT NULL,
    "check" boolean DEFAULT false NOT NULL,
    inserted_at timestamp(0) without time zone NOT NULL,
    updated_at timestamp(0) without time zone NOT NULL
);


ALTER TABLE realtime.broadcasts OWNER TO supabase_realtime_admin;

--
-- Name: broadcasts_id_seq; Type: SEQUENCE; Schema: realtime; Owner: supabase_realtime_admin
--

CREATE SEQUENCE realtime.broadcasts_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE realtime.broadcasts_id_seq OWNER TO supabase_realtime_admin;

--
-- Name: broadcasts_id_seq; Type: SEQUENCE OWNED BY; Schema: realtime; Owner: supabase_realtime_admin
--

ALTER SEQUENCE realtime.broadcasts_id_seq OWNED BY realtime.broadcasts.id;


--
-- Name: channels; Type: TABLE; Schema: realtime; Owner: supabase_realtime_admin
--

CREATE TABLE realtime.channels (
    id bigint NOT NULL,
    name character varying(255) NOT NULL,
    inserted_at timestamp(0) without time zone NOT NULL,
    updated_at timestamp(0) without time zone NOT NULL,
    "check" boolean DEFAULT false
);


ALTER TABLE realtime.channels OWNER TO supabase_realtime_admin;

--
-- Name: channels_id_seq; Type: SEQUENCE; Schema: realtime; Owner: supabase_realtime_admin
--

CREATE SEQUENCE realtime.channels_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE realtime.channels_id_seq OWNER TO supabase_realtime_admin;

--
-- Name: channels_id_seq; Type: SEQUENCE OWNED BY; Schema: realtime; Owner: supabase_realtime_admin
--

ALTER SEQUENCE realtime.channels_id_seq OWNED BY realtime.channels.id;


--
-- Name: presences; Type: TABLE; Schema: realtime; Owner: supabase_realtime_admin
--

CREATE TABLE realtime.presences (
    id bigint NOT NULL,
    channel_id bigint NOT NULL,
    "check" boolean DEFAULT false NOT NULL,
    inserted_at timestamp(0) without time zone NOT NULL,
    updated_at timestamp(0) without time zone NOT NULL
);


ALTER TABLE realtime.presences OWNER TO supabase_realtime_admin;

--
-- Name: presences_id_seq; Type: SEQUENCE; Schema: realtime; Owner: supabase_realtime_admin
--

CREATE SEQUENCE realtime.presences_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE realtime.presences_id_seq OWNER TO supabase_realtime_admin;

--
-- Name: presences_id_seq; Type: SEQUENCE OWNED BY; Schema: realtime; Owner: supabase_realtime_admin
--

ALTER SEQUENCE realtime.presences_id_seq OWNED BY realtime.presences.id;


--
-- Name: schema_migrations; Type: TABLE; Schema: realtime; Owner: supabase_admin
--

CREATE TABLE realtime.schema_migrations (
    version bigint NOT NULL,
    inserted_at timestamp(0) without time zone
);


ALTER TABLE realtime.schema_migrations OWNER TO supabase_admin;

--
-- Name: subscription; Type: TABLE; Schema: realtime; Owner: supabase_admin
--

CREATE TABLE realtime.subscription (
    id bigint NOT NULL,
    subscription_id uuid NOT NULL,
    entity regclass NOT NULL,
    filters realtime.user_defined_filter[] DEFAULT '{}'::realtime.user_defined_filter[] NOT NULL,
    claims jsonb NOT NULL,
    claims_role regrole GENERATED ALWAYS AS (realtime.to_regrole((claims ->> 'role'::text))) STORED NOT NULL,
    created_at timestamp without time zone DEFAULT timezone('utc'::text, now()) NOT NULL
);


ALTER TABLE realtime.subscription OWNER TO supabase_admin;

--
-- Name: subscription_id_seq; Type: SEQUENCE; Schema: realtime; Owner: supabase_admin
--

ALTER TABLE realtime.subscription ALTER COLUMN id ADD GENERATED ALWAYS AS IDENTITY (
    SEQUENCE NAME realtime.subscription_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);


--
-- Name: buckets; Type: TABLE; Schema: storage; Owner: supabase_storage_admin
--

CREATE TABLE storage.buckets (
    id text NOT NULL,
    name text NOT NULL,
    owner uuid,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now(),
    public boolean DEFAULT false,
    avif_autodetection boolean DEFAULT false,
    file_size_limit bigint,
    allowed_mime_types text[],
    owner_id text
);


ALTER TABLE storage.buckets OWNER TO supabase_storage_admin;

--
-- Name: COLUMN buckets.owner; Type: COMMENT; Schema: storage; Owner: supabase_storage_admin
--

COMMENT ON COLUMN storage.buckets.owner IS 'Field is deprecated, use owner_id instead';


--
-- Name: migrations; Type: TABLE; Schema: storage; Owner: supabase_storage_admin
--

CREATE TABLE storage.migrations (
    id integer NOT NULL,
    name character varying(100) NOT NULL,
    hash character varying(40) NOT NULL,
    executed_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE storage.migrations OWNER TO supabase_storage_admin;

--
-- Name: objects; Type: TABLE; Schema: storage; Owner: supabase_storage_admin
--

CREATE TABLE storage.objects (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    bucket_id text,
    name text,
    owner uuid,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now(),
    last_accessed_at timestamp with time zone DEFAULT now(),
    metadata jsonb,
    path_tokens text[] GENERATED ALWAYS AS (string_to_array(name, '/'::text)) STORED,
    version text,
    owner_id text
);


ALTER TABLE storage.objects OWNER TO supabase_storage_admin;

--
-- Name: COLUMN objects.owner; Type: COMMENT; Schema: storage; Owner: supabase_storage_admin
--

COMMENT ON COLUMN storage.objects.owner IS 'Field is deprecated, use owner_id instead';


--
-- Name: hooks; Type: TABLE; Schema: supabase_functions; Owner: supabase_functions_admin
--

CREATE TABLE supabase_functions.hooks (
    id bigint NOT NULL,
    hook_table_id integer NOT NULL,
    hook_name text NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    request_id bigint
);


ALTER TABLE supabase_functions.hooks OWNER TO supabase_functions_admin;

--
-- Name: TABLE hooks; Type: COMMENT; Schema: supabase_functions; Owner: supabase_functions_admin
--

COMMENT ON TABLE supabase_functions.hooks IS 'Supabase Functions Hooks: Audit trail for triggered hooks.';


--
-- Name: hooks_id_seq; Type: SEQUENCE; Schema: supabase_functions; Owner: supabase_functions_admin
--

CREATE SEQUENCE supabase_functions.hooks_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE supabase_functions.hooks_id_seq OWNER TO supabase_functions_admin;

--
-- Name: hooks_id_seq; Type: SEQUENCE OWNED BY; Schema: supabase_functions; Owner: supabase_functions_admin
--

ALTER SEQUENCE supabase_functions.hooks_id_seq OWNED BY supabase_functions.hooks.id;


--
-- Name: migrations; Type: TABLE; Schema: supabase_functions; Owner: supabase_functions_admin
--

CREATE TABLE supabase_functions.migrations (
    version text NOT NULL,
    inserted_at timestamp with time zone DEFAULT now() NOT NULL
);


ALTER TABLE supabase_functions.migrations OWNER TO supabase_functions_admin;

--
-- Name: decrypted_secrets; Type: VIEW; Schema: vault; Owner: supabase_admin
--

CREATE VIEW vault.decrypted_secrets AS
 SELECT secrets.id,
    secrets.name,
    secrets.description,
    secrets.secret,
        CASE
            WHEN (secrets.secret IS NULL) THEN NULL::text
            ELSE
            CASE
                WHEN (secrets.key_id IS NULL) THEN NULL::text
                ELSE convert_from(pgsodium.crypto_aead_det_decrypt(decode(secrets.secret, 'base64'::text), convert_to(((((secrets.id)::text || secrets.description) || (secrets.created_at)::text) || (secrets.updated_at)::text), 'utf8'::name), secrets.key_id, secrets.nonce), 'utf8'::name)
            END
        END AS decrypted_secret,
    secrets.key_id,
    secrets.nonce,
    secrets.created_at,
    secrets.updated_at
   FROM vault.secrets;


ALTER TABLE vault.decrypted_secrets OWNER TO supabase_admin;

--
-- Name: refresh_tokens id; Type: DEFAULT; Schema: auth; Owner: supabase_auth_admin
--

ALTER TABLE ONLY auth.refresh_tokens ALTER COLUMN id SET DEFAULT nextval('auth.refresh_tokens_id_seq'::regclass);


--
-- Name: broadcasts id; Type: DEFAULT; Schema: realtime; Owner: supabase_realtime_admin
--

ALTER TABLE ONLY realtime.broadcasts ALTER COLUMN id SET DEFAULT nextval('realtime.broadcasts_id_seq'::regclass);


--
-- Name: channels id; Type: DEFAULT; Schema: realtime; Owner: supabase_realtime_admin
--

ALTER TABLE ONLY realtime.channels ALTER COLUMN id SET DEFAULT nextval('realtime.channels_id_seq'::regclass);


--
-- Name: presences id; Type: DEFAULT; Schema: realtime; Owner: supabase_realtime_admin
--

ALTER TABLE ONLY realtime.presences ALTER COLUMN id SET DEFAULT nextval('realtime.presences_id_seq'::regclass);


--
-- Name: hooks id; Type: DEFAULT; Schema: supabase_functions; Owner: supabase_functions_admin
--

ALTER TABLE ONLY supabase_functions.hooks ALTER COLUMN id SET DEFAULT nextval('supabase_functions.hooks_id_seq'::regclass);


--
-- Data for Name: audit_log_entries; Type: TABLE DATA; Schema: auth; Owner: supabase_auth_admin
--

COPY auth.audit_log_entries (instance_id, id, payload, created_at, ip_address) FROM stdin;
\.


--
-- Data for Name: flow_state; Type: TABLE DATA; Schema: auth; Owner: supabase_auth_admin
--

COPY auth.flow_state (id, user_id, auth_code, code_challenge_method, code_challenge, provider_type, provider_access_token, provider_refresh_token, created_at, updated_at, authentication_method, auth_code_issued_at) FROM stdin;
\.


--
-- Data for Name: identities; Type: TABLE DATA; Schema: auth; Owner: supabase_auth_admin
--

COPY auth.identities (provider_id, user_id, identity_data, provider, last_sign_in_at, created_at, updated_at, id) FROM stdin;
\.


--
-- Data for Name: instances; Type: TABLE DATA; Schema: auth; Owner: supabase_auth_admin
--

COPY auth.instances (id, uuid, raw_base_config, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: mfa_amr_claims; Type: TABLE DATA; Schema: auth; Owner: supabase_auth_admin
--

COPY auth.mfa_amr_claims (session_id, created_at, updated_at, authentication_method, id) FROM stdin;
\.


--
-- Data for Name: mfa_challenges; Type: TABLE DATA; Schema: auth; Owner: supabase_auth_admin
--

COPY auth.mfa_challenges (id, factor_id, created_at, verified_at, ip_address) FROM stdin;
\.


--
-- Data for Name: mfa_factors; Type: TABLE DATA; Schema: auth; Owner: supabase_auth_admin
--

COPY auth.mfa_factors (id, user_id, friendly_name, factor_type, status, created_at, updated_at, secret) FROM stdin;
\.


--
-- Data for Name: refresh_tokens; Type: TABLE DATA; Schema: auth; Owner: supabase_auth_admin
--

COPY auth.refresh_tokens (instance_id, id, token, user_id, revoked, created_at, updated_at, parent, session_id) FROM stdin;
\.


--
-- Data for Name: saml_providers; Type: TABLE DATA; Schema: auth; Owner: supabase_auth_admin
--

COPY auth.saml_providers (id, sso_provider_id, entity_id, metadata_xml, metadata_url, attribute_mapping, created_at, updated_at, name_id_format) FROM stdin;
\.


--
-- Data for Name: saml_relay_states; Type: TABLE DATA; Schema: auth; Owner: supabase_auth_admin
--

COPY auth.saml_relay_states (id, sso_provider_id, request_id, for_email, redirect_to, created_at, updated_at, flow_state_id) FROM stdin;
\.


--
-- Data for Name: schema_migrations; Type: TABLE DATA; Schema: auth; Owner: supabase_auth_admin
--

COPY auth.schema_migrations (version) FROM stdin;
20171026211738
20171026211808
20171026211834
20180103212743
20180108183307
20180119214651
20180125194653
00
20210710035447
20210722035447
20210730183235
20210909172000
20210927181326
20211122151130
20211124214934
20211202183645
20220114185221
20220114185340
20220224000811
20220323170000
20220429102000
20220531120530
20220614074223
20220811173540
20221003041349
20221003041400
20221011041400
20221020193600
20221021073300
20221021082433
20221027105023
20221114143122
20221114143410
20221125140132
20221208132122
20221215195500
20221215195800
20221215195900
20230116124310
20230116124412
20230131181311
20230322519590
20230402418590
20230411005111
20230508135423
20230523124323
20230818113222
20230914180801
20231027141322
20231114161723
20231117164230
20240115144230
20240214120130
20240306115329
20240314092811
\.


--
-- Data for Name: sessions; Type: TABLE DATA; Schema: auth; Owner: supabase_auth_admin
--

COPY auth.sessions (id, user_id, created_at, updated_at, factor_id, aal, not_after, refreshed_at, user_agent, ip, tag) FROM stdin;
\.


--
-- Data for Name: sso_domains; Type: TABLE DATA; Schema: auth; Owner: supabase_auth_admin
--

COPY auth.sso_domains (id, sso_provider_id, domain, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: sso_providers; Type: TABLE DATA; Schema: auth; Owner: supabase_auth_admin
--

COPY auth.sso_providers (id, resource_id, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: users; Type: TABLE DATA; Schema: auth; Owner: supabase_auth_admin
--

COPY auth.users (instance_id, id, aud, role, email, encrypted_password, email_confirmed_at, invited_at, confirmation_token, confirmation_sent_at, recovery_token, recovery_sent_at, email_change_token_new, email_change, email_change_sent_at, last_sign_in_at, raw_app_meta_data, raw_user_meta_data, is_super_admin, created_at, updated_at, phone, phone_confirmed_at, phone_change, phone_change_token, phone_change_sent_at, email_change_token_current, email_change_confirm_status, banned_until, reauthentication_token, reauthentication_sent_at, is_sso_user, deleted_at, is_anonymous) FROM stdin;
\.


--
-- Data for Name: key; Type: TABLE DATA; Schema: pgsodium; Owner: supabase_admin
--

COPY pgsodium.key (id, status, created, expires, key_type, key_id, key_context, name, associated_data, raw_key, raw_key_nonce, parent_key, comment, user_data) FROM stdin;
\.


--
-- Data for Name: anchors; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.anchors (anchor_name, live_url, new_url, tsid, mp4id, status, is_recording, anchor_id, "timestamp", nosub, record, platform, title, record_url) FROM stdin;
李荣浩	https://v.douyin.com/iYyrn2ed/	https://live.douyin.com/284354165982	\N	\N	f	f	95fb28ed-2420-4ab7-a122-af056b1dc507	2024-04-10 12:59:35.069378+00	f	t	Douyin	\N	\N
小张有点恐高🇨🇳	https://v.douyin.com/iYSXsUfc/	https://live.douyin.com/276022856919	\N	\N	f	f	f74a944f-29b8-4e1d-8a75-96c4c0544d0a	2024-04-08 14:15:15.494744+00	f	t	Douyin	\N	\N
叫我阿豪	https://v.douyin.com/iYyrt5nb/	https://live.douyin.com/265604476618	\N	\N	f	f	43f838f0-821c-4119-b0f1-c730bfc7a3c6	2024-04-10 13:01:45.7791+00	f	t	Douyin	\N	\N
侃哥侃英语	https://v.douyin.com/iFyY4yQ1/	https://live.douyin.com/777848332017	BQACAgEAAxkDAAIDTWYKz36ZPZQIXbdCNFGBM9B1ivKFAAKXBAACVw5ZRPW09wHmIoFQHgQ	BAACAgEAAxkDAAIDU2YK0b5-pB-CbhH_5drIV5Ajav5jAAKbBAACVw5ZRJlxg2Hjx_AdHgQ	f	t	b463ded3-6d4b-4471-82b8-ee107fa2539e	2024-03-30 05:12:01.335092+00	f	t	Douyin	\N	\N
英语一对一外教	https://v.douyin.com/iYDTpKhg/	https://live.douyin.com/942303968907	\N	\N	f	f	580b49c8-dec3-41bb-8490-0655ddf0d664	2024-04-09 22:44:34.263446+00	f	t	Douyin	\N	\N
長春軍哥	https://v.douyin.com/iF3Qc7B6/	https://live.douyin.com/271638035024	BQACAgUAAxkDAAIZGmYPTgwt_agpbFprmgxkuh6HdtNzAALeDQACJuV4VOdyZDrEFAjnHgQ	BAACAgUAAxkDAAIZF2YPTfNsNga_BIJWuitw0khPiH3sAALdDQACJuV4VNMoWJP3hTsSHgQ	f	f	b0f1aa62-fb70-410f-a795-95a61f90cce8	2024-03-30 05:12:01.335092+00	f	t	Douyin	\N	\N
小声一点	https://v.douyin.com/iYhBce3J/	https://live.douyin.com/491994449456	\N	\N	f	f	16d9d7da-5c5d-44f2-81b8-296bbcca83e6	2024-04-06 23:52:27.350905+00	f	t	Douyin	\N	\N
KK长沙	https://v.douyin.com/iYykP8Dt/	https://live.douyin.com/23157115615	\N	\N	f	f	cee6846c-29aa-4172-9c7f-1cb1a581b67a	2024-04-10 13:17:08.870881+00	f	t	Douyin	\N	\N
小胖爱生活	https://v.douyin.com/iYRapowT/	https://live.douyin.com/*********	BQACAgUAAxkDAAIYYmYOz-9E_wtNsZzC137ngCyQylihAAITEgACJuVwVL0mnaFYNGLCHgQ	BAACAgUAAxkDAAIYXGYOzxnhSqYknOlRW1xA_UxywtcOAAIOEgACJuVwVORYJopfevmtHgQ	t	t	1780d203-0cb4-41d1-9a63-f2171aed73d6	2024-04-01 14:24:09.167173+00	f	t	Douyin	\N	\N
ace	https://www.tiktok.com/t/ZPRT5WVYF/	https://www.tiktok.com/@stariiaz/live	BQACAgUAAxkDAAIQiWYMswWEPYgVSdodrPdQYWo1KXvfAAKwDgACNL9gVIkhqn4x_PC-HgQ	BAACAgUAAxkDAAIQi2YMs87sRSNTPDmOV5iWV-lngBh4AAKxDgACNL9gVBvyexOlm9rUHgQ	f	t	36ba9612-6a58-4eec-80f9-3d2e1dbbcddd	2024-03-30 05:52:08.804532+00	t	t	TikTok	\N	\N
あすちぃチャンネル　asuchy channel	https://www.tiktok.com/@asukaa_aa/live	https://www.tiktok.com/@asukaa_aa/live	BQACAgUAAxkDAAISt2YNybaJZ6iK3BXZaGcbCqUWsxQmAAKwDwACNL9oVH31IdtsZiodHgQ	BAACAgUAAxkDAAISuWYNybhvIYzMbNXbFSkeiTodGjTPAAKxDwACNL9oVNw8SV7khvjdHgQ	f	t	a17ec824-4e7c-40b8-9701-2263a3b5f68a	2024-03-30 05:12:01.335092+00	f	t	TikTok	\N	\N
SusieWoo 戴舒萱	https://v.douyin.com/iFP2eMjg/	https://live.douyin.com/305210738760	BQACAgUAAxkDAAIZT2YPm9JHBkbbnBZUf1eDfH6TpLFmAAJfDgACJuV4VGZQzC9H3Zr0HgQ	BAACAgUAAxkDAAIZUWYPm9NyAuDrIN1OhHlvEzhMgsWyAAJgDgACJuV4VIlM7-5cj_fyHgQ	f	f	72ccbfed-f87b-4905-a7a5-b0ac17c470d4	2024-03-30 05:12:01.335092+00	f	t	Douyin	\N	\N
黑熊熊s	https://v.douyin.com/iYjppv4V/	https://live.douyin.com/264407440026	\N	\N	f	t	c71b09aa-29fc-4c3e-9841-d1ded1775a2b	2024-04-05 06:35:11.18118+00	f	t	Douyin	\N	\N
三皮很黄	https://live.douyin.com/726227297450	https://live.douyin.com/726227297450	BQACAgEAAxkDAAIC6GYKlztVeUORhbJNcy4HxYysJ2oOAAI9BAACnapZRFjpsS_WF4R3HgQ	BAACAgEAAxkDAAIC6mYKl0aeSC911NobyoAuJBQeqPE3AAI-BAACnapZRJz3JotBumb7HgQ	f	f	3717d0b4-107b-4731-914d-7f588ef345a7	2024-03-30 05:12:01.335092+00	f	t	Douyin	\N	\N
梵高	https://live.douyin.com/560502539715	https://live.douyin.com/560502539715	BQACAgUAAxkDAAISw2YNz2x6kAXgzuP_x9KipfZ9JeGrAAK1DwACNL9oVIpcqDjvYJ8fHgQ	BAACAgUAAxkDAAISwGYNzx7h1mBL0URqRtkINlmGqCN8AAK0DwACNL9oVI8dAzavxiObHgQ	f	f	4893c002-51a4-4cb2-9e26-c65e81956bc9	2024-03-30 05:12:01.335092+00	f	t	Douyin	\N	\N
韩老板.	https://v.douyin.com/iF5SgMXq/	https://live.douyin.com/972802467046	BQACAgUAAxkDAAIZQ2YPktIvTYKXqo4N52qL7izf807UAAJZDgACJuV4VJqTBA_ymf3bHgQ	BAACAgUAAxkDAAIZQWYPkrAZd4YiDuj5BKUK8OrldtBDAAJYDgACJuV4VHyrNuZt6Jk9HgQ	f	f	2c2ce248-5222-4918-8bc3-44684f3c3bbe	2024-03-30 05:12:01.335092+00	f	t	Douyin	\N	\N
NoLongerExisted	https://www.tiktok.com/t/ZSFaMRFQ4/	https://www.tiktok.com/t/ZSFaMRFQ4/	\N	\N	f	f	43d9dd00-e3ed-4260-9c29-8e2a9e772c85	2024-03-30 05:12:01.335092+00	f	t	TikTok	\N	\N
CardMastersTikShop	https://www.tiktok.com/t/ZTL2HrQYr/	https://www.tiktok.com/@loluzq/live	BQACAgUAAxkDAAIZPmYPjrFLMI95UE2pcqt16RQNUNOMAAJVDgACJuV4VE524DoH5m38HgQ	BAACAgUAAxkDAAIZPGYPjclWxRfTekzafvyl3SMK1RnxAAJUDgACJuV4VJSXntANnppxHgQ	f	f	a244c6fe-7d8b-4825-864e-bae9a0404399	2024-03-30 05:12:01.335092+00	f	t	TikTok	\N	\N
Rcwrld.4l	https://www.tiktok.com/t/ZTLjesyvV/	https://www.tiktok.com/@nezzyylive/live	\N	\N	f	f	2e57bd19-7f1a-445d-ae3b-72499af12028	2024-03-30 05:12:01.335092+00	f	t	TikTok	\N	\N
董丽娜	https://v.douyin.com/iFco3tV6/	https://live.douyin.com/347771021033	\N	\N	f	f	697ed9dd-28ab-4240-8ea3-f1b491bf8868	2024-03-30 05:12:01.335092+00	f	t	Douyin	\N	\N
UZi	https://live.douyin.com/53188700123	https://live.douyin.com/53188700123	\N	\N	f	f	02ddae6d-5277-4524-8818-7af2cdfebbb1	2024-03-30 05:12:01.335092+00	f	t	Douyin	\N	\N
Pro Gamer	https://www.tiktok.com/t/ZSF9hXuYv/	https://www.tiktok.com/@pro_gamerz_/live	\N	\N	f	f	c986fd33-e044-4727-bc23-480254c78f5e	2024-03-30 05:12:01.335092+00	f	t	TikTok	\N	\N
碎穿碎舒服	https://v.douyin.com/iY1vuAST/	https://live.douyin.com/44665723829	BQACAgUAAxkDAAITH2YN-vhKObTvYjcBhhdSso9A1hcsAAI6DgACJuVoVD663A3R_GNLHgQ	BAACAgUAAxkDAAITG2YN-qbY3JSB_DuC43-FDrsCdMerAAI4DgACJuVoVEa5lDtXXgUKHgQ	f	f	c22c774b-cb4f-4052-80fe-ae5280056a1c	2024-03-31 00:32:20.115406+00	f	t	Douyin	\N	\N
央视新闻	https://v.douyin.com/iYNj78r6/	https://live.douyin.com/282773369501	BQACAgUAAxkDAAIQyGYM2eIHFjRggIbqmEVdxTIF0j7zAAIxDwACNL9gVKs4bqZCB_-1HgQ	\N	f	f	1ebb2c3b-7e41-4bdb-b090-16424ce4d113	2024-04-03 04:12:24.19194+00	f	t	Douyin	\N	\N
Lucky crystal us	https://www.tiktok.com/t/ZTL6g5pn1/	https://www.tiktok.com/@luckycrystalus/live	BQACAgUAAxkDAAIZemYPz_eW5Z2J9x3nucJVY3GkklEJAALhDgACJuV4VFRiZFAEeiNdHgQ	BAACAgUAAxkDAAIZeGYPz5rMb5Xu4IrRbNYdn4g9ROPHAALdDgACJuV4VEBizTj_UI2rHgQ	f	t	d5914ab7-d323-4969-b990-d5a6179d3353	2024-03-30 05:12:01.335092+00	f	t	TikTok	\N	\N
RAY	https://www.tiktok.com/t/ZSFa2SvYn/	https://www.tiktok.com/@ray_ray_lucky/live	BQACAgUAAxkDAAIYTGYOzKoYcuTno90NkvcYVYVNgfRCAAL6EQACJuVwVLU_ITyjwwABjR4E	BAACAgUAAxkDAAIWWGYOXmR5L2xlrCBUwMRun8ZziEX0AALmDwACJuVwVII44eGq6iLWHgQ	f	f	2e7d195b-20d8-4fcb-85cd-b8ea0d16e5f8	2024-03-30 05:12:01.335092+00	f	t	TikTok	\N	\N
Roses to Fly 🐱	https://vt.tiktok.com/ZSFpkmNwK/	https://www.tiktok.com/@rosesto.fly/live	\N	\N	f	f	d33e4dd8-709d-491c-b586-21d0c06266aa	2024-04-09 22:45:06.423212+00	f	t	TikTok	\N	\N
muna	https://www.tiktok.com/t/ZPRTtjHXh/	https://www.tiktok.com/@munabesabe/live	\N	\N	f	f	b6382cee-81c5-4ea2-aef7-ae644862f751	2024-04-09 20:04:24.746345+00	f	t	TikTok	\N	\N
一坨小肥肉	https://v.douyin.com/iFwj8TX9/	https://live.douyin.com/639321394025	BQACAgUAAxkDAAIRgmYNWgABrZaqhrXsYMdos7PLPb6Y_gAC-w4AAjS_aFSmMWKYXHqZiB4E	BAACAgUAAxkDAAIRhGYNWg8NkCvqYxdn_kvuPG49ZebBAAL8DgACNL9oVCSPHr5VOynUHgQ	f	f	35075411-3383-41c2-bc5e-5212d3621f2b	2024-03-30 05:12:01.335092+00	f	t	Douyin	\N	\N
抖抖在纽约	https://v.douyin.com/iYDThceB/	https://live.douyin.com/786474560305	\N	\N	f	f	5838b264-3e76-4030-8220-f86604699f25	2024-04-09 22:36:11.316331+00	f	t	Douyin	\N	\N
小米汽车	https://v.douyin.com/iYdVcvnF/	https://live.douyin.com/339059878593	BQACAgUAAxkDAAIYbGYO0GznVRKjAzOG-RajvvrOgH6MAAIYEgACJuVwVE9QossggAaDHgQ	BAACAgUAAxkDAAIYVmYOzoopJI5SYGGKDMUJcwLHoxZpAAIIEgACJuVwVFB7b4j0mV4qHgQ	t	f	8565fac8-3774-40b7-a9dc-c88e484777d1	2024-04-01 06:59:08.510629+00	f	t	Douyin	\N	\N
外教英语天天学	https://v.douyin.com/iF4MWLub/	https://live.douyin.com/831651812240	11234654561	\N	f	f	49c89d7b-868f-4ce8-a7d3-edd7f70af158	2024-03-30 05:12:01.335092+00	f	t	Douyin	\N	\N
エムドッグス/動物プロダクション	https://www.tiktok.com/t/ZSF5ow6Q6/	https://www.tiktok.com/@mdogs58/live	BQACAgUAAxkDAAIZZmYPq3wwjiSTWewvlie6PSelyPSrAAKXDgACJuV4VDW0gdJnM1l9HgQ	BAACAgUAAxkDAAIZaGYPq39qPnNE9YMcKtVwVKCW2KyFAAKYDgACJuV4VGgjhVyHRXyVHgQ	f	f	ea046534-f40b-47ff-9bc8-874161a220e9	2024-03-30 05:12:01.335092+00	f	t	TikTok	\N	\N
夏冰	https://v.douyin.com/iYh9YeJ1/	https://live.douyin.com/776235052386	\N	\N	f	f	07327675-2b5c-4443-b065-940f0f699fa8	2024-04-07 02:36:41.078374+00	f	t	Douyin	\N	\N
萌新	https://v.douyin.com/iYFDLH6V/	https://live.douyin.com/17215555443	BQACAgUAAxkDAAIYUGYOzf5itEXy-rvLA4KRVHgYGMUqAAIEEgACJuVwVECqzQW5sK5WHgQ	BAACAgUAAxkDAAIR12YNc_H8oR2cmD5c56gTX3pmd0IRAAIvDwACNL9oVG0gUfk_d_WRHgQ	t	f	ad9bf83a-4fdb-4929-8d8b-e297e075ad47	2024-04-03 14:58:13.945495+00	f	t	Douyin	\N	\N
彪呗	https://v.douyin.com/iYkMHoDn/	https://live.douyin.com/79544744166	\N	\N	f	f	02d84481-987e-4fbf-aa21-0023cccef8d2	2024-04-07 09:35:38.959981+00	f	t	Douyin	\N	\N
豆沙包是两只喵	https://v.douyin.com/iYjsDHwv/	https://live.douyin.com/990395723623	\N	\N	f	t	bf7b83cc-678f-46a9-9efa-e57ece9e1dd6	2024-04-05 06:37:48.138936+00	f	t	Douyin	\N	\N
正装憨憨壮熊	https://v.douyin.com/iYrE3htW/	https://live.douyin.com/711854233246	\N	\N	f	f	19845e2e-271b-49ab-880e-c0222f3f0ba3	2024-04-06 16:01:16.474777+00	f	t	Douyin	\N	\N
LowKeyr6	https://www.tiktok.com/t/ZPRTtmX2T/	https://www.tiktok.com/@lowkeyr6/live	\N	\N	f	f	2ef7ca45-0eeb-45c9-98fe-b3f96bf46603	2024-04-09 20:05:36.761925+00	f	t	TikTok	\N	\N
潮小布男士内衣	https://v.douyin.com/iYe64tcy/	https://live.douyin.com/925795002547	\N	\N	f	f	eeb07860-971f-467f-af1c-75218b575831	2024-03-31 12:04:06.64549+00	f	t	Douyin	\N	\N
一个爷们	https://v.douyin.com/iYDusERt/	https://live.douyin.com/201970030173	\N	\N	t	f	fe06d68b-d329-48c7-9df2-ca4c2aa01854	2024-04-09 14:28:33.906477+00	f	t	Douyin	\N	\N
牧牧牧牧牧牧🎸	https://v.douyin.com/iYhHVYRA/	https://live.douyin.com/592211262404	\N	\N	f	f	bb46d500-8685-4f5e-8523-0f1f8594a05d	2024-04-07 02:35:36.876713+00	f	t	Douyin	\N	\N
老板.	https://v.douyin.com/iYDWvMsA/	https://live.douyin.com/1322251165	\N	\N	t	f	d603d2c1-bce0-4e6e-b00d-9287686420da	2024-04-09 20:41:14.596038+00	f	t	Douyin	\N	\N
牡丹江蓝天救援队	https://live.douyin.com/598818727100	https://live.douyin.com/598818727100	BQACAgUAAxkDAAIPOmYL_p6v4C9UYMBa4o89M-Hr-jQEAAJhDQACqZNgVKG7wcQxHFnvHgQ	BAACAgEAAxkDAAIC1WYKfKmEh52stBxFXtFYykcVIybfAAIkBAACnapZRGyTqSIh7cHbHgQ	f	f	bc5edb3f-e3ce-4989-9ec0-d90729cc1ada	2024-03-30 05:12:01.335092+00	f	t	Douyin	\N	\N
林沐泽	https://v.douyin.com/iFoHaHjD/	https://live.douyin.com/264472825894	BQACAgUAAxkDAAIWBmYOUKAT3SBy_1fmYvtzNTongNKQAAKiDwACJuVwVAr3zp7NXTUuHgQ	BAACAgUAAxkDAAIWAAFmDk92qVv_HMguvrQLlidQLmjY8AACkg8AAiblcFQqnV8oluWxSx4E	f	f	fb5f1e72-e015-47ae-9447-651dab5078cf	2024-03-30 08:38:37.868654+00	f	t	Douyin	\N	\N
杨镔	https://v.douyin.com/iFXmVarH/	https://live.douyin.com/53275020954	BQACAgUAAxkDAAIZR2YPl9J7tNZu-rD9PT502moCTnvaAAJdDgACJuV4VP-zFGxX_dL5HgQ	BAACAgUAAxkDAAIZRWYPl5lz03ietA3EI2DnHsb4gWcNAAJcDgACJuV4VNmRw9hzsAXFHgQ	f	f	b959d168-3ad0-49dc-bbcf-52f75839cc21	2024-03-30 05:12:01.335092+00	f	t	Douyin	\N	\N
野火君	https://live.douyin.com/403746213918	https://live.douyin.com/403746213918	\N	\N	f	f	600e05a2-9e6d-4aaa-8e83-6950703d7dbf	2024-03-30 05:12:01.335092+00	f	t	Douyin	\N	\N
图图、	https://live.douyin.com/181114213532	https://live.douyin.com/181114213532	\N	\N	f	f	7b4487af-efaf-4330-beb7-a0640041faa3	2024-03-30 05:12:01.335092+00	f	t	Douyin	\N	\N
大虎彦	https://v.douyin.com/iFq1jV5y/	https://live.douyin.com/375698733972	BQACAgUAAxkDAAIReGYNWUklK_8B--6tBJoTAiOOJ67MAALzDgACNL9oVJmeFI-rKBIOHgQ	BAACAgUAAxkDAAIRdmYNWTU6awTYu4qxeWxB5GbOhaXwAALxDgACNL9oVG4ZQpKgdI0gHgQ	f	f	e96bad1e-c505-4d4a-b720-c6f34be0e235	2024-03-30 05:12:01.335092+00	f	t	Douyin	\N	\N
马应龙肛肠医生吕少海	https://live.douyin.com/501613607520	https://live.douyin.com/501613607520	\N	*********	f	f	0886017b-2b07-4b16-b250-b869f5b8d384	2024-03-30 05:12:01.335092+00	f	t	Douyin	\N	\N
鳴	https://live.douyin.com/475396724771	https://live.douyin.com/475396724771	BQACAgUAAxkDAAIZfmYP1KmwIvKgqLLHDELcGGPQF89OAALnDgACJuV4VNMpNMZkNdWMHgQ	BAACAgUAAxkDAAIZfGYP1IEtcnbPKNfsKSXMPp8-hbjCAALmDgACJuV4VK1Hq45_OS4_HgQ	f	f	917bdbff-18c3-464e-8841-e6af31ba7c4e	2024-03-30 05:12:01.335092+00	f	t	Douyin	\N	\N
咕噜锤	https://v.douyin.com/iYjnfeTR/	https://live.douyin.com/43014502447	BQACAgUAAxkDAAIZYGYPqj8iv6BpmWOrDYvg6Zje8R66AAKSDgACJuV4VBhEQgiSnkHAHgQ	BAACAgUAAxkDAAIZXWYPqjtj2xMQ-qCVceLdEgjUkKyiAAKRDgACJuV4VLFHx6p6YeJGHgQ	f	f	d42bce41-6767-4716-b939-432efd3279f1	2024-04-05 07:09:48.316753+00	f	t	Douyin	\N	\N
刘冠奇	https://v.douyin.com/iY8LjW3H/	https://live.douyin.com/747828148609	BQACAgUAAxkDAAITVGYODRwlAAFp5v09PAutxpsapxGimAAC3A0AAiblcFQbUuTEgWUqPB4E	BAACAgUAAxkDAAITSmYODGTU8i0uEPC21ESzZk-UnuAcAALXDQACJuVwVB3iC7ClZ8poHgQ	f	f	f9af1eb9-4622-4983-94c2-ebea1db19262	2024-04-02 01:39:51.897557+00	f	t	Douyin	\N	\N
舒克❤️	https://v.douyin.com/iY6ounxr/	https://live.douyin.com/986724842046	\N	\N	t	f	16024921-ad59-4a32-9068-a5b65bb8db2e	2024-04-05 19:20:21.478452+00	f	t	Douyin	\N	\N
大佑	https://live.douyin.com/157436942812	https://live.douyin.com/157436942812	BQACAgUAAxkDAAIZbmYPtOcufL0b5dev8d-Uf0-ntdC-AAKyDgACJuV4VJDWkbSZOXzPHgQ	BAACAgUAAxkDAAIZcGYPtPRo2X0hFwOfn0YxliZtK-9bAAKzDgACJuV4VJckFbecvxp6HgQ	t	f	285f5d24-e8c5-4f8b-90a0-56d12abd2426	2024-03-30 05:12:01.335092+00	f	t	Douyin	\N	\N
大象新闻	https://v.douyin.com/iYNjNxpg/	https://live.douyin.com/238099515779	BQACAgUAAxkDAAIYcmYO0UpHGh9EcrHbF5Yq5AMoGOlvAAIbEgACJuVwVPtC61Z0MVD7HgQ	BAACAgUAAxkDAAIYE2YOoKX89Y8x8uWYz2Hh4EcZJPmOAAKQEQACJuVwVCbcE72LbjSNHgQ	f	f	6ff7b205-ae36-420e-b185-d3e5359a77ba	2024-04-03 04:10:59.455105+00	f	t	Douyin	\N	\N
Ryan Sundry	https://vt.tiktok.com/ZSF92amae/	https://www.tiktok.com/@imsundry/live	BQACAgUAAxkDAAIQIWYMOJrEnjLN-0RJKfQH7c3Ap6k6AAIwDgACNL9gVIdUik7A1qjBHgQ	BAACAgUAAxkDAAIQH2YMOIXbDXmGwwKA6xYSsgEGNrYLAAIvDgACNL9gVF9yX2pVSSrzHgQ	t	f	7cdc7352-a80c-4918-9492-9dd58a54fef4	2024-03-30 05:12:01.335092+00	f	t	TikTok	\N	\N
𝓚/琥珀/𝓚	https://www.tiktok.com/t/ZTL2Hfvas/	https://www.tiktok.com/@washoiwashoi001/live	BQACAgUAAxkDAAIZFWYPTPiLPE_Ol5rsrj4kd3CH-0JRAALWDQACJuV4VNFJ5eWrAAGzHR4E	BAACAgUAAxkDAAIZBGYPIa8939aJdwvB0DeeFoJ5ZK6hAAK9DQACJuV4VF0Rk0pW2V96HgQ	f	f	1b40d49c-2c68-4dac-8ffe-cac815966522	2024-03-30 05:12:01.335092+00	f	t	TikTok	\N	\N
西双版纳阿亮（泰国直播）	https://v.douyin.com/iYd9xvAU/	https://live.douyin.com/714802981830	BQACAgUAAxkDAAIW3GYOdXimUC5eXYheo3uZVkDd2RJOAAKFEAACJuVwVBdIetxABFkbHgQ	BAACAgUAAxkDAAIXN2YOeynFTKIGrGAEWM1OhL0JJri3AALJEAACJuVwVLqh0l-f5Ml8HgQ	f	f	8296ed73-7f66-44a6-9de4-670fd9fe4fc6	2024-04-01 05:49:02.312484+00	f	t	Douyin	\N	\N
Just Jake	https://www.tiktok.com/t/ZTL6bWFy8/	https://www.tiktok.com/@jakethedoggames/live	BQACAgUAAxkDAAIUQmYON_jgWIh3IZE1YAee47jPNfk9AAJ9DgACJuVwVB4zxh-i6Dh9HgQ	BAACAgUAAxkDAAIUPGYONyfnmOGop-r2wH7GWIuArQABgAACeA4AAiblcFSGsM7aqPKxHR4E	f	t	66ee59e9-fea8-4a7c-9843-96237c04c8bb	2024-03-30 05:12:01.335092+00	t	t	TikTok	\N	\N
标准日本语	https://v.douyin.com/iFcutEVg/	https://live.douyin.com/314189026275	BQACAgUAAxkDAAIRnGYNYHsshWGzdOwL00fQe5oXFS60AAINDwACNL9oVF_EkZpFbqNzHgQ	BAACAgUAAxkDAAIRmmYNYHhp8-EKeJ7pldkv3PlLZrF5AAIMDwACNL9oVGf_QAJSyhyaHgQ	f	f	cde01b4a-edc0-4f7f-8e7e-f89cf7669f89	2024-03-30 05:12:01.335092+00	f	t	Douyin	\N	\N
阿勝l	https://v.douyin.com/iFXawCYw/	https://live.douyin.com/10060813163	\N	BAACAgUAAxkDAAIZiGYQFuw1hc1JdMVTd_Qv9SAja0TxAAI4DQACNpyBVBpYKyBm3dSMHgQ	t	t	1c0ec025-d2ed-4e5e-ad14-7322f1395268	2024-03-30 05:12:01.335092+00	f	t	Douyin	\N	\N
娄艺潇	https://v.douyin.com/iY8b5jbS/	https://live.douyin.com/17959613797	BQACAgUAAxkDAAIYPmYOwiC7cyqKIO1UysfN5o1eFr2fAALeEQACJuVwVDJLnuL2yuzWHgQ	BAACAgUAAxkDAAIYQGYOwkPmfGOK1miSBWp4_PxiN0cEAALfEQACJuVwVFfrxjC6Z0n2HgQ	f	t	31bc73b3-eed8-4899-83c1-a061a3fd93a5	2024-04-02 07:38:27.0186+00	f	t	Douyin	\N	\N
承煜bear	https://v.douyin.com/iFQ7Yj9M/	https://live.douyin.com/319198270830	\N	\N	f	f	b0302e31-bcc7-4d42-aab7-482a75f06bf1	2024-03-30 05:12:01.335092+00	f	t	Douyin	\N	\N
山西凯尔特锻造有限公司	https://v.douyin.com/iY8jaTDs/	https://live.douyin.com/546852452446	BQACAgUAAxkDAAIZWGYPqK2olk3re8QaKpnHLQlnvrqrAAKFDgACJuV4VHHUZOE_6TN8HgQ	BAACAgUAAxkDAAIZWmYPqLM-Ec1VDxLz6bGUUDIU6d9-AAKGDgACJuV4VImE4Ap9HE_iHgQ	f	t	813dc207-9080-4678-81bc-98e3e23fa52f	2024-04-02 02:46:40.257318+00	f	t	Douyin	\N	\N
李察德咨询	https://v.douyin.com/iFwddPnX/	https://live.douyin.com/614394277218	\N	\N	f	f	79b19bbd-13ff-4819-bc2d-aab2bd8c57ce	2024-03-30 05:12:01.335092+00	f	t	Douyin	\N	\N
floating	https://v.douyin.com/iFP17sLN/	https://live.douyin.com/932934719424	\N	\N	f	f	87c543a2-8e19-4e64-9114-5acf94a8bf39	2024-03-30 05:12:01.335092+00	f	t	Douyin	\N	\N
むーちょ🥤	https://www.tiktok.com/t/ZSFucpYnq/	https://www.tiktok.com/@mucho_824/live	BQACAgUAAxkDAAIRYmYNUcPHfG4EAVYieluJkQABRPeg3QAC3w4AAjS_aFTjj_EFGssSWh4E	BAACAgUAAxkDAAIRYGYNUZ-zdrkuUV_yaN-Ht1zO0yqJAALeDgACNL9oVI-UhHOWwz8pHgQ	f	f	acf2a797-7b0a-43d6-9561-5d7be7654596	2024-03-30 05:12:01.335092+00	f	t	TikTok	\N	\N
白羊座的柒柒	https://v.douyin.com/iFyfXsGr/	https://live.douyin.com/746066284916	\N	\N	t	t	16db8578-67d3-489a-b297-cecd909f30fd	2024-03-30 05:12:01.335092+00	f	t	Douyin	\N	\N
花花的塞尔达之旅	https://v.douyin.com/iFuEW2rg/	https://live.douyin.com/433878083515	BQACAgUAAxkDAAIP1GYMJHB1lejLjlQ1OcscXQsTa80AAwMOAAI0v2BUK1TMuDkQYUseBA	BAACAgUAAxkDAAIP1mYMJHgcq8CIeJDIXY-Rtmohv6TZAAIEDgACNL9gVPGmTS23nTyyHgQ	t	f	3f66dd84-4da0-456d-b8cd-d75d709eb1e0	2024-03-30 05:12:01.335092+00	f	t	Douyin	\N	\N
牛牛㍿	https://v.douyin.com/iFHdWeyL/	https://live.douyin.com/201604547382	BQACAgUAAxkDAAIPgWYMEaReL3uP1sWEp7KTnQu8FbcsAALHDQACNL9gVENzf71n-RYmHgQ	BAACAgUAAxkDAAIPf2YMEZep0QpfSjqTjaF34yw4qPdtAALGDQACNL9gVJo61F4llEB9HgQ	f	f	f3ce01d7-ba4f-41ce-b2ea-196b59e2b66d	2024-03-30 05:12:01.335092+00	f	t	Douyin	\N	\N
火鸡	https://live.douyin.com/822386640856	https://live.douyin.com/822386640856	\N	\N	f	f	a3a84a63-ea81-4144-8db1-f5973a9a3335	2024-03-30 05:12:01.335092+00	f	t	Douyin	\N	\N
嘎图🌈阿庄SPA	https://v.douyin.com/iYMrf5rd/	https://live.douyin.com/686909847350	\N	\N	f	f	360df5fc-a771-4cec-b702-f4dab11b8259	2024-04-06 00:38:37.093473+00	f	t	Douyin	\N	\N
姜汤雪梨🍐	https://v.douyin.com/iYFDe8Wo/	https://live.douyin.com/496143870065	BQACAgUAAxkDAAIR9GYNfSkF0w0znN7JTwMdblhv2DqxAAJCDwACNL9oVG_8KP6I6kBOHgQ	BAACAgUAAxkDAAIR8mYNfSiMk1ih250jzqaQVrtpk0IaAAJBDwACNL9oVGNOtsR65_g7HgQ	f	f	d195603b-08a6-4c9c-9e5d-b3570aae693b	2024-04-03 14:55:29.064841+00	f	t	Douyin	\N	\N
九欧拆卡	https://live.douyin.com/928017290959	https://live.douyin.com/928017290959	BQACAgUAAxkDAAIS6WYN32jfVrkmvcKl8P1OTfglC5T4AAIRDgACJuVoVA8bIX5MGpiNHgQ	BAACAgUAAxkDAAIS62YN38Jr0-FyuQIXgPpFXtSEHcT6AAISDgACJuVoVDHyZ0pi850YHgQ	f	t	57be941b-12bf-44ef-a50e-92ba03a5298e	2024-03-30 05:12:01.335092+00	f	t	Douyin	\N	\N
小老表（豪旭传媒）	https://v.douyin.com/iF3EFPfv/	https://live.douyin.com/507107985769	BQACAgEAAxkDAAM1ZgWVuhURUjLXC_W4L4xAJ8ggcBUAAqoEAAIgaylEN9dcsQH2q1keBA	\N	f	f	34b3e34e-f6c2-4ba4-b3f4-abb260fa5419	2024-03-30 05:12:01.335092+00	f	t	Douyin	\N	\N
这是小9	https://live.douyin.com/530634218576	https://live.douyin.com/530634218576	\N	\N	f	f	cd93ec2d-dcd1-4fe7-8fa2-2b7553a4fb2e	2024-03-30 05:12:01.335092+00	f	t	Douyin	\N	\N
李老板双翻娱乐传媒	https://v.douyin.com/iY19NDLh/	https://live.douyin.com/927022003674	BQACAgUAAxkDAAISu2YNzinFRDzv0baB96qXGUYKhMY0AAKyDwACNL9oVKXn6XAOpQbHHgQ	BAACAgUAAxkDAAISvWYNzmDux06dwnSNN1aOI5-Bbl-5AAKzDwACNL9oVOEku7irHoMsHgQ	f	t	faec3128-5192-42fa-a68e-7efe9f46e5c1	2024-03-30 18:12:10.676131+00	f	t	Douyin	\N	\N
是小武子呀.🌈	https://v.douyin.com/iYFDP48y/	https://live.douyin.com/710662581889	BQACAgUAAxkDAAISMGYNjLv1kDbSNLoEjbDEzwOXV-RpAAJpDwACNL9oVBNGsA11P-DEHgQ	BAACAgUAAxkDAAISMmYNjMOJJdv05JlnTi57zu_VB2BBAAJqDwACNL9oVD9y3Q4Y8V_KHgQ	f	f	44e4613e-8736-4419-8aa1-1572488670ab	2024-04-03 15:00:59.560808+00	f	t	Douyin	\N	\N
季枫reborn	https://v.douyin.com/iFV59U3U/	https://live.douyin.com/263402337733	*********	\N	f	f	cc946dd9-4ed4-4534-bc81-2ae4efceb987	2024-03-30 05:12:01.335092+00	f	t	Douyin	\N	\N
青衣弹幕游戏	https://v.douyin.com/iF3uoujh/	https://live.douyin.com/466219631344	BQACAgUAAxkDAAIX7mYOkr16kKB8a61iiKqEE0Lpfk-xAAJiEQACJuVwVJLwNHN29k6CHgQ	BAACAgUAAxkDAAIYRGYOymC80JapfimwgbGFv9CPVtoNAALrEQACJuVwVLbIs28TYqCsHgQ	t	f	f45abff4-3f55-4948-97dc-6e60bed2d928	2024-03-30 05:12:01.335092+00	t	f	Douyin	\N	\N
游吟诗人Kass	https://v.douyin.com/iYFAtR2w/	https://live.douyin.com/42996501765	BQACAgUAAxkDAAIR3WYNdkZ2grRvZ0vn68xdjpS5-Fk-AAIzDwACNL9oVIjoOUQONsnWHgQ	BAACAgUAAxkDAAIR22YNdj6KakLadxL4jW-fMYgwMh1yAAIyDwACNL9oVEekQx9Esx7DHgQ	f	f	fa9bff02-63a3-40d0-9870-61195e335021	2024-04-03 14:55:36.477411+00	f	t	Douyin	\N	\N
三字哥哥	https://v.douyin.com/iFwL6EDG/	https://live.douyin.com/587147456524	\N	\N	f	f	098f6c47-d34c-4e60-86c1-dc4bce6a83b6	2024-03-30 05:12:01.335092+00	f	t	Douyin	\N	\N
老王 脑阔疼🎱(收徒）	https://v.douyin.com/iFoo1Y4m/	https://live.douyin.com/765140099951	BQACAgEAAxkDAAIC-GYKqkcR8JtJy170qbUupMHs4f28AAJJBAACnapZRNfwoHix3ByEHgQ	BAACAgEAAxkDAAIC-mYKrAECzW61KRyivdYX_KmibanhAAJNBAACnapZRB7ZrHc3mkJXHgQ	f	f	0f2e6c59-aa23-4f72-9795-65d75f4ba10b	2024-03-30 11:55:58.713263+00	f	t	Douyin	\N	\N
Princessaa.shy	https://www.tiktok.com/t/ZPRT5TKq3/	https://www.tiktok.com/@princessaa.shy/live	BQACAgUAAxkDAAIONWYLf-zTkQlexcZEkB24oHwolLE3AAIPDgACqZNYVEN0PjhcE52XHgQ	BAACAgUAAxkDAAION2YLgHmbLAwhxtxqW_t7T6G_MVlHAAIQDgACqZNYVGkzU9nYpbgwHgQ	f	f	267add55-1bb4-4549-af66-0e9810252cef	2024-03-30 05:43:47.153175+00	t	t	TikTok	\N	\N
củi khô truonggiao	https://vt.tiktok.com/ZSF9jh3ku/	https://www.tiktok.com/@chuongdeoo/live	BQACAgUAAxkDAAIQh2YMsP-9McePOURYKKerOnxZ-kluAAKrDgACNL9gVLmDvFjwNufVHgQ	\N	f	f	74c3b354-8db0-4d7b-a07d-fcb97d3c5d56	2024-03-30 05:12:01.335092+00	f	t	TikTok	\N	\N
🌻ひまわり🌻太陽花🌻	https://www.tiktok.com/t/ZSFaYtcko/	https://www.tiktok.com/@taiyouhana1117/live	BQACAgUAAxkDAAIRzWYNcWJ16Gd7iUmJAmb-d-LH9MidAAIqDwACNL9oVMY5zs_1UVrWHgQ	BAACAgUAAxkDAAIYPGYOwZy2sk6xWPM1VUAWjf4kk_kfAALYEQACJuVwVCTmif1o-cSaHgQ	f	t	82bba014-6377-4fa3-a378-81c493bda133	2024-03-30 05:12:01.335092+00	f	t	TikTok	\N	\N
ava grace	https://www.tiktok.com/t/ZPRTaFkp8/	https://www.tiktok.com/@avaelligott/live	\N	\N	f	f	76fb7ad6-7e28-4724-b6d8-6b8c3a0dadd9	2024-03-30 05:12:56.316499+00	t	t	TikTok	\N	\N
就很甜	https://v.douyin.com/iFPyySLm/	https://live.douyin.com/480152283921	BQACAgUAAxkDAAIRO2YNLHKUVGEXK7OgtohKFDH02dYcAAKnDgACNL9oVD0X_hCTCzqrHgQ	BAACAgUAAxkDAAIYQmYOw2q_5GAx1_IF0bQ9AzIcsA99AALhEQACJuVwVOAn9UaikWzCHgQ	t	f	fbe3db70-2aa5-4a6e-997f-886ffd531b32	2024-03-30 05:12:01.335092+00	f	t	Douyin	\N	\N
于泽旭	https://v.douyin.com/iYMF9h4b/	https://live.douyin.com/223278392842	\N	\N	t	f	183c1c6b-6979-44c0-8b85-f571cdb203dc	2024-04-05 22:55:15.202127+00	f	t	Douyin	\N	\N
四川鱼庄塘下店、老板娘	https://live.douyin.com/269186479309	https://live.douyin.com/269186479309	BQACAgUAAxkDAAIRS2YNQOTwHNUKlfiCls-GRmTF3yAiAALBDgACNL9oVFgtqeNuC1RVHgQ	BAACAgUAAxkDAAIRT2YNQbikxgu34XqPEUM-r0ppeBp0AALGDgACNL9oVJNypyphUBf4HgQ	f	f	0d8b5c50-dc0b-4191-bb51-0909e5b1b999	2024-03-30 05:12:01.335092+00	f	t	Douyin	\N	\N
沐南熙	https://live.douyin.com/48745530292	https://live.douyin.com/48745530292	BQACAgUAAxkDAAIUlWYOP4JgFkg9MD2HaXt10fqvOjMQAAKzDgACJuVwVH0yeiaLjjrnHgQ	BAACAgUAAxkDAAIUk2YOP3oh_3O6kx49keovRsiRJjLOAAKyDgACJuVwVO1WqRQ4z3efHgQ	f	f	434b41e3-25ef-4f73-af49-4217b16701c1	2024-03-30 05:12:01.335092+00	f	t	Douyin	\N	\N
金刚	https://live.douyin.com/508425285304	https://live.douyin.com/508425285304	BQACAgUAAxkDAAIZbGYPrIVuCE0AAbVYTv3fP3GRUSnNTwACmg4AAibleFTbUMWHlUDgUB4E	BAACAgUAAxkDAAIZamYPrCYkkUDHzN-zK94r6oF60_fMAAKZDgACJuV4VIP24srIckH3HgQ	f	f	e4a7a75c-35d2-40a9-9c09-a35c14efe2b9	2024-03-30 05:12:01.335092+00	f	t	Douyin	\N	\N
jeffreyinnyc	https://www.tiktok.com/t/ZSFHXkm5b/	https://www.tiktok.com/@jeffreyinnyc/live	BQACAgUAAxkDAAIYlGYO-u9xRLOh8WHc7Y_gUz1WTNqvAAJHEgACJuVwVOkvN5UCUq_NHgQ	BAACAgUAAxkDAAIYkmYO-rdwfHAe5R6qIuYp_ck7mSx8AAJGEgACJuVwVFyGzcL95LR3HgQ	f	f	7ea18d14-0234-4720-a540-f78806228ea1	2024-03-30 05:12:01.335092+00	f	t	TikTok	\N	\N
小孙的摆摊日记	https://live.douyin.com/4177443406	https://live.douyin.com/4177443406	BQACAgEAAxkDAAIC42YKk9P0-sjiNoqe1B1ZXEx4SEpCAAI6BAACnapZRNA6xl7mwzadHgQ	BAACAgEAAxkDAAIC7GYKmj89DUzTRAHd3lhJGdTm6FB-AAI_BAACnapZRMqmfHNiLxeaHgQ	f	f	83372199-6bbb-46d4-b175-1024f2993333	2024-03-30 05:12:01.335092+00	f	t	Douyin	\N	\N
-肥猪猪的日常	https://v.douyin.com/iY1kVTvu/	https://live.douyin.com/776364896040	BQACAgUAAxkDAAIRtWYNaqE7YeALPDiO8kaLBCMXGPlMAAIgDwACNL9oVGmUEhfLh5TmHgQ	BAACAgUAAxkDAAIRs2YNapFVBp-TM4nshMbQ6TEAAWTRsgACHw8AAjS_aFRntY7Gst-u6h4E	f	f	c073e70b-b631-4af5-b6bb-0c79e021399a	2024-03-30 14:34:31.13067+00	f	t	Douyin	\N	\N
天总（超级闺蜜节狂野版）	https://live.douyin.com/735080947200	https://live.douyin.com/735080947200	BQACAgUAAxkDAAISRmYNlk359Oq3j1S0io22_7IjBR-gAAJ1DwACNL9oVF4zET0r2io3HgQ	BAACAgUAAxkDAAISRGYNlj9Ay17GUs8m7mFK10OAR1wBAAJ0DwACNL9oVOTs2pbuBXDlHgQ	f	f	bf1add38-6495-44bd-a2a1-c923620fd71b	2024-03-30 05:12:01.335092+00	f	t	Douyin	\N	\N
ちーもこチャンネル🐷🐽	https://vt.tiktok.com/ZSFVNfBDo/	https://www.tiktok.com/@ri_mokotyan/live	BQACAgUAAxkDAAIZOmYPhY_Y38lOMXx-NCx94lLFkrATAAJEDgACJuV4VKCio8HknInnHgQ	BAACAgUAAxkDAAIZOGYPhOvAZ_Zr1EecWnO6289yFAmxAAJCDgACJuV4VGgsST5Zo4DTHgQ	t	t	f4ee5407-9467-438d-b1e6-92b76bdf6663	2024-04-02 08:00:50.471788+00	t	t	TikTok	\N	\N
猛男推拿师🌈（健身版）	https://v.douyin.com/iF58x4YB/	https://live.douyin.com/655189503370	BQACAgUAAxkDAAIPKmYL6LsvYsQ7UJ9LG1-_29r__JJbAAI2DQACqZNgVCfPihWONivqHgQ	BAACAgUAAxkDAAIOEWYLZKVNbrp3EgWjt8RTu8CzM3wqAALBDQACqZNYVGq2ssb8d99BHgQ	f	f	cee5b45c-d5a9-4867-a86f-933513faf536	2024-03-30 05:12:01.335092+00	f	t	Douyin	\N	\N
BIGDONGDONG	https://live.douyin.com/514209091958	https://live.douyin.com/514209091958	BQACAgUAAxkDAAIRbGYNVztFNbho85rOLz6auqiO4zfyAALmDgACNL9oVCaJ9Crov1sCHgQ	BAACAgUAAxkDAAIRbmYNV287nUbsto5ZoQtASoKAagABfgAC5w4AAjS_aFQQnR0cHwv3ux4E	f	f	83a2c08f-5a2f-4608-83e9-f50578c8f23b	2024-03-30 05:12:01.335092+00	f	t	Douyin	\N	\N
厦门小程	https://v.douyin.com/iYdVPB9o/	https://live.douyin.com/284511790014	BQACAgUAAxkDAAITyGYOKLR9AtrU3kJA6XdqilWafeFFAAI1DgACJuVwVNBuIKuRKf4rHgQ	BAACAgUAAxkDAAIT0mYOKQFXJLcXyUaQAbI73AKcOo7NAAI6DgACJuVwVO0GT3Mk0J6jHgQ	f	f	447e5bef-c356-4569-95fa-9fd1ec4ca27c	2024-04-01 06:54:14.130963+00	f	t	Douyin	\N	\N
线上外教英语课堂	https://v.douyin.com/iFxLj39j/	https://live.douyin.com/135372758543	\N	\N	f	f	23955180-bbc8-4db8-b536-d8263ca98ddd	2024-03-30 05:12:01.335092+00	f	t	Douyin	\N	\N
張家漢	https://live.douyin.com/484381898905	https://live.douyin.com/484381898905	\N	\N	f	f	ec78e3aa-a791-464d-9ca7-dcf2339fa874	2024-03-30 05:12:01.335092+00	f	t	Douyin	\N	\N
佳晨下班了	https://v.douyin.com/iFPry8CH/	https://live.douyin.com/625594547942	BQACAgEAAxkDAAIBX2YIM6RopyAZ6PHgnOt-YqXQ0NToAAJLAwACvoFARKTSKgwzqTBwHgQ	BAACAgEAAxkDAAIBYWYINAo6hSm4zb232nXU2HPsSvRoAAJMAwACvoFARHwh8qUWhx9kHgQ	t	f	9b100d57-5193-4362-b1d5-6122092fbd33	2024-03-30 05:12:01.335092+00	f	t	Douyin	\N	\N
俏花仙园艺	https://v.douyin.com/iFEe9UXv/	https://live.douyin.com/939520323536	BQACAgUAAxkDAAIPqGYMFoArqPluJlF1pB8r3PGp4PeuAALZDQACNL9gVLBQXwmNoLVtHgQ	BAACAgUAAxkDAAIPqmYMFtpt5XinmnbYNjjp-xBmdyCmAALaDQACNL9gVI4ekppaFcXLHgQ	f	f	930f9f3d-22a2-491a-9be5-84af5aa0f98f	2024-03-30 05:12:01.335092+00	f	t	Douyin	\N	\N
新东方孟繁飞	https://v.douyin.com/iYRPwsLd/	https://live.douyin.com/453284208292	BQACAgUAAxkDAAIQe2YMpYOgT9TtXjQOiL33kYDJo8lbAAKNDgACNL9gVPpLlJKGUlvEHgQ	BAACAgUAAxkDAAIQeWYMpXfIk0b_geCjn_EnhVCteHBeAAKMDgACNL9gVP3_5cGVTgdYHgQ	f	f	7d135cd9-e3c7-4a09-a0c7-726662b97d97	2024-04-01 14:02:33.865872+00	f	t	Douyin	\N	\N
奇异果🐻	https://live.douyin.com/*********	https://live.douyin.com/*********	BQACAgUAAxkDAAIQOGYMUBDfl1vyaZe_kvi6TuHwmYILAAJADgACNL9gVPnIFDnMCfQ8HgQ	BAACAgUAAxkDAAIQNGYMT7y7JWeVpsAVZtccgWZa4N6NAAI-DgACNL9gVFNwtTaccfjYHgQ	t	f	7f4e8290-05b4-4184-84a4-ac857148e6cf	2024-03-30 05:12:01.335092+00	f	t	Douyin	\N	\N
阿昊🇨🇳	https://v.douyin.com/iFoGF58S/	https://live.douyin.com/664779722644	BQACAgUAAxkDAAIRQ2YNNGsl6J8la7DV5yqNtAgDhpXsAAK0DgACNL9oVFse9s5hXFcZHgQ	BAACAgUAAxkDAAIRQWYNM-wYjJdU3B1ZndTYatm_HexlAAKzDgACNL9oVLot5CgoXQd8HgQ	f	f	3043b292-44a5-4c7b-b536-78d93adb66d3	2024-03-30 10:20:06.705457+00	f	t	Douyin	\N	\N
Kristin 英语老师	https://v.douyin.com/iYN2vBmF/	https://live.douyin.com/532513924908	BQACAgUAAxkDAAIQvGYM2TP8Y-ibmmIFAatfhQYL4Q85AAIrDwACNL9gVI6t7iRHm8uRHgQ	\N	t	f	87df29c0-9e96-4ed0-9ab6-e8851247531e	2024-04-03 04:10:43.885992+00	f	t	Douyin	\N	\N
赋.南启	https://live.douyin.com/466448042671	https://live.douyin.com/466448042671	BQACAgUAAxkDAAISFmYNhjdgI6_1N5UphTR3R1IB_GxjAAJcDwACNL9oVHmjXlXggluOHgQ	BAACAgUAAxkDAAISGGYNhjpTvPtlsiFIqKDQhzcepWyDAAJdDwACNL9oVCv2IjZFVkDtHgQ	f	f	8fa3e757-f456-4e50-a437-5da0b7ead77a	2024-03-30 05:12:01.335092+00	f	t	Douyin	\N	\N
楚桥说美澳🇺🇸	https://v.douyin.com/iFyY7qe2/	https://live.douyin.com/395335698171	BQACAgUAAxkDAAIRjGYNXIqoJEhRpB5AtblkjNyDYKSfAAIDDwACNL9oVDR8pVp1WCu5HgQ	BAACAgUAAxkDAAIRhmYNXH97iXQzMGjAdK9K-Eh1Juy4AAMPAAI0v2hU6Ox23DXFXIweBA	f	f	fb2e05db-667f-41c2-916c-c6a7526cf52d	2024-03-30 05:12:01.335092+00	f	t	Douyin	\N	\N
余姚肉哥生活号-晴晴	https://v.douyin.com/iFTSw7EM/	https://live.douyin.com/880924552382	BQACAgUAAxkDAAIYCWYOnfp1SbJZNKbJKRHYUkh7bK4yAAKLEQACJuVwVFjAP9DUW4CsHgQ	BAACAgUAAxkDAAIYDGYOnudM4dpEQArNB8vaOKDnkriUAAKMEQACJuVwVFVvbFIR3sthHgQ	f	t	b6b272b6-0dc3-4740-8232-44fd254d235b	2024-03-30 05:12:01.335092+00	f	t	Douyin	\N	\N
薯条🇨🇳	https://v.douyin.com/iYFDmHjV/	https://live.douyin.com/116577118687	BQACAgUAAxkDAAIRx2YNcC9Vm3TTXwcJa0WjFR1xDOxWAAImDwACNL9oVM0rcZFYxlFxHgQ	BAACAgUAAxkDAAIRyWYNcDANeTby8slWVfcYEU5tO4hRAAInDwACNL9oVJGtR9XLW0AVHgQ	f	f	533cc65e-6477-45c8-b6a5-fb3eff8a7c13	2024-04-03 14:57:41.1661+00	f	t	Douyin	\N	\N
亢奋的猛男暴力推拿按摩	https://v.douyin.com/iFQsmhhc/	https://live.douyin.com/360081711045	\N	\N	f	f	26261c9d-bccb-484b-8a1e-63e130ed9f1e	2024-03-30 05:12:01.335092+00	f	t	Douyin	\N	\N
迪士尼在逃蒙牛	https://v.douyin.com/iFPSrb8y/	https://live.douyin.com/207438342040	\N	\N	f	f	cdb6d7f1-05f8-4be9-812d-75635a5f092d	2024-03-30 05:12:01.335092+00	f	t	Douyin	\N	\N
小鱼很奈斯	https://v.douyin.com/iY6bRg7a/	https://live.douyin.com/849857064637	\N	\N	f	f	3a16c588-5c51-4bd6-aa3f-37ec4e743646	2024-04-05 14:16:19.607631+00	f	t	Douyin	\N	\N
刀刀🔪	https://live.douyin.com/755333441418	https://live.douyin.com/755333441418	BQACAgUAAxkDAAIRnmYNYvkYVDreLUy-S79R9kHzsKTSAAIPDwACNL9oVMKFZgFnF-uxHgQ	BAACAgUAAxkDAAIRoGYNYxLH6DSgb4jpeAsxKLdZ353xAAIQDwACNL9oVHqTi9QPDQeSHgQ	f	f	e69cd3e6-3290-4a67-9a3a-94089b1033e5	2024-03-30 05:12:01.335092+00	f	t	Douyin	\N	\N
疯狂丸丸	https://v.douyin.com/iFcauuQ6/	https://live.douyin.com/82537845528	BQACAgUAAxkDAAIRIWYNFQMn8pdcYXC_w8ii_eKe7tYAA2sOAAI0v2hUAAF3XUSACIt5HgQ	BAACAgUAAxkDAAIRI2YNFRKqbOECSgOckaZGVR7srSg0AAJsDgACNL9oVHl8LryJOPqCHgQ	f	f	05bcfe37-59cd-40f0-8120-f068a141668d	2024-03-30 05:12:01.335092+00	f	t	Douyin	\N	\N
Amber2.0	https://v.douyin.com/iFPU2Ut3/	https://live.douyin.com/895074320899	BQACAgUAAxkDAAIP9WYMKtjRc3M2d9cTKSwsGqHsCcuAAAIUDgACNL9gVDwjd0hkMWWJHgQ	BAACAgUAAxkDAAIP92YMKt7wFOmKWN79ukB-2qivImVvAAIVDgACNL9gVCA86GnXdAxyHgQ	t	t	f28aed7e-aa85-4fba-b5e4-26716609b35f	2024-03-30 05:12:01.335092+00	f	t	Douyin	\N	\N
Nihongo_Yuki🇯🇵	https://vt.tiktok.com/ZSFVFcRnx/	https://www.tiktok.com/@yukijapan0623/live	BQACAgUAAxkDAAIZgGYP10WwAtWDtxhqF-0hQmOOCLI3AALuDgACJuV4VE6f1Ojz8s8YHgQ	BAACAgUAAxkDAAIZKGYPb2rvnHXHHRreFZRxuka0TZNqAAIbDgACJuV4VIbDp_LsXH9THgQ	f	f	5df72d61-271a-479c-a525-c3f7bac9a815	2024-04-02 08:56:52.957389+00	f	t	TikTok	\N	\N
你的阿宽	https://live.douyin.com/671239017144	https://live.douyin.com/671239017144	\N	\N	f	f	66fbcf87-02a5-4219-8c91-7eb04df72930	2024-03-30 05:12:01.335092+00	f	t	Douyin	\N	\N
闻风居士	https://v.douyin.com/iYFsJsXW/	https://live.douyin.com/134581135027	BQACAgUAAxkDAAIZCmYPKfzVqOixX_Ia36QruphnFQwVAALADQACJuV4VF_UhIej0eFQHgQ	BAACAgUAAxkDAAIZB2YPKdbfRMGm_RshiENJcjrVgcLIAAK_DQACJuV4VE951OfXO8byHgQ	f	f	fde40757-aaf5-4a67-ab7c-8872cd424e98	2024-04-03 22:47:12.356848+00	f	t	Douyin	\N	\N
郑伟	https://live.douyin.com/418304853299	https://live.douyin.com/418304853299	BQACAgUAAxkDAAIPC2YL2S5tdod93n4OcjfYpIKkAAFbDAACFw0AAqmTYFTGclZzO6shzh4E	BAACAgEAAxkDAAIBg2YIPLY6aQbHZyRsSOibQ6hMFyIvAAJeAwACvoFARCuTmbXU1iI0HgQ	f	t	e93633ae-06b0-4296-9122-5de17faacc2c	2024-03-30 05:12:01.335092+00	f	t	Douyin	\N	\N
阮	https://v.douyin.com/iFQp4aJL/	https://live.douyin.com/878773940075	\N	\N	f	f	25ddb3c1-ba75-4cc0-a1c2-ed76ff1082de	2024-03-30 05:12:01.335092+00	f	t	Douyin	\N	\N
李金铭	https://v.douyin.com/iFuegHtU/	https://live.douyin.com/28945346664	BQACAgUAAxkDAAIX0WYOiDcUb8a_iXoFZcNGqB5x-CqvAAJBEQACJuVwVPmP0l8hEsCoHgQ	BAACAgUAAxkDAAIX2WYOilHnFEFShvEZO7abicO6z17yAAJMEQACJuVwVMgvDzqH-OP9HgQ	f	f	f0138a5d-f632-47c8-85e4-1d82988dbebf	2024-03-30 05:12:01.335092+00	f	t	Douyin	\N	\N
萱呀♥️	https://v.douyin.com/iYFfJPtE/	https://live.douyin.com/872918554375	BQACAgUAAxkDAAIYcGYO0H_ZJ-cb1Tak8m4YyO3PXSGyAAIaEgACJuVwVJCyM_RdbfYyHgQ	BAACAgUAAxkDAAIYaGYO0FFKp1hAV7T9D1jyM5ezI-G-AAIWEgACJuVwVGbuelV7plpNHgQ	t	t	519f250b-6567-4275-89b5-20a9ed20c205	2024-04-03 15:43:25.519585+00	f	t	Douyin	\N	\N
雷躍BlueK	https://live.douyin.com/489069141497	https://live.douyin.com/489069141497	BQACAgUAAxkDAAIXt2YOhM8n7onsvsP6VFj_kSGW2OZwAAIuEQACJuVwVFRUsbO5ZDlLHgQ	BAACAgUAAxkDAAIXw2YOhPGM-EZtdgcYLwSfpLLpBrTWAAI0EQACJuVwVOdJjziTqv8GHgQ	t	f	489701e0-44fa-4f14-92cb-eb4ce1f91046	2024-03-30 05:12:01.335092+00	f	t	Douyin	\N	\N
余姚肉哥	https://v.douyin.com/iFq63GJq/	https://live.douyin.com/200859156287	BQACAgUAAxkDAAITn2YOHcSRn4qdnn4vbl_SIDo0Njn8AAIWDgACJuVwVOuNL0Q4Z5ZkHgQ	BAACAgUAAxkDAAITm2YOG_rbjd6xIuSNoCBv-zdLrFC2AAISDgACJuVwVJubt4r6IaCWHgQ	f	f	9798177e-860e-4ff6-b322-3c9007648ea9	2024-03-30 05:12:01.335092+00	f	t	Douyin	\N	\N
三文猫日语猫萌🐱	https://v.douyin.com/iYdPa6FB/	https://live.douyin.com/96621962908	BQACAgUAAxkDAAIOX2YLmIcohS7w5wcAAX-M8G20ZMWPgAACTw4AAqmTWFSSDjSd83Q47h4E	BAACAgUAAxkDAAIOYWYLmmGkbs0NZBWYSg3UtpxosJKqAAJTDgACqZNYVEGiaskGgKKCHgQ	f	t	c7da4b8a-5c01-4ce7-934c-85155c8993ed	2024-04-01 04:47:59.879076+00	f	t	Douyin	\N	\N
铁人🇨🇳	https://v.douyin.com/iFwjrkcn/	https://live.douyin.com/257643868462	BQACAgUAAxkDAAIR7GYNe1mMYVc4V-RYgk5n3v53oiHFAAI-DwACNL9oVNL9pXexJ2RtHgQ	BAACAgUAAxkDAAIR6mYNe1VYAe0kynIdwjepfqiKSH-XAAI9DwACNL9oVOedsD6IkPPVHgQ	t	t	37fba3c7-6a79-44e5-b013-1d8762d7829e	2024-03-30 05:12:01.335092+00	f	t	Douyin	\N	\N
coyscards	https://www.tiktok.com/t/ZTL6gMNgK/	https://www.tiktok.com/@coyscards/live	BQACAgUAAxkDAAITWGYODb6cvC0kRe9ravvxYaHyY6J7AALgDQACJuVwVA6CuPRE_Ml5HgQ	BAACAgUAAxkDAAITUGYODPgya0HlonkbU4tATdTJ9iXkAALaDQACJuVwVDX6DHe5mbpyHgQ	f	f	5a9a9bfd-78c4-461b-8c5d-1238a7fc2034	2024-03-30 05:12:01.335092+00	f	t	TikTok	\N	\N
AYUKA🙉🎸	https://www.tiktok.com/@ayuka_lala/live	https://www.tiktok.com/@ayuka_lala/live	BQACAgUAAxkDAAISPGYNkMdLPPAMdIw3kumOLqWPJzKyAAJvDwACNL9oVE1A8as_5MgrHgQ	BAACAgUAAxkDAAISPmYNkNJqdCQ39hbNs0nABagUNGipAAJwDwACNL9oVFThPPdiiQTcHgQ	t	t	32b8355d-e743-4a92-ab3f-f72ec68baa78	2024-03-30 05:12:01.335092+00	f	t	TikTok	\N	\N
twitch:laaroyce	https://www.tiktok.com/t/ZTL6cN6LR/	https://www.tiktok.com/@laaroyce/live	\N	\N	f	f	af5051a1-1daa-4f70-a21c-128de06e956a	2024-03-30 05:12:01.335092+00	f	t	TikTok	\N	\N
惊悚怪	https://v.douyin.com/iFy2xM4E/	https://live.douyin.com/166063492916	BQACAgUAAxkDAAISDmYNgqN8bRMwAwENZh8HG1Od3wJvAAJQDwACNL9oVB7WTqbUyZ5tHgQ	BAACAgUAAxkDAAISEGYNgqi4UzpsmwEpGwOjKRfdXyzgAAJRDwACNL9oVH-lON5RlbaJHgQ	f	f	5bb52047-bbf9-4820-a35e-1e3b54b47e09	2024-03-30 05:12:01.335092+00	f	t	Douyin	\N	\N
大楠在日本	https://v.douyin.com/iFf5mQG8/	https://live.douyin.com/513949947836	\N	\N	f	f	83b6989a-2066-486b-bda7-2e27eb0193e7	2024-03-30 05:12:01.335092+00	f	t	Douyin	\N	\N
老虍	https://v.douyin.com/iFgx7tA1/	https://live.douyin.com/68140530589	BQACAgUAAxkDAAISNGYNjQwk9OlK3uxWa0TOvpS_cJ4SAAJrDwACNL9oVHpVEzLJchgiHgQ	BAACAgUAAxkDAAISNWYNjQyTG1tPloa5wxNuefkAAZdtFgACbA8AAjS_aFQTAAGIIIK26nweBA	f	f	8c2c00ed-8977-45b0-8577-55c7bc161a32	2024-03-30 05:12:01.335092+00	f	t	Douyin	\N	\N
交个朋友直播间	https://v.douyin.com/iYd42u5h/	https://live.douyin.com/168465302284	BQACAgUAAxkDAAIYYGYOz8L53h8pb68nqmya1O9CjmevAAISEgACJuVwVONVo0PsLxs3HgQ	BAACAgUAAxkDAAIX62YOkZAUmtEjxcKGuJjhzAvCSqhZAAJgEQACJuVwVPUanQ1Fdl2SHgQ	t	t	f828f885-8075-4cb6-a0fd-c3904829bc03	2024-04-01 06:38:24.580608+00	f	t	Douyin	\N	\N
龙阿龙阿龙	https://v.douyin.com/iFPyXhNt/	https://live.douyin.com/224086959504	BQACAgUAAxkDAAIYZGYO0DyKskFPXLPuj-FBtBijH_RNAAIUEgACJuVwVHWcFdPCUAGnHgQ	BAACAgUAAxkDAAIYZmYO0EfnUzGjpxfM_6SwK4xeki7DAAIVEgACJuVwVAO6hcdZGz83HgQ	t	t	12c26785-000a-4e98-9704-4447e44412de	2024-03-30 05:12:01.335092+00	f	t	Douyin	\N	\N
配音美男子	https://v.douyin.com/iFy2Xvy3/	https://live.douyin.com/37914210056	BQACAgUAAxkDAAIPrWYMF4qZmIQqTuCTuH_kb5rEgJNIAALcDQACNL9gVMHQk-3usPuCHgQ	BAACAgUAAxkDAAIPr2YMF5DMFx5w_-EXRHHuRtfn_PogAALdDQACNL9gVI5yyqMXcihRHgQ	f	f	95965765-2a65-4da1-be0b-536b444651a4	2024-03-30 05:12:01.335092+00	f	t	Douyin	\N	\N
𝓵	https://www.tiktok.com/t/ZPRTueJXt/	https://www.tiktok.com/@leilaniiv.1/live	\N	\N	f	f	d8e15a4a-03a4-486f-a925-cbc3ad56fb46	2024-04-01 07:08:21.347641+00	t	t	TikTok	\N	\N
ᴴᴬᴿᴵ🎀 나하리 🎹	https://www.tiktok.com/t/ZSFuHDMvT/	https://www.tiktok.com/@nahariland/live	BQACAgUAAxkDAAIRZmYNVK9jXl_8mivTHhQXYWOjdEwPAALiDgACNL9oVC-kh2nVAbOSHgQ	BAACAgUAAxkDAAIRZGYNVK61w6uuG6OZL03kYkL23obsAALhDgACNL9oVIRwlaCyAAFkxx4E	t	f	c07c4fc8-d9ef-4f34-8560-3ebf2489b9f3	2024-03-30 05:12:01.335092+00	f	t	TikTok	\N	\N
盖子每天都很酷๕๖	https://v.douyin.com/iY6eUq1N/	https://live.douyin.com/633173397866	BQACAgUAAxkDAAIZdGYPyvjOQDkT9R3W2Cf6rPzMO2jzAALRDgACJuV4VEdr6V3ETx6OHgQ	BAACAgUAAxkDAAIZdmYPyxm6KEo0GyfYPmq0rhmhlYLPAALSDgACJuV4VMwflAyyK1fPHgQ	f	f	31471158-63e8-4f89-bd26-a1b030bd30ab	2024-04-05 09:05:15.261704+00	f	t	Douyin	\N	\N
饱饱熊	https://v.douyin.com/iF3xXrej/	https://live.douyin.com/30234587618	BQACAgUAAxkDAAIZE2YPSOIwNCQPxMADoM-lYY5J8MfRAALTDQACJuV4VJ2sBPX9ipD9HgQ	BAACAgUAAxkDAAIZEGYPSIUpZKVKjjfgOQUMlJ5v3bTKAALSDQACJuV4VGzIYdtjPMhkHgQ	f	f	b258a615-1a33-4122-a8ac-358e5c7aa97c	2024-03-30 05:12:01.335092+00	f	t	Douyin	\N	\N
赵雅芝4⽉10号11点新款家纺万⼈团购会	https://v.douyin.com/iFTeCNvs/	https://live.douyin.com/949398557422	\N	\N	t	f	af622d60-d89c-4a15-b597-3bc8e16fcf03	2024-03-30 05:12:01.335092+00	t	t	Douyin	\N	\N
胡大虫	https://v.douyin.com/iFyYbbk9/	https://live.douyin.com/82863128201	BQACAgUAAxkDAAIYWmYOzwf0BKZh6z-Ru8IzeXt9Aa87AAINEgACJuVwVOSmqfhxyDJuHgQ	BAACAgUAAxkDAAISCGYNglxQ4I6MFoLLAnTeJvojiHLnAAJNDwACNL9oVOG7wd5XYDGRHgQ	t	t	877bdab0-b603-4103-8aa0-528453a8aa48	2024-03-30 05:12:01.335092+00	f	t	Douyin	\N	\N
文武斌🌈	https://v.douyin.com/iFy2pKoy/	https://live.douyin.com/761922881827	BQACAgEAAxkDAAIDa2YK2YVN_MIyrKEfNCs1P-QFp5ryAAKoBAACVw5ZRMn6IXIa0LK8HgQ	BAACAgEAAxkDAAIDbGYK2fsp_3LeC6BFv82cQQ6xHfgzAAKpBAACVw5ZROMpi_odFoqzHgQ	f	t	5dbae2de-a268-48bb-81fb-4d46e5ca4410	2024-03-30 05:12:01.335092+00	f	t	Douyin	\N	\N
\.


--
-- Data for Name: file; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.file (anchor_name, anchor_id, id, "timestamp", timestampz, date, type) FROM stdin;
\N	acf2a797-7b0a-43d6-9561-5d7be7654596	\N	2024-03-30 13:40:42.90525	\N	\N	\N
\N	acf2a797-7b0a-43d6-9561-5d7be7654596	\N	2024-03-30 13:43:37.861695	\N	\N	\N
\.


--
-- Data for Name: user_subscriptions; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.user_subscriptions (user_id, anchor_id, send_file, send_video, push_notification) FROM stdin;
1355840683	0886017b-2b07-4b16-b250-b869f5b8d384	t	t	t
1355840683	fb2e05db-667f-41c2-916c-c6a7526cf52d	t	t	t
1355840683	877bdab0-b603-4103-8aa0-528453a8aa48	t	t	t
1355840683	f28aed7e-aa85-4fba-b5e4-26716609b35f	t	t	t
1355840683	cc946dd9-4ed4-4534-bc81-2ae4efceb987	t	t	t
1355840683	4893c002-51a4-4cb2-9e26-c65e81956bc9	t	t	t
1355840683	72ccbfed-f87b-4905-a7a5-b0ac17c470d4	t	t	t
1355840683	2e7d195b-20d8-4fcb-85cd-b8ea0d16e5f8	t	t	t
1355840683	cdb6d7f1-05f8-4be9-812d-75635a5f092d	t	t	t
1355840683	ea046534-f40b-47ff-9bc8-874161a220e9	t	t	t
1355840683	9b100d57-5193-4362-b1d5-6122092fbd33	t	t	t
1355840683	5dbae2de-a268-48bb-81fb-4d46e5ca4410	t	t	t
1355840683	49c89d7b-868f-4ce8-a7d3-edd7f70af158	t	t	t
1355840683	87c543a2-8e19-4e64-9114-5acf94a8bf39	t	t	t
1355840683	83b6989a-2066-486b-bda7-2e27eb0193e7	t	t	t
1355840683	a17ec824-4e7c-40b8-9701-2263a3b5f68a	t	t	t
1355840683	bf1add38-6495-44bd-a2a1-c923620fd71b	t	t	t
1355840683	16db8578-67d3-489a-b297-cecd909f30fd	t	t	t
1355840683	12c26785-000a-4e98-9704-4447e44412de	t	t	t
1355840683	fbe3db70-2aa5-4a6e-997f-886ffd531b32	t	t	t
1355840683	43d9dd00-e3ed-4260-9c29-8e2a9e772c85	t	t	t
1355840683	5bb52047-bbf9-4820-a35e-1e3b54b47e09	t	t	t
1355840683	95965765-2a65-4da1-be0b-536b444651a4	t	t	t
1355840683	8fa3e757-f456-4e50-a437-5da0b7ead77a	t	t	t
1355840683	9798177e-860e-4ff6-b322-3c9007648ea9	t	t	t
1355840683	32b8355d-e743-4a92-ab3f-f72ec68baa78	t	t	t
1355840683	7ea18d14-0234-4720-a540-f78806228ea1	t	t	t
1355840683	3f66dd84-4da0-456d-b8cd-d75d709eb1e0	t	t	t
1355840683	57be941b-12bf-44ef-a50e-92ba03a5298e	t	t	t
1355840683	b463ded3-6d4b-4471-82b8-ee107fa2539e	t	t	t
1355840683	02ddae6d-5277-4524-8818-7af2cdfebbb1	t	t	t
1355840683	7f4e8290-05b4-4184-84a4-ac857148e6cf	t	t	t
1355840683	489701e0-44fa-4f14-92cb-eb4ce1f91046	t	t	t
1355840683	434b41e3-25ef-4f73-af49-4217b16701c1	t	t	t
1355840683	1c0ec025-d2ed-4e5e-ad14-7322f1395268	t	t	t
1355840683	cee5b45c-d5a9-4867-a86f-933513faf536	t	t	t
1355840683	a3a84a63-ea81-4144-8db1-f5973a9a3335	t	t	t
1355840683	f3ce01d7-ba4f-41ce-b2ea-196b59e2b66d	t	t	t
1355840683	0d8b5c50-dc0b-4191-bb51-0909e5b1b999	t	t	t
1355840683	e4a7a75c-35d2-40a9-9c09-a35c14efe2b9	t	t	t
1355840683	23955180-bbc8-4db8-b536-d8263ca98ddd	t	t	t
1355840683	7b4487af-efaf-4330-beb7-a0640041faa3	t	t	t
1355840683	b0302e31-bcc7-4d42-aab7-482a75f06bf1	t	t	t
1355840683	917bdbff-18c3-464e-8841-e6af31ba7c4e	t	t	t
1355840683	cd93ec2d-dcd1-4fe7-8fa2-2b7553a4fb2e	t	t	t
1355840683	ec78e3aa-a791-464d-9ca7-dcf2339fa874	t	t	t
1355840683	2c2ce248-5222-4918-8bc3-44684f3c3bbe	t	t	t
1355840683	8c2c00ed-8977-45b0-8577-55c7bc161a32	t	t	t
1355840683	26261c9d-bccb-484b-8a1e-63e130ed9f1e	t	t	t
1355840683	66fbcf87-02a5-4219-8c91-7eb04df72930	t	t	t
1355840683	5a9a9bfd-78c4-461b-8c5d-1238a7fc2034	t	t	t
1355840683	600e05a2-9e6d-4aaa-8e83-6950703d7dbf	t	t	t
1355840683	bc5edb3f-e3ce-4989-9ec0-d90729cc1ada	t	t	t
1355840683	285f5d24-e8c5-4f8b-90a0-56d12abd2426	t	t	t
1355840683	25ddb3c1-ba75-4cc0-a1c2-ed76ff1082de	t	t	t
1355840683	d5914ab7-d323-4969-b990-d5a6179d3353	t	t	t
1355840683	1b40d49c-2c68-4dac-8ffe-cac815966522	t	t	t
1355840683	82bba014-6377-4fa3-a378-81c493bda133	t	t	t
1355840683	c07c4fc8-d9ef-4f34-8560-3ebf2489b9f3	t	t	t
1355840683	83372199-6bbb-46d4-b175-1024f2993333	t	t	t
1355840683	83a2c08f-5a2f-4608-83e9-f50578c8f23b	t	t	t
1355840683	e93633ae-06b0-4296-9122-5de17faacc2c	t	t	t
1355840683	3717d0b4-107b-4731-914d-7f588ef345a7	t	t	t
1355840683	c986fd33-e044-4727-bc23-480254c78f5e	t	t	t
1355840683	74c3b354-8db0-4d7b-a07d-fcb97d3c5d56	t	t	t
1355840683	b959d168-3ad0-49dc-bbcf-52f75839cc21	t	t	t
1355840683	2e57bd19-7f1a-445d-ae3b-72499af12028	t	t	t
1355840683	7cdc7352-a80c-4918-9492-9dd58a54fef4	t	t	t
1355840683	acf2a797-7b0a-43d6-9561-5d7be7654596	t	t	t
1355840683	af5051a1-1daa-4f70-a21c-128de06e956a	t	t	t
1355840683	e69cd3e6-3290-4a67-9a3a-94089b1033e5	t	t	t
1355840683	f0138a5d-f632-47c8-85e4-1d82988dbebf	t	t	t
1355840683	a244c6fe-7d8b-4825-864e-bae9a0404399	t	t	t
255757275	05bcfe37-59cd-40f0-8120-f068a141668d	t	t	t
1355840683	05bcfe37-59cd-40f0-8120-f068a141668d	t	t	t
255757275	cde01b4a-edc0-4f7f-8e7e-f89cf7669f89	t	t	t
255757275	e96bad1e-c505-4d4a-b720-c6f34be0e235	t	t	t
255757275	697ed9dd-28ab-4240-8ea3-f1b491bf8868	t	t	t
255757275	83a2c08f-5a2f-4608-83e9-f50578c8f23b	t	t	t
1355840683	697ed9dd-28ab-4240-8ea3-f1b491bf8868	t	t	t
255757275	b258a615-1a33-4122-a8ac-358e5c7aa97c	t	t	t
255757275	b0f1aa62-fb70-410f-a795-95a61f90cce8	t	t	t
255757275	34b3e34e-f6c2-4ba4-b3f4-abb260fa5419	t	t	t
255757275	b6b272b6-0dc3-4740-8232-44fd254d235b	t	t	t
1355840683	79b19bbd-13ff-4819-bc2d-aab2bd8c57ce	t	t	t
255757275	098f6c47-d34c-4e60-86c1-dc4bce6a83b6	t	t	t
255757275	37fba3c7-6a79-44e5-b013-1d8762d7829e	t	t	t
255757275	35075411-3383-41c2-bc5e-5212d3621f2b	t	t	t
255757275	5bb52047-bbf9-4820-a35e-1e3b54b47e09	t	t	t
255757275	930f9f3d-22a2-491a-9be5-84af5aa0f98f	t	t	t
1355840683	fb5f1e72-e015-47ae-9447-651dab5078cf	t	t	t
1355840683	3043b292-44a5-4c7b-b536-78d93adb66d3	t	t	t
1355840683	0f2e6c59-aa23-4f72-9795-65d75f4ba10b	t	t	t
255757275	c073e70b-b631-4af5-b6bb-0c79e021399a	t	t	t
255757275	faec3128-5192-42fa-a68e-7efe9f46e5c1	t	t	t
255757275	c22c774b-cb4f-4052-80fe-ae5280056a1c	t	t	t
235196660	eeb07860-971f-467f-af1c-75218b575831	t	t	t
235196660	c7da4b8a-5c01-4ce7-934c-85155c8993ed	t	t	t
235196660	8296ed73-7f66-44a6-9de4-670fd9fe4fc6	t	t	t
235196660	f828f885-8075-4cb6-a0fd-c3904829bc03	t	t	t
235196660	447e5bef-c356-4569-95fa-9fd1ec4ca27c	t	t	t
235196660	8565fac8-3774-40b7-a9dc-c88e484777d1	t	t	t
1355840683	7d135cd9-e3c7-4a09-a0c7-726662b97d97	t	t	t
1355840683	1780d203-0cb4-41d1-9a63-f2171aed73d6	t	t	t
235196660	4893c002-51a4-4cb2-9e26-c65e81956bc9	t	t	t
235196660	f9af1eb9-4622-4983-94c2-ebea1db19262	t	t	t
255757275	f9af1eb9-4622-4983-94c2-ebea1db19262	t	t	t
235196660	813dc207-9080-4678-81bc-98e3e23fa52f	t	t	t
235196660	31bc73b3-eed8-4899-83c1-a061a3fd93a5	t	t	t
255757275	5df72d61-271a-479c-a525-c3f7bac9a815	t	t	t
235196660	87df29c0-9e96-4ed0-9ab6-e8851247531e	t	t	t
235196660	6ff7b205-ae36-420e-b185-d3e5359a77ba	t	t	t
235196660	1ebb2c3b-7e41-4bdb-b090-16424ce4d113	t	t	t
255757275	d195603b-08a6-4c9c-9e5d-b3570aae693b	t	t	t
255757275	fa9bff02-63a3-40d0-9870-61195e335021	t	t	t
255757275	533cc65e-6477-45c8-b6a5-fb3eff8a7c13	t	t	t
255757275	ad9bf83a-4fdb-4929-8d8b-e297e075ad47	t	t	t
255757275	44e4613e-8736-4419-8aa1-1572488670ab	t	t	t
1355840683	519f250b-6567-4275-89b5-20a9ed20c205	t	t	t
1355840683	d195603b-08a6-4c9c-9e5d-b3570aae693b	t	t	t
235196660	fde40757-aaf5-4a67-ab7c-8872cd424e98	t	t	t
255757275	c71b09aa-29fc-4c3e-9841-d1ded1775a2b	t	t	t
255757275	bf7b83cc-678f-46a9-9efa-e57ece9e1dd6	t	t	t
255757275	d42bce41-6767-4716-b939-432efd3279f1	t	t	t
1355840683	31471158-63e8-4f89-bd26-a1b030bd30ab	t	t	t
1355840683	3a16c588-5c51-4bd6-aa3f-37ec4e743646	t	t	t
255757275	16024921-ad59-4a32-9068-a5b65bb8db2e	t	t	t
235196660	183c1c6b-6979-44c0-8b85-f571cdb203dc	t	t	t
235196660	360df5fc-a771-4cec-b702-f4dab11b8259	t	t	t
235196660	19845e2e-271b-49ab-880e-c0222f3f0ba3	t	t	t
235196660	16d9d7da-5c5d-44f2-81b8-296bbcca83e6	t	t	t
235196660	bb46d500-8685-4f5e-8523-0f1f8594a05d	t	t	t
235196660	07327675-2b5c-4443-b065-940f0f699fa8	t	t	t
235196660	02d84481-987e-4fbf-aa21-0023cccef8d2	t	t	t
255757275	f74a944f-29b8-4e1d-8a75-96c4c0544d0a	t	t	t
255757275	fe06d68b-d329-48c7-9df2-ca4c2aa01854	t	t	t
1355840683	b6382cee-81c5-4ea2-aef7-ae644862f751	t	t	t
1355840683	2ef7ca45-0eeb-45c9-98fe-b3f96bf46603	t	t	t
235196660	d603d2c1-bce0-4e6e-b00d-9287686420da	t	t	t
235196660	5838b264-3e76-4030-8220-f86604699f25	t	t	t
235196660	580b49c8-dec3-41bb-8490-0655ddf0d664	t	t	t
235196660	d33e4dd8-709d-491c-b586-21d0c06266aa	t	t	t
235196660	95fb28ed-2420-4ab7-a122-af056b1dc507	t	t	t
235196660	43f838f0-821c-4119-b0f1-c730bfc7a3c6	t	t	t
235196660	cee6846c-29aa-4172-9c7f-1cb1a581b67a	t	t	t
\.


--
-- Data for Name: users; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.users (user_id, username, allowance, tier, push_notification, send_file, send_video) FROM stdin;
235196660	dreamworked	100	private	t	f	t
255757275	\N	190000	privare	f	f	t
1355840683	juaer	1000	privare	f	f	f
6565208961	\N	2	free	t	f	f
\.


--
-- Data for Name: broadcasts; Type: TABLE DATA; Schema: realtime; Owner: supabase_realtime_admin
--

COPY realtime.broadcasts (id, channel_id, "check", inserted_at, updated_at) FROM stdin;
\.


--
-- Data for Name: channels; Type: TABLE DATA; Schema: realtime; Owner: supabase_realtime_admin
--

COPY realtime.channels (id, name, inserted_at, updated_at, "check") FROM stdin;
\.


--
-- Data for Name: presences; Type: TABLE DATA; Schema: realtime; Owner: supabase_realtime_admin
--

COPY realtime.presences (id, channel_id, "check", inserted_at, updated_at) FROM stdin;
\.


--
-- Data for Name: schema_migrations; Type: TABLE DATA; Schema: realtime; Owner: supabase_admin
--

COPY realtime.schema_migrations (version, inserted_at) FROM stdin;
20211116024918	2024-03-25 20:07:36
20211116045059	2024-03-25 20:07:36
20211116050929	2024-03-25 20:07:37
20211116051442	2024-03-25 20:07:38
20211116212300	2024-03-25 20:07:39
20211116213355	2024-03-25 20:07:39
20211116213934	2024-03-25 20:07:40
20211116214523	2024-03-25 20:07:41
20211122062447	2024-03-25 20:07:42
20211124070109	2024-03-25 20:07:43
20211202204204	2024-03-25 20:07:43
20211202204605	2024-03-25 20:07:44
20211210212804	2024-03-25 20:07:46
20211228014915	2024-03-25 20:07:47
20220107221237	2024-03-25 20:07:48
20220228202821	2024-03-25 20:07:48
20220312004840	2024-03-25 20:07:49
20220603231003	2024-03-25 20:07:50
20220603232444	2024-03-25 20:07:51
20220615214548	2024-03-25 20:07:52
20220712093339	2024-03-25 20:07:53
20220908172859	2024-03-25 20:07:53
20220916233421	2024-03-25 20:07:54
20230119133233	2024-03-25 20:07:55
20230128025114	2024-03-25 20:07:56
20230128025212	2024-03-25 20:07:56
20230227211149	2024-03-25 20:07:57
20230228184745	2024-03-25 20:07:58
20230308225145	2024-03-25 20:07:58
20230328144023	2024-03-25 20:07:59
20231018144023	2024-03-25 20:08:00
20231204144023	2024-03-25 20:08:01
20231204144024	2024-03-25 20:08:02
20231204144025	2024-03-25 20:08:03
20240108234812	2024-03-25 20:08:03
20240109165339	2024-03-25 20:08:04
20240227174441	2024-03-25 20:08:05
20240311171622	2024-04-04 15:24:16
20240321100241	2024-04-04 15:24:18
20240401105812	2024-04-04 15:24:20
\.


--
-- Data for Name: subscription; Type: TABLE DATA; Schema: realtime; Owner: supabase_admin
--

COPY realtime.subscription (id, subscription_id, entity, filters, claims, created_at) FROM stdin;
20783	d0f9fc88-f74a-11ee-b6ba-0a58a9feac02	public.anchors	{}	{"exp": 2026927293, "iat": 1711351293, "iss": "supabase", "ref": "nathkbbkyfjthxpdqocu", "role": "anon"}	2024-04-10 14:58:40.012026
\.


--
-- Data for Name: buckets; Type: TABLE DATA; Schema: storage; Owner: supabase_storage_admin
--

COPY storage.buckets (id, name, owner, created_at, updated_at, public, avif_autodetection, file_size_limit, allowed_mime_types, owner_id) FROM stdin;
\.


--
-- Data for Name: migrations; Type: TABLE DATA; Schema: storage; Owner: supabase_storage_admin
--

COPY storage.migrations (id, name, hash, executed_at) FROM stdin;
0	create-migrations-table	e18db593bcde2aca2a408c4d1100f6abba2195df	2024-03-25 07:22:29.23353
1	initialmigration	6ab16121fbaa08bbd11b712d05f358f9b555d777	2024-03-25 07:22:29.292314
2	storage-schema	5c7968fd083fcea04050c1b7f6253c9771b99011	2024-03-25 07:22:29.305202
3	pathtoken-column	2cb1b0004b817b29d5b0a971af16bafeede4b70d	2024-03-25 07:22:29.378101
4	add-migrations-rls	427c5b63fe1c5937495d9c635c263ee7a5905058	2024-03-25 07:22:29.407782
5	add-size-functions	79e081a1455b63666c1294a440f8ad4b1e6a7f84	2024-03-25 07:22:29.421171
6	change-column-name-in-get-size	f93f62afdf6613ee5e7e815b30d02dc990201044	2024-03-25 07:22:29.434946
7	add-rls-to-buckets	e7e7f86adbc51049f341dfe8d30256c1abca17aa	2024-03-25 07:22:29.449237
8	add-public-to-buckets	fd670db39ed65f9d08b01db09d6202503ca2bab3	2024-03-25 07:22:29.462485
9	fix-search-function	3a0af29f42e35a4d101c259ed955b67e1bee6825	2024-03-25 07:22:29.475964
10	search-files-search-function	68dc14822daad0ffac3746a502234f486182ef6e	2024-03-25 07:22:29.489872
11	add-trigger-to-auto-update-updated_at-column	7425bdb14366d1739fa8a18c83100636d74dcaa2	2024-03-25 07:22:29.503677
12	add-automatic-avif-detection-flag	8e92e1266eb29518b6a4c5313ab8f29dd0d08df9	2024-03-25 07:22:29.560273
13	add-bucket-custom-limits	cce962054138135cd9a8c4bcd531598684b25e7d	2024-03-25 07:22:29.616164
14	use-bytes-for-max-size	941c41b346f9802b411f06f30e972ad4744dad27	2024-03-25 07:22:29.635719
15	add-can-insert-object-function	934146bc38ead475f4ef4b555c524ee5d66799e5	2024-03-25 07:22:29.667051
16	add-version	76debf38d3fd07dcfc747ca49096457d95b1221b	2024-03-25 07:22:29.680706
17	drop-owner-foreign-key	f1cbb288f1b7a4c1eb8c38504b80ae2a0153d101	2024-03-25 07:22:29.693946
18	add_owner_id_column_deprecate_owner	e7a511b379110b08e2f214be852c35414749fe66	2024-03-25 07:22:29.752328
19	alter-default-value-objects-id	02e5e22a78626187e00d173dc45f58fa66a4f043	2024-03-25 07:22:29.766697
\.


--
-- Data for Name: objects; Type: TABLE DATA; Schema: storage; Owner: supabase_storage_admin
--

COPY storage.objects (id, bucket_id, name, owner, created_at, updated_at, last_accessed_at, metadata, version, owner_id) FROM stdin;
\.


--
-- Data for Name: hooks; Type: TABLE DATA; Schema: supabase_functions; Owner: supabase_functions_admin
--

COPY supabase_functions.hooks (id, hook_table_id, hook_name, created_at, request_id) FROM stdin;
\.


--
-- Data for Name: migrations; Type: TABLE DATA; Schema: supabase_functions; Owner: supabase_functions_admin
--

COPY supabase_functions.migrations (version, inserted_at) FROM stdin;
initial	2024-03-31 04:35:12.43706+00
20210809183423_update_grants	2024-03-31 04:35:12.43706+00
\.


--
-- Data for Name: secrets; Type: TABLE DATA; Schema: vault; Owner: supabase_admin
--

COPY vault.secrets (id, name, description, secret, key_id, nonce, created_at, updated_at) FROM stdin;
\.


--
-- Name: refresh_tokens_id_seq; Type: SEQUENCE SET; Schema: auth; Owner: supabase_auth_admin
--

SELECT pg_catalog.setval('auth.refresh_tokens_id_seq', 1, false);


--
-- Name: key_key_id_seq; Type: SEQUENCE SET; Schema: pgsodium; Owner: supabase_admin
--

SELECT pg_catalog.setval('pgsodium.key_key_id_seq', 1, false);


--
-- Name: users_user_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.users_user_id_seq', 1, false);


--
-- Name: broadcasts_id_seq; Type: SEQUENCE SET; Schema: realtime; Owner: supabase_realtime_admin
--

SELECT pg_catalog.setval('realtime.broadcasts_id_seq', 1, false);


--
-- Name: channels_id_seq; Type: SEQUENCE SET; Schema: realtime; Owner: supabase_realtime_admin
--

SELECT pg_catalog.setval('realtime.channels_id_seq', 1, false);


--
-- Name: presences_id_seq; Type: SEQUENCE SET; Schema: realtime; Owner: supabase_realtime_admin
--

SELECT pg_catalog.setval('realtime.presences_id_seq', 1, false);


--
-- Name: subscription_id_seq; Type: SEQUENCE SET; Schema: realtime; Owner: supabase_admin
--

SELECT pg_catalog.setval('realtime.subscription_id_seq', 20783, true);


--
-- Name: hooks_id_seq; Type: SEQUENCE SET; Schema: supabase_functions; Owner: supabase_functions_admin
--

SELECT pg_catalog.setval('supabase_functions.hooks_id_seq', 1, false);


--
-- Name: mfa_amr_claims amr_id_pk; Type: CONSTRAINT; Schema: auth; Owner: supabase_auth_admin
--

ALTER TABLE ONLY auth.mfa_amr_claims
    ADD CONSTRAINT amr_id_pk PRIMARY KEY (id);


--
-- Name: audit_log_entries audit_log_entries_pkey; Type: CONSTRAINT; Schema: auth; Owner: supabase_auth_admin
--

ALTER TABLE ONLY auth.audit_log_entries
    ADD CONSTRAINT audit_log_entries_pkey PRIMARY KEY (id);


--
-- Name: flow_state flow_state_pkey; Type: CONSTRAINT; Schema: auth; Owner: supabase_auth_admin
--

ALTER TABLE ONLY auth.flow_state
    ADD CONSTRAINT flow_state_pkey PRIMARY KEY (id);


--
-- Name: identities identities_pkey; Type: CONSTRAINT; Schema: auth; Owner: supabase_auth_admin
--

ALTER TABLE ONLY auth.identities
    ADD CONSTRAINT identities_pkey PRIMARY KEY (id);


--
-- Name: identities identities_provider_id_provider_unique; Type: CONSTRAINT; Schema: auth; Owner: supabase_auth_admin
--

ALTER TABLE ONLY auth.identities
    ADD CONSTRAINT identities_provider_id_provider_unique UNIQUE (provider_id, provider);


--
-- Name: instances instances_pkey; Type: CONSTRAINT; Schema: auth; Owner: supabase_auth_admin
--

ALTER TABLE ONLY auth.instances
    ADD CONSTRAINT instances_pkey PRIMARY KEY (id);


--
-- Name: mfa_amr_claims mfa_amr_claims_session_id_authentication_method_pkey; Type: CONSTRAINT; Schema: auth; Owner: supabase_auth_admin
--

ALTER TABLE ONLY auth.mfa_amr_claims
    ADD CONSTRAINT mfa_amr_claims_session_id_authentication_method_pkey UNIQUE (session_id, authentication_method);


--
-- Name: mfa_challenges mfa_challenges_pkey; Type: CONSTRAINT; Schema: auth; Owner: supabase_auth_admin
--

ALTER TABLE ONLY auth.mfa_challenges
    ADD CONSTRAINT mfa_challenges_pkey PRIMARY KEY (id);


--
-- Name: mfa_factors mfa_factors_pkey; Type: CONSTRAINT; Schema: auth; Owner: supabase_auth_admin
--

ALTER TABLE ONLY auth.mfa_factors
    ADD CONSTRAINT mfa_factors_pkey PRIMARY KEY (id);


--
-- Name: refresh_tokens refresh_tokens_pkey; Type: CONSTRAINT; Schema: auth; Owner: supabase_auth_admin
--

ALTER TABLE ONLY auth.refresh_tokens
    ADD CONSTRAINT refresh_tokens_pkey PRIMARY KEY (id);


--
-- Name: refresh_tokens refresh_tokens_token_unique; Type: CONSTRAINT; Schema: auth; Owner: supabase_auth_admin
--

ALTER TABLE ONLY auth.refresh_tokens
    ADD CONSTRAINT refresh_tokens_token_unique UNIQUE (token);


--
-- Name: saml_providers saml_providers_entity_id_key; Type: CONSTRAINT; Schema: auth; Owner: supabase_auth_admin
--

ALTER TABLE ONLY auth.saml_providers
    ADD CONSTRAINT saml_providers_entity_id_key UNIQUE (entity_id);


--
-- Name: saml_providers saml_providers_pkey; Type: CONSTRAINT; Schema: auth; Owner: supabase_auth_admin
--

ALTER TABLE ONLY auth.saml_providers
    ADD CONSTRAINT saml_providers_pkey PRIMARY KEY (id);


--
-- Name: saml_relay_states saml_relay_states_pkey; Type: CONSTRAINT; Schema: auth; Owner: supabase_auth_admin
--

ALTER TABLE ONLY auth.saml_relay_states
    ADD CONSTRAINT saml_relay_states_pkey PRIMARY KEY (id);


--
-- Name: schema_migrations schema_migrations_pkey; Type: CONSTRAINT; Schema: auth; Owner: supabase_auth_admin
--

ALTER TABLE ONLY auth.schema_migrations
    ADD CONSTRAINT schema_migrations_pkey PRIMARY KEY (version);


--
-- Name: sessions sessions_pkey; Type: CONSTRAINT; Schema: auth; Owner: supabase_auth_admin
--

ALTER TABLE ONLY auth.sessions
    ADD CONSTRAINT sessions_pkey PRIMARY KEY (id);


--
-- Name: sso_domains sso_domains_pkey; Type: CONSTRAINT; Schema: auth; Owner: supabase_auth_admin
--

ALTER TABLE ONLY auth.sso_domains
    ADD CONSTRAINT sso_domains_pkey PRIMARY KEY (id);


--
-- Name: sso_providers sso_providers_pkey; Type: CONSTRAINT; Schema: auth; Owner: supabase_auth_admin
--

ALTER TABLE ONLY auth.sso_providers
    ADD CONSTRAINT sso_providers_pkey PRIMARY KEY (id);


--
-- Name: users users_phone_key; Type: CONSTRAINT; Schema: auth; Owner: supabase_auth_admin
--

ALTER TABLE ONLY auth.users
    ADD CONSTRAINT users_phone_key UNIQUE (phone);


--
-- Name: users users_pkey; Type: CONSTRAINT; Schema: auth; Owner: supabase_auth_admin
--

ALTER TABLE ONLY auth.users
    ADD CONSTRAINT users_pkey PRIMARY KEY (id);


--
-- Name: anchors anchors_anchor_id_key; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.anchors
    ADD CONSTRAINT anchors_anchor_id_key UNIQUE (anchor_id);


--
-- Name: anchors anchors_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.anchors
    ADD CONSTRAINT anchors_pkey PRIMARY KEY (anchor_id);


--
-- Name: user_subscriptions user_subscriptions_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.user_subscriptions
    ADD CONSTRAINT user_subscriptions_pkey PRIMARY KEY (user_id, anchor_id);


--
-- Name: users users_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.users
    ADD CONSTRAINT users_pkey PRIMARY KEY (user_id);


--
-- Name: broadcasts broadcasts_pkey; Type: CONSTRAINT; Schema: realtime; Owner: supabase_realtime_admin
--

ALTER TABLE ONLY realtime.broadcasts
    ADD CONSTRAINT broadcasts_pkey PRIMARY KEY (id);


--
-- Name: channels channels_pkey; Type: CONSTRAINT; Schema: realtime; Owner: supabase_realtime_admin
--

ALTER TABLE ONLY realtime.channels
    ADD CONSTRAINT channels_pkey PRIMARY KEY (id);


--
-- Name: subscription pk_subscription; Type: CONSTRAINT; Schema: realtime; Owner: supabase_admin
--

ALTER TABLE ONLY realtime.subscription
    ADD CONSTRAINT pk_subscription PRIMARY KEY (id);


--
-- Name: presences presences_pkey; Type: CONSTRAINT; Schema: realtime; Owner: supabase_realtime_admin
--

ALTER TABLE ONLY realtime.presences
    ADD CONSTRAINT presences_pkey PRIMARY KEY (id);


--
-- Name: schema_migrations schema_migrations_pkey; Type: CONSTRAINT; Schema: realtime; Owner: supabase_admin
--

ALTER TABLE ONLY realtime.schema_migrations
    ADD CONSTRAINT schema_migrations_pkey PRIMARY KEY (version);


--
-- Name: buckets buckets_pkey; Type: CONSTRAINT; Schema: storage; Owner: supabase_storage_admin
--

ALTER TABLE ONLY storage.buckets
    ADD CONSTRAINT buckets_pkey PRIMARY KEY (id);


--
-- Name: migrations migrations_name_key; Type: CONSTRAINT; Schema: storage; Owner: supabase_storage_admin
--

ALTER TABLE ONLY storage.migrations
    ADD CONSTRAINT migrations_name_key UNIQUE (name);


--
-- Name: migrations migrations_pkey; Type: CONSTRAINT; Schema: storage; Owner: supabase_storage_admin
--

ALTER TABLE ONLY storage.migrations
    ADD CONSTRAINT migrations_pkey PRIMARY KEY (id);


--
-- Name: objects objects_pkey; Type: CONSTRAINT; Schema: storage; Owner: supabase_storage_admin
--

ALTER TABLE ONLY storage.objects
    ADD CONSTRAINT objects_pkey PRIMARY KEY (id);


--
-- Name: hooks hooks_pkey; Type: CONSTRAINT; Schema: supabase_functions; Owner: supabase_functions_admin
--

ALTER TABLE ONLY supabase_functions.hooks
    ADD CONSTRAINT hooks_pkey PRIMARY KEY (id);


--
-- Name: migrations migrations_pkey; Type: CONSTRAINT; Schema: supabase_functions; Owner: supabase_functions_admin
--

ALTER TABLE ONLY supabase_functions.migrations
    ADD CONSTRAINT migrations_pkey PRIMARY KEY (version);


--
-- Name: audit_logs_instance_id_idx; Type: INDEX; Schema: auth; Owner: supabase_auth_admin
--

CREATE INDEX audit_logs_instance_id_idx ON auth.audit_log_entries USING btree (instance_id);


--
-- Name: confirmation_token_idx; Type: INDEX; Schema: auth; Owner: supabase_auth_admin
--

CREATE UNIQUE INDEX confirmation_token_idx ON auth.users USING btree (confirmation_token) WHERE ((confirmation_token)::text !~ '^[0-9 ]*$'::text);


--
-- Name: email_change_token_current_idx; Type: INDEX; Schema: auth; Owner: supabase_auth_admin
--

CREATE UNIQUE INDEX email_change_token_current_idx ON auth.users USING btree (email_change_token_current) WHERE ((email_change_token_current)::text !~ '^[0-9 ]*$'::text);


--
-- Name: email_change_token_new_idx; Type: INDEX; Schema: auth; Owner: supabase_auth_admin
--

CREATE UNIQUE INDEX email_change_token_new_idx ON auth.users USING btree (email_change_token_new) WHERE ((email_change_token_new)::text !~ '^[0-9 ]*$'::text);


--
-- Name: factor_id_created_at_idx; Type: INDEX; Schema: auth; Owner: supabase_auth_admin
--

CREATE INDEX factor_id_created_at_idx ON auth.mfa_factors USING btree (user_id, created_at);


--
-- Name: flow_state_created_at_idx; Type: INDEX; Schema: auth; Owner: supabase_auth_admin
--

CREATE INDEX flow_state_created_at_idx ON auth.flow_state USING btree (created_at DESC);


--
-- Name: identities_email_idx; Type: INDEX; Schema: auth; Owner: supabase_auth_admin
--

CREATE INDEX identities_email_idx ON auth.identities USING btree (email text_pattern_ops);


--
-- Name: INDEX identities_email_idx; Type: COMMENT; Schema: auth; Owner: supabase_auth_admin
--

COMMENT ON INDEX auth.identities_email_idx IS 'Auth: Ensures indexed queries on the email column';


--
-- Name: identities_user_id_idx; Type: INDEX; Schema: auth; Owner: supabase_auth_admin
--

CREATE INDEX identities_user_id_idx ON auth.identities USING btree (user_id);


--
-- Name: idx_auth_code; Type: INDEX; Schema: auth; Owner: supabase_auth_admin
--

CREATE INDEX idx_auth_code ON auth.flow_state USING btree (auth_code);


--
-- Name: idx_user_id_auth_method; Type: INDEX; Schema: auth; Owner: supabase_auth_admin
--

CREATE INDEX idx_user_id_auth_method ON auth.flow_state USING btree (user_id, authentication_method);


--
-- Name: mfa_challenge_created_at_idx; Type: INDEX; Schema: auth; Owner: supabase_auth_admin
--

CREATE INDEX mfa_challenge_created_at_idx ON auth.mfa_challenges USING btree (created_at DESC);


--
-- Name: mfa_factors_user_friendly_name_unique; Type: INDEX; Schema: auth; Owner: supabase_auth_admin
--

CREATE UNIQUE INDEX mfa_factors_user_friendly_name_unique ON auth.mfa_factors USING btree (friendly_name, user_id) WHERE (TRIM(BOTH FROM friendly_name) <> ''::text);


--
-- Name: mfa_factors_user_id_idx; Type: INDEX; Schema: auth; Owner: supabase_auth_admin
--

CREATE INDEX mfa_factors_user_id_idx ON auth.mfa_factors USING btree (user_id);


--
-- Name: reauthentication_token_idx; Type: INDEX; Schema: auth; Owner: supabase_auth_admin
--

CREATE UNIQUE INDEX reauthentication_token_idx ON auth.users USING btree (reauthentication_token) WHERE ((reauthentication_token)::text !~ '^[0-9 ]*$'::text);


--
-- Name: recovery_token_idx; Type: INDEX; Schema: auth; Owner: supabase_auth_admin
--

CREATE UNIQUE INDEX recovery_token_idx ON auth.users USING btree (recovery_token) WHERE ((recovery_token)::text !~ '^[0-9 ]*$'::text);


--
-- Name: refresh_tokens_instance_id_idx; Type: INDEX; Schema: auth; Owner: supabase_auth_admin
--

CREATE INDEX refresh_tokens_instance_id_idx ON auth.refresh_tokens USING btree (instance_id);


--
-- Name: refresh_tokens_instance_id_user_id_idx; Type: INDEX; Schema: auth; Owner: supabase_auth_admin
--

CREATE INDEX refresh_tokens_instance_id_user_id_idx ON auth.refresh_tokens USING btree (instance_id, user_id);


--
-- Name: refresh_tokens_parent_idx; Type: INDEX; Schema: auth; Owner: supabase_auth_admin
--

CREATE INDEX refresh_tokens_parent_idx ON auth.refresh_tokens USING btree (parent);


--
-- Name: refresh_tokens_session_id_revoked_idx; Type: INDEX; Schema: auth; Owner: supabase_auth_admin
--

CREATE INDEX refresh_tokens_session_id_revoked_idx ON auth.refresh_tokens USING btree (session_id, revoked);


--
-- Name: refresh_tokens_updated_at_idx; Type: INDEX; Schema: auth; Owner: supabase_auth_admin
--

CREATE INDEX refresh_tokens_updated_at_idx ON auth.refresh_tokens USING btree (updated_at DESC);


--
-- Name: saml_providers_sso_provider_id_idx; Type: INDEX; Schema: auth; Owner: supabase_auth_admin
--

CREATE INDEX saml_providers_sso_provider_id_idx ON auth.saml_providers USING btree (sso_provider_id);


--
-- Name: saml_relay_states_created_at_idx; Type: INDEX; Schema: auth; Owner: supabase_auth_admin
--

CREATE INDEX saml_relay_states_created_at_idx ON auth.saml_relay_states USING btree (created_at DESC);


--
-- Name: saml_relay_states_for_email_idx; Type: INDEX; Schema: auth; Owner: supabase_auth_admin
--

CREATE INDEX saml_relay_states_for_email_idx ON auth.saml_relay_states USING btree (for_email);


--
-- Name: saml_relay_states_sso_provider_id_idx; Type: INDEX; Schema: auth; Owner: supabase_auth_admin
--

CREATE INDEX saml_relay_states_sso_provider_id_idx ON auth.saml_relay_states USING btree (sso_provider_id);


--
-- Name: sessions_not_after_idx; Type: INDEX; Schema: auth; Owner: supabase_auth_admin
--

CREATE INDEX sessions_not_after_idx ON auth.sessions USING btree (not_after DESC);


--
-- Name: sessions_user_id_idx; Type: INDEX; Schema: auth; Owner: supabase_auth_admin
--

CREATE INDEX sessions_user_id_idx ON auth.sessions USING btree (user_id);


--
-- Name: sso_domains_domain_idx; Type: INDEX; Schema: auth; Owner: supabase_auth_admin
--

CREATE UNIQUE INDEX sso_domains_domain_idx ON auth.sso_domains USING btree (lower(domain));


--
-- Name: sso_domains_sso_provider_id_idx; Type: INDEX; Schema: auth; Owner: supabase_auth_admin
--

CREATE INDEX sso_domains_sso_provider_id_idx ON auth.sso_domains USING btree (sso_provider_id);


--
-- Name: sso_providers_resource_id_idx; Type: INDEX; Schema: auth; Owner: supabase_auth_admin
--

CREATE UNIQUE INDEX sso_providers_resource_id_idx ON auth.sso_providers USING btree (lower(resource_id));


--
-- Name: user_id_created_at_idx; Type: INDEX; Schema: auth; Owner: supabase_auth_admin
--

CREATE INDEX user_id_created_at_idx ON auth.sessions USING btree (user_id, created_at);


--
-- Name: users_email_partial_key; Type: INDEX; Schema: auth; Owner: supabase_auth_admin
--

CREATE UNIQUE INDEX users_email_partial_key ON auth.users USING btree (email) WHERE (is_sso_user = false);


--
-- Name: INDEX users_email_partial_key; Type: COMMENT; Schema: auth; Owner: supabase_auth_admin
--

COMMENT ON INDEX auth.users_email_partial_key IS 'Auth: A partial unique index that applies only when is_sso_user is false';


--
-- Name: users_instance_id_email_idx; Type: INDEX; Schema: auth; Owner: supabase_auth_admin
--

CREATE INDEX users_instance_id_email_idx ON auth.users USING btree (instance_id, lower((email)::text));


--
-- Name: users_instance_id_idx; Type: INDEX; Schema: auth; Owner: supabase_auth_admin
--

CREATE INDEX users_instance_id_idx ON auth.users USING btree (instance_id);


--
-- Name: users_is_anonymous_idx; Type: INDEX; Schema: auth; Owner: supabase_auth_admin
--

CREATE INDEX users_is_anonymous_idx ON auth.users USING btree (is_anonymous);


--
-- Name: idx_user_subscriptions_anchor_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_user_subscriptions_anchor_id ON public.user_subscriptions USING btree (anchor_id);


--
-- Name: idx_user_subscriptions_user_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_user_subscriptions_user_id ON public.user_subscriptions USING btree (user_id);


--
-- Name: broadcasts_channel_id_index; Type: INDEX; Schema: realtime; Owner: supabase_realtime_admin
--

CREATE UNIQUE INDEX broadcasts_channel_id_index ON realtime.broadcasts USING btree (channel_id);


--
-- Name: channels_name_index; Type: INDEX; Schema: realtime; Owner: supabase_realtime_admin
--

CREATE UNIQUE INDEX channels_name_index ON realtime.channels USING btree (name);


--
-- Name: ix_realtime_subscription_entity; Type: INDEX; Schema: realtime; Owner: supabase_admin
--

CREATE INDEX ix_realtime_subscription_entity ON realtime.subscription USING hash (entity);


--
-- Name: presences_channel_id_index; Type: INDEX; Schema: realtime; Owner: supabase_realtime_admin
--

CREATE UNIQUE INDEX presences_channel_id_index ON realtime.presences USING btree (channel_id);


--
-- Name: subscription_subscription_id_entity_filters_key; Type: INDEX; Schema: realtime; Owner: supabase_admin
--

CREATE UNIQUE INDEX subscription_subscription_id_entity_filters_key ON realtime.subscription USING btree (subscription_id, entity, filters);


--
-- Name: bname; Type: INDEX; Schema: storage; Owner: supabase_storage_admin
--

CREATE UNIQUE INDEX bname ON storage.buckets USING btree (name);


--
-- Name: bucketid_objname; Type: INDEX; Schema: storage; Owner: supabase_storage_admin
--

CREATE UNIQUE INDEX bucketid_objname ON storage.objects USING btree (bucket_id, name);


--
-- Name: name_prefix_search; Type: INDEX; Schema: storage; Owner: supabase_storage_admin
--

CREATE INDEX name_prefix_search ON storage.objects USING btree (name text_pattern_ops);


--
-- Name: supabase_functions_hooks_h_table_id_h_name_idx; Type: INDEX; Schema: supabase_functions; Owner: supabase_functions_admin
--

CREATE INDEX supabase_functions_hooks_h_table_id_h_name_idx ON supabase_functions.hooks USING btree (hook_table_id, hook_name);


--
-- Name: supabase_functions_hooks_request_id_idx; Type: INDEX; Schema: supabase_functions; Owner: supabase_functions_admin
--

CREATE INDEX supabase_functions_hooks_request_id_idx ON supabase_functions.hooks USING btree (request_id);


--
-- Name: anchors trigger_update_platform_before_insert; Type: TRIGGER; Schema: public; Owner: postgres
--

CREATE TRIGGER trigger_update_platform_before_insert BEFORE INSERT ON public.anchors FOR EACH ROW EXECUTE FUNCTION public.update_platform_on_insert();


--
-- Name: subscription tr_check_filters; Type: TRIGGER; Schema: realtime; Owner: supabase_admin
--

CREATE TRIGGER tr_check_filters BEFORE INSERT OR UPDATE ON realtime.subscription FOR EACH ROW EXECUTE FUNCTION realtime.subscription_check_filters();


--
-- Name: objects update_objects_updated_at; Type: TRIGGER; Schema: storage; Owner: supabase_storage_admin
--

CREATE TRIGGER update_objects_updated_at BEFORE UPDATE ON storage.objects FOR EACH ROW EXECUTE FUNCTION storage.update_updated_at_column();


--
-- Name: identities identities_user_id_fkey; Type: FK CONSTRAINT; Schema: auth; Owner: supabase_auth_admin
--

ALTER TABLE ONLY auth.identities
    ADD CONSTRAINT identities_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE CASCADE;


--
-- Name: mfa_amr_claims mfa_amr_claims_session_id_fkey; Type: FK CONSTRAINT; Schema: auth; Owner: supabase_auth_admin
--

ALTER TABLE ONLY auth.mfa_amr_claims
    ADD CONSTRAINT mfa_amr_claims_session_id_fkey FOREIGN KEY (session_id) REFERENCES auth.sessions(id) ON DELETE CASCADE;


--
-- Name: mfa_challenges mfa_challenges_auth_factor_id_fkey; Type: FK CONSTRAINT; Schema: auth; Owner: supabase_auth_admin
--

ALTER TABLE ONLY auth.mfa_challenges
    ADD CONSTRAINT mfa_challenges_auth_factor_id_fkey FOREIGN KEY (factor_id) REFERENCES auth.mfa_factors(id) ON DELETE CASCADE;


--
-- Name: mfa_factors mfa_factors_user_id_fkey; Type: FK CONSTRAINT; Schema: auth; Owner: supabase_auth_admin
--

ALTER TABLE ONLY auth.mfa_factors
    ADD CONSTRAINT mfa_factors_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE CASCADE;


--
-- Name: refresh_tokens refresh_tokens_session_id_fkey; Type: FK CONSTRAINT; Schema: auth; Owner: supabase_auth_admin
--

ALTER TABLE ONLY auth.refresh_tokens
    ADD CONSTRAINT refresh_tokens_session_id_fkey FOREIGN KEY (session_id) REFERENCES auth.sessions(id) ON DELETE CASCADE;


--
-- Name: saml_providers saml_providers_sso_provider_id_fkey; Type: FK CONSTRAINT; Schema: auth; Owner: supabase_auth_admin
--

ALTER TABLE ONLY auth.saml_providers
    ADD CONSTRAINT saml_providers_sso_provider_id_fkey FOREIGN KEY (sso_provider_id) REFERENCES auth.sso_providers(id) ON DELETE CASCADE;


--
-- Name: saml_relay_states saml_relay_states_flow_state_id_fkey; Type: FK CONSTRAINT; Schema: auth; Owner: supabase_auth_admin
--

ALTER TABLE ONLY auth.saml_relay_states
    ADD CONSTRAINT saml_relay_states_flow_state_id_fkey FOREIGN KEY (flow_state_id) REFERENCES auth.flow_state(id) ON DELETE CASCADE;


--
-- Name: saml_relay_states saml_relay_states_sso_provider_id_fkey; Type: FK CONSTRAINT; Schema: auth; Owner: supabase_auth_admin
--

ALTER TABLE ONLY auth.saml_relay_states
    ADD CONSTRAINT saml_relay_states_sso_provider_id_fkey FOREIGN KEY (sso_provider_id) REFERENCES auth.sso_providers(id) ON DELETE CASCADE;


--
-- Name: sessions sessions_user_id_fkey; Type: FK CONSTRAINT; Schema: auth; Owner: supabase_auth_admin
--

ALTER TABLE ONLY auth.sessions
    ADD CONSTRAINT sessions_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE CASCADE;


--
-- Name: sso_domains sso_domains_sso_provider_id_fkey; Type: FK CONSTRAINT; Schema: auth; Owner: supabase_auth_admin
--

ALTER TABLE ONLY auth.sso_domains
    ADD CONSTRAINT sso_domains_sso_provider_id_fkey FOREIGN KEY (sso_provider_id) REFERENCES auth.sso_providers(id) ON DELETE CASCADE;


--
-- Name: user_subscriptions public_user_subscriptions_anchor_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.user_subscriptions
    ADD CONSTRAINT public_user_subscriptions_anchor_id_fkey FOREIGN KEY (anchor_id) REFERENCES public.anchors(anchor_id);


--
-- Name: user_subscriptions public_user_subscriptions_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.user_subscriptions
    ADD CONSTRAINT public_user_subscriptions_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(user_id);


--
-- Name: broadcasts broadcasts_channel_id_fkey; Type: FK CONSTRAINT; Schema: realtime; Owner: supabase_realtime_admin
--

ALTER TABLE ONLY realtime.broadcasts
    ADD CONSTRAINT broadcasts_channel_id_fkey FOREIGN KEY (channel_id) REFERENCES realtime.channels(id) ON DELETE CASCADE;


--
-- Name: presences presences_channel_id_fkey; Type: FK CONSTRAINT; Schema: realtime; Owner: supabase_realtime_admin
--

ALTER TABLE ONLY realtime.presences
    ADD CONSTRAINT presences_channel_id_fkey FOREIGN KEY (channel_id) REFERENCES realtime.channels(id) ON DELETE CASCADE;


--
-- Name: objects objects_bucketId_fkey; Type: FK CONSTRAINT; Schema: storage; Owner: supabase_storage_admin
--

ALTER TABLE ONLY storage.objects
    ADD CONSTRAINT "objects_bucketId_fkey" FOREIGN KEY (bucket_id) REFERENCES storage.buckets(id);


--
-- Name: broadcasts; Type: ROW SECURITY; Schema: realtime; Owner: supabase_realtime_admin
--

ALTER TABLE realtime.broadcasts ENABLE ROW LEVEL SECURITY;

--
-- Name: channels; Type: ROW SECURITY; Schema: realtime; Owner: supabase_realtime_admin
--

ALTER TABLE realtime.channels ENABLE ROW LEVEL SECURITY;

--
-- Name: presences; Type: ROW SECURITY; Schema: realtime; Owner: supabase_realtime_admin
--

ALTER TABLE realtime.presences ENABLE ROW LEVEL SECURITY;

--
-- Name: buckets; Type: ROW SECURITY; Schema: storage; Owner: supabase_storage_admin
--

ALTER TABLE storage.buckets ENABLE ROW LEVEL SECURITY;

--
-- Name: migrations; Type: ROW SECURITY; Schema: storage; Owner: supabase_storage_admin
--

ALTER TABLE storage.migrations ENABLE ROW LEVEL SECURITY;

--
-- Name: objects; Type: ROW SECURITY; Schema: storage; Owner: supabase_storage_admin
--

ALTER TABLE storage.objects ENABLE ROW LEVEL SECURITY;

--
-- Name: supabase_realtime; Type: PUBLICATION; Schema: -; Owner: postgres
--

CREATE PUBLICATION supabase_realtime WITH (publish = 'insert, update, delete, truncate');


ALTER PUBLICATION supabase_realtime OWNER TO postgres;

--
-- Name: supabase_realtime anchors; Type: PUBLICATION TABLE; Schema: public; Owner: postgres
--

ALTER PUBLICATION supabase_realtime ADD TABLE ONLY public.anchors;


--
-- Name: SCHEMA auth; Type: ACL; Schema: -; Owner: supabase_admin
--

GRANT USAGE ON SCHEMA auth TO anon;
GRANT USAGE ON SCHEMA auth TO authenticated;
GRANT USAGE ON SCHEMA auth TO service_role;
GRANT ALL ON SCHEMA auth TO supabase_auth_admin;
GRANT ALL ON SCHEMA auth TO dashboard_user;
GRANT ALL ON SCHEMA auth TO postgres;


--
-- Name: SCHEMA extensions; Type: ACL; Schema: -; Owner: postgres
--

GRANT USAGE ON SCHEMA extensions TO anon;
GRANT USAGE ON SCHEMA extensions TO authenticated;
GRANT USAGE ON SCHEMA extensions TO service_role;
GRANT ALL ON SCHEMA extensions TO dashboard_user;


--
-- Name: SCHEMA net; Type: ACL; Schema: -; Owner: supabase_admin
--

GRANT USAGE ON SCHEMA net TO supabase_functions_admin;
GRANT USAGE ON SCHEMA net TO anon;
GRANT USAGE ON SCHEMA net TO authenticated;
GRANT USAGE ON SCHEMA net TO service_role;


--
-- Name: SCHEMA public; Type: ACL; Schema: -; Owner: pg_database_owner
--

GRANT USAGE ON SCHEMA public TO postgres;
GRANT USAGE ON SCHEMA public TO anon;
GRANT USAGE ON SCHEMA public TO authenticated;
GRANT USAGE ON SCHEMA public TO service_role;


--
-- Name: SCHEMA realtime; Type: ACL; Schema: -; Owner: supabase_admin
--

GRANT USAGE ON SCHEMA realtime TO postgres;
GRANT USAGE ON SCHEMA realtime TO anon;
GRANT USAGE ON SCHEMA realtime TO authenticated;
GRANT USAGE ON SCHEMA realtime TO service_role;
GRANT ALL ON SCHEMA realtime TO supabase_realtime_admin;


--
-- Name: SCHEMA storage; Type: ACL; Schema: -; Owner: supabase_admin
--

GRANT ALL ON SCHEMA storage TO postgres;
GRANT USAGE ON SCHEMA storage TO anon;
GRANT USAGE ON SCHEMA storage TO authenticated;
GRANT USAGE ON SCHEMA storage TO service_role;
GRANT ALL ON SCHEMA storage TO supabase_storage_admin;
GRANT ALL ON SCHEMA storage TO dashboard_user;


--
-- Name: SCHEMA supabase_functions; Type: ACL; Schema: -; Owner: supabase_admin
--

GRANT USAGE ON SCHEMA supabase_functions TO postgres;
GRANT USAGE ON SCHEMA supabase_functions TO anon;
GRANT USAGE ON SCHEMA supabase_functions TO authenticated;
GRANT USAGE ON SCHEMA supabase_functions TO service_role;
GRANT ALL ON SCHEMA supabase_functions TO supabase_functions_admin;


--
-- Name: FUNCTION email(); Type: ACL; Schema: auth; Owner: supabase_auth_admin
--

GRANT ALL ON FUNCTION auth.email() TO dashboard_user;


--
-- Name: FUNCTION jwt(); Type: ACL; Schema: auth; Owner: supabase_auth_admin
--

GRANT ALL ON FUNCTION auth.jwt() TO postgres;
GRANT ALL ON FUNCTION auth.jwt() TO dashboard_user;


--
-- Name: FUNCTION role(); Type: ACL; Schema: auth; Owner: supabase_auth_admin
--

GRANT ALL ON FUNCTION auth.role() TO dashboard_user;


--
-- Name: FUNCTION uid(); Type: ACL; Schema: auth; Owner: supabase_auth_admin
--

GRANT ALL ON FUNCTION auth.uid() TO dashboard_user;


--
-- Name: FUNCTION algorithm_sign(signables text, secret text, algorithm text); Type: ACL; Schema: extensions; Owner: postgres
--

REVOKE ALL ON FUNCTION extensions.algorithm_sign(signables text, secret text, algorithm text) FROM postgres;
GRANT ALL ON FUNCTION extensions.algorithm_sign(signables text, secret text, algorithm text) TO postgres WITH GRANT OPTION;
GRANT ALL ON FUNCTION extensions.algorithm_sign(signables text, secret text, algorithm text) TO dashboard_user;


--
-- Name: FUNCTION armor(bytea); Type: ACL; Schema: extensions; Owner: postgres
--

REVOKE ALL ON FUNCTION extensions.armor(bytea) FROM postgres;
GRANT ALL ON FUNCTION extensions.armor(bytea) TO postgres WITH GRANT OPTION;
GRANT ALL ON FUNCTION extensions.armor(bytea) TO dashboard_user;


--
-- Name: FUNCTION armor(bytea, text[], text[]); Type: ACL; Schema: extensions; Owner: postgres
--

REVOKE ALL ON FUNCTION extensions.armor(bytea, text[], text[]) FROM postgres;
GRANT ALL ON FUNCTION extensions.armor(bytea, text[], text[]) TO postgres WITH GRANT OPTION;
GRANT ALL ON FUNCTION extensions.armor(bytea, text[], text[]) TO dashboard_user;


--
-- Name: FUNCTION crypt(text, text); Type: ACL; Schema: extensions; Owner: postgres
--

REVOKE ALL ON FUNCTION extensions.crypt(text, text) FROM postgres;
GRANT ALL ON FUNCTION extensions.crypt(text, text) TO postgres WITH GRANT OPTION;
GRANT ALL ON FUNCTION extensions.crypt(text, text) TO dashboard_user;


--
-- Name: FUNCTION dearmor(text); Type: ACL; Schema: extensions; Owner: postgres
--

REVOKE ALL ON FUNCTION extensions.dearmor(text) FROM postgres;
GRANT ALL ON FUNCTION extensions.dearmor(text) TO postgres WITH GRANT OPTION;
GRANT ALL ON FUNCTION extensions.dearmor(text) TO dashboard_user;


--
-- Name: FUNCTION decrypt(bytea, bytea, text); Type: ACL; Schema: extensions; Owner: postgres
--

REVOKE ALL ON FUNCTION extensions.decrypt(bytea, bytea, text) FROM postgres;
GRANT ALL ON FUNCTION extensions.decrypt(bytea, bytea, text) TO postgres WITH GRANT OPTION;
GRANT ALL ON FUNCTION extensions.decrypt(bytea, bytea, text) TO dashboard_user;


--
-- Name: FUNCTION decrypt_iv(bytea, bytea, bytea, text); Type: ACL; Schema: extensions; Owner: postgres
--

REVOKE ALL ON FUNCTION extensions.decrypt_iv(bytea, bytea, bytea, text) FROM postgres;
GRANT ALL ON FUNCTION extensions.decrypt_iv(bytea, bytea, bytea, text) TO postgres WITH GRANT OPTION;
GRANT ALL ON FUNCTION extensions.decrypt_iv(bytea, bytea, bytea, text) TO dashboard_user;


--
-- Name: FUNCTION digest(bytea, text); Type: ACL; Schema: extensions; Owner: postgres
--

REVOKE ALL ON FUNCTION extensions.digest(bytea, text) FROM postgres;
GRANT ALL ON FUNCTION extensions.digest(bytea, text) TO postgres WITH GRANT OPTION;
GRANT ALL ON FUNCTION extensions.digest(bytea, text) TO dashboard_user;


--
-- Name: FUNCTION digest(text, text); Type: ACL; Schema: extensions; Owner: postgres
--

REVOKE ALL ON FUNCTION extensions.digest(text, text) FROM postgres;
GRANT ALL ON FUNCTION extensions.digest(text, text) TO postgres WITH GRANT OPTION;
GRANT ALL ON FUNCTION extensions.digest(text, text) TO dashboard_user;


--
-- Name: FUNCTION encrypt(bytea, bytea, text); Type: ACL; Schema: extensions; Owner: postgres
--

REVOKE ALL ON FUNCTION extensions.encrypt(bytea, bytea, text) FROM postgres;
GRANT ALL ON FUNCTION extensions.encrypt(bytea, bytea, text) TO postgres WITH GRANT OPTION;
GRANT ALL ON FUNCTION extensions.encrypt(bytea, bytea, text) TO dashboard_user;


--
-- Name: FUNCTION encrypt_iv(bytea, bytea, bytea, text); Type: ACL; Schema: extensions; Owner: postgres
--

REVOKE ALL ON FUNCTION extensions.encrypt_iv(bytea, bytea, bytea, text) FROM postgres;
GRANT ALL ON FUNCTION extensions.encrypt_iv(bytea, bytea, bytea, text) TO postgres WITH GRANT OPTION;
GRANT ALL ON FUNCTION extensions.encrypt_iv(bytea, bytea, bytea, text) TO dashboard_user;


--
-- Name: FUNCTION gen_random_bytes(integer); Type: ACL; Schema: extensions; Owner: postgres
--

REVOKE ALL ON FUNCTION extensions.gen_random_bytes(integer) FROM postgres;
GRANT ALL ON FUNCTION extensions.gen_random_bytes(integer) TO postgres WITH GRANT OPTION;
GRANT ALL ON FUNCTION extensions.gen_random_bytes(integer) TO dashboard_user;


--
-- Name: FUNCTION gen_random_uuid(); Type: ACL; Schema: extensions; Owner: postgres
--

REVOKE ALL ON FUNCTION extensions.gen_random_uuid() FROM postgres;
GRANT ALL ON FUNCTION extensions.gen_random_uuid() TO postgres WITH GRANT OPTION;
GRANT ALL ON FUNCTION extensions.gen_random_uuid() TO dashboard_user;


--
-- Name: FUNCTION gen_salt(text); Type: ACL; Schema: extensions; Owner: postgres
--

REVOKE ALL ON FUNCTION extensions.gen_salt(text) FROM postgres;
GRANT ALL ON FUNCTION extensions.gen_salt(text) TO postgres WITH GRANT OPTION;
GRANT ALL ON FUNCTION extensions.gen_salt(text) TO dashboard_user;


--
-- Name: FUNCTION gen_salt(text, integer); Type: ACL; Schema: extensions; Owner: postgres
--

REVOKE ALL ON FUNCTION extensions.gen_salt(text, integer) FROM postgres;
GRANT ALL ON FUNCTION extensions.gen_salt(text, integer) TO postgres WITH GRANT OPTION;
GRANT ALL ON FUNCTION extensions.gen_salt(text, integer) TO dashboard_user;


--
-- Name: FUNCTION grant_pg_cron_access(); Type: ACL; Schema: extensions; Owner: postgres
--

REVOKE ALL ON FUNCTION extensions.grant_pg_cron_access() FROM postgres;
GRANT ALL ON FUNCTION extensions.grant_pg_cron_access() TO postgres WITH GRANT OPTION;
GRANT ALL ON FUNCTION extensions.grant_pg_cron_access() TO dashboard_user;


--
-- Name: FUNCTION grant_pg_graphql_access(); Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON FUNCTION extensions.grant_pg_graphql_access() TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION grant_pg_net_access(); Type: ACL; Schema: extensions; Owner: postgres
--

REVOKE ALL ON FUNCTION extensions.grant_pg_net_access() FROM postgres;
GRANT ALL ON FUNCTION extensions.grant_pg_net_access() TO postgres WITH GRANT OPTION;
GRANT ALL ON FUNCTION extensions.grant_pg_net_access() TO dashboard_user;


--
-- Name: FUNCTION hmac(bytea, bytea, text); Type: ACL; Schema: extensions; Owner: postgres
--

REVOKE ALL ON FUNCTION extensions.hmac(bytea, bytea, text) FROM postgres;
GRANT ALL ON FUNCTION extensions.hmac(bytea, bytea, text) TO postgres WITH GRANT OPTION;
GRANT ALL ON FUNCTION extensions.hmac(bytea, bytea, text) TO dashboard_user;


--
-- Name: FUNCTION hmac(text, text, text); Type: ACL; Schema: extensions; Owner: postgres
--

REVOKE ALL ON FUNCTION extensions.hmac(text, text, text) FROM postgres;
GRANT ALL ON FUNCTION extensions.hmac(text, text, text) TO postgres WITH GRANT OPTION;
GRANT ALL ON FUNCTION extensions.hmac(text, text, text) TO dashboard_user;


--
-- Name: FUNCTION pg_stat_statements(showtext boolean, OUT userid oid, OUT dbid oid, OUT toplevel boolean, OUT queryid bigint, OUT query text, OUT plans bigint, OUT total_plan_time double precision, OUT min_plan_time double precision, OUT max_plan_time double precision, OUT mean_plan_time double precision, OUT stddev_plan_time double precision, OUT calls bigint, OUT total_exec_time double precision, OUT min_exec_time double precision, OUT max_exec_time double precision, OUT mean_exec_time double precision, OUT stddev_exec_time double precision, OUT rows bigint, OUT shared_blks_hit bigint, OUT shared_blks_read bigint, OUT shared_blks_dirtied bigint, OUT shared_blks_written bigint, OUT local_blks_hit bigint, OUT local_blks_read bigint, OUT local_blks_dirtied bigint, OUT local_blks_written bigint, OUT temp_blks_read bigint, OUT temp_blks_written bigint, OUT blk_read_time double precision, OUT blk_write_time double precision, OUT temp_blk_read_time double precision, OUT temp_blk_write_time double precision, OUT wal_records bigint, OUT wal_fpi bigint, OUT wal_bytes numeric, OUT jit_functions bigint, OUT jit_generation_time double precision, OUT jit_inlining_count bigint, OUT jit_inlining_time double precision, OUT jit_optimization_count bigint, OUT jit_optimization_time double precision, OUT jit_emission_count bigint, OUT jit_emission_time double precision); Type: ACL; Schema: extensions; Owner: postgres
--

REVOKE ALL ON FUNCTION extensions.pg_stat_statements(showtext boolean, OUT userid oid, OUT dbid oid, OUT toplevel boolean, OUT queryid bigint, OUT query text, OUT plans bigint, OUT total_plan_time double precision, OUT min_plan_time double precision, OUT max_plan_time double precision, OUT mean_plan_time double precision, OUT stddev_plan_time double precision, OUT calls bigint, OUT total_exec_time double precision, OUT min_exec_time double precision, OUT max_exec_time double precision, OUT mean_exec_time double precision, OUT stddev_exec_time double precision, OUT rows bigint, OUT shared_blks_hit bigint, OUT shared_blks_read bigint, OUT shared_blks_dirtied bigint, OUT shared_blks_written bigint, OUT local_blks_hit bigint, OUT local_blks_read bigint, OUT local_blks_dirtied bigint, OUT local_blks_written bigint, OUT temp_blks_read bigint, OUT temp_blks_written bigint, OUT blk_read_time double precision, OUT blk_write_time double precision, OUT temp_blk_read_time double precision, OUT temp_blk_write_time double precision, OUT wal_records bigint, OUT wal_fpi bigint, OUT wal_bytes numeric, OUT jit_functions bigint, OUT jit_generation_time double precision, OUT jit_inlining_count bigint, OUT jit_inlining_time double precision, OUT jit_optimization_count bigint, OUT jit_optimization_time double precision, OUT jit_emission_count bigint, OUT jit_emission_time double precision) FROM postgres;
GRANT ALL ON FUNCTION extensions.pg_stat_statements(showtext boolean, OUT userid oid, OUT dbid oid, OUT toplevel boolean, OUT queryid bigint, OUT query text, OUT plans bigint, OUT total_plan_time double precision, OUT min_plan_time double precision, OUT max_plan_time double precision, OUT mean_plan_time double precision, OUT stddev_plan_time double precision, OUT calls bigint, OUT total_exec_time double precision, OUT min_exec_time double precision, OUT max_exec_time double precision, OUT mean_exec_time double precision, OUT stddev_exec_time double precision, OUT rows bigint, OUT shared_blks_hit bigint, OUT shared_blks_read bigint, OUT shared_blks_dirtied bigint, OUT shared_blks_written bigint, OUT local_blks_hit bigint, OUT local_blks_read bigint, OUT local_blks_dirtied bigint, OUT local_blks_written bigint, OUT temp_blks_read bigint, OUT temp_blks_written bigint, OUT blk_read_time double precision, OUT blk_write_time double precision, OUT temp_blk_read_time double precision, OUT temp_blk_write_time double precision, OUT wal_records bigint, OUT wal_fpi bigint, OUT wal_bytes numeric, OUT jit_functions bigint, OUT jit_generation_time double precision, OUT jit_inlining_count bigint, OUT jit_inlining_time double precision, OUT jit_optimization_count bigint, OUT jit_optimization_time double precision, OUT jit_emission_count bigint, OUT jit_emission_time double precision) TO postgres WITH GRANT OPTION;
GRANT ALL ON FUNCTION extensions.pg_stat_statements(showtext boolean, OUT userid oid, OUT dbid oid, OUT toplevel boolean, OUT queryid bigint, OUT query text, OUT plans bigint, OUT total_plan_time double precision, OUT min_plan_time double precision, OUT max_plan_time double precision, OUT mean_plan_time double precision, OUT stddev_plan_time double precision, OUT calls bigint, OUT total_exec_time double precision, OUT min_exec_time double precision, OUT max_exec_time double precision, OUT mean_exec_time double precision, OUT stddev_exec_time double precision, OUT rows bigint, OUT shared_blks_hit bigint, OUT shared_blks_read bigint, OUT shared_blks_dirtied bigint, OUT shared_blks_written bigint, OUT local_blks_hit bigint, OUT local_blks_read bigint, OUT local_blks_dirtied bigint, OUT local_blks_written bigint, OUT temp_blks_read bigint, OUT temp_blks_written bigint, OUT blk_read_time double precision, OUT blk_write_time double precision, OUT temp_blk_read_time double precision, OUT temp_blk_write_time double precision, OUT wal_records bigint, OUT wal_fpi bigint, OUT wal_bytes numeric, OUT jit_functions bigint, OUT jit_generation_time double precision, OUT jit_inlining_count bigint, OUT jit_inlining_time double precision, OUT jit_optimization_count bigint, OUT jit_optimization_time double precision, OUT jit_emission_count bigint, OUT jit_emission_time double precision) TO dashboard_user;


--
-- Name: FUNCTION pg_stat_statements_info(OUT dealloc bigint, OUT stats_reset timestamp with time zone); Type: ACL; Schema: extensions; Owner: postgres
--

REVOKE ALL ON FUNCTION extensions.pg_stat_statements_info(OUT dealloc bigint, OUT stats_reset timestamp with time zone) FROM postgres;
GRANT ALL ON FUNCTION extensions.pg_stat_statements_info(OUT dealloc bigint, OUT stats_reset timestamp with time zone) TO postgres WITH GRANT OPTION;
GRANT ALL ON FUNCTION extensions.pg_stat_statements_info(OUT dealloc bigint, OUT stats_reset timestamp with time zone) TO dashboard_user;


--
-- Name: FUNCTION pg_stat_statements_reset(userid oid, dbid oid, queryid bigint); Type: ACL; Schema: extensions; Owner: postgres
--

REVOKE ALL ON FUNCTION extensions.pg_stat_statements_reset(userid oid, dbid oid, queryid bigint) FROM postgres;
GRANT ALL ON FUNCTION extensions.pg_stat_statements_reset(userid oid, dbid oid, queryid bigint) TO postgres WITH GRANT OPTION;
GRANT ALL ON FUNCTION extensions.pg_stat_statements_reset(userid oid, dbid oid, queryid bigint) TO dashboard_user;


--
-- Name: FUNCTION pgp_armor_headers(text, OUT key text, OUT value text); Type: ACL; Schema: extensions; Owner: postgres
--

REVOKE ALL ON FUNCTION extensions.pgp_armor_headers(text, OUT key text, OUT value text) FROM postgres;
GRANT ALL ON FUNCTION extensions.pgp_armor_headers(text, OUT key text, OUT value text) TO postgres WITH GRANT OPTION;
GRANT ALL ON FUNCTION extensions.pgp_armor_headers(text, OUT key text, OUT value text) TO dashboard_user;


--
-- Name: FUNCTION pgp_key_id(bytea); Type: ACL; Schema: extensions; Owner: postgres
--

REVOKE ALL ON FUNCTION extensions.pgp_key_id(bytea) FROM postgres;
GRANT ALL ON FUNCTION extensions.pgp_key_id(bytea) TO postgres WITH GRANT OPTION;
GRANT ALL ON FUNCTION extensions.pgp_key_id(bytea) TO dashboard_user;


--
-- Name: FUNCTION pgp_pub_decrypt(bytea, bytea); Type: ACL; Schema: extensions; Owner: postgres
--

REVOKE ALL ON FUNCTION extensions.pgp_pub_decrypt(bytea, bytea) FROM postgres;
GRANT ALL ON FUNCTION extensions.pgp_pub_decrypt(bytea, bytea) TO postgres WITH GRANT OPTION;
GRANT ALL ON FUNCTION extensions.pgp_pub_decrypt(bytea, bytea) TO dashboard_user;


--
-- Name: FUNCTION pgp_pub_decrypt(bytea, bytea, text); Type: ACL; Schema: extensions; Owner: postgres
--

REVOKE ALL ON FUNCTION extensions.pgp_pub_decrypt(bytea, bytea, text) FROM postgres;
GRANT ALL ON FUNCTION extensions.pgp_pub_decrypt(bytea, bytea, text) TO postgres WITH GRANT OPTION;
GRANT ALL ON FUNCTION extensions.pgp_pub_decrypt(bytea, bytea, text) TO dashboard_user;


--
-- Name: FUNCTION pgp_pub_decrypt(bytea, bytea, text, text); Type: ACL; Schema: extensions; Owner: postgres
--

REVOKE ALL ON FUNCTION extensions.pgp_pub_decrypt(bytea, bytea, text, text) FROM postgres;
GRANT ALL ON FUNCTION extensions.pgp_pub_decrypt(bytea, bytea, text, text) TO postgres WITH GRANT OPTION;
GRANT ALL ON FUNCTION extensions.pgp_pub_decrypt(bytea, bytea, text, text) TO dashboard_user;


--
-- Name: FUNCTION pgp_pub_decrypt_bytea(bytea, bytea); Type: ACL; Schema: extensions; Owner: postgres
--

REVOKE ALL ON FUNCTION extensions.pgp_pub_decrypt_bytea(bytea, bytea) FROM postgres;
GRANT ALL ON FUNCTION extensions.pgp_pub_decrypt_bytea(bytea, bytea) TO postgres WITH GRANT OPTION;
GRANT ALL ON FUNCTION extensions.pgp_pub_decrypt_bytea(bytea, bytea) TO dashboard_user;


--
-- Name: FUNCTION pgp_pub_decrypt_bytea(bytea, bytea, text); Type: ACL; Schema: extensions; Owner: postgres
--

REVOKE ALL ON FUNCTION extensions.pgp_pub_decrypt_bytea(bytea, bytea, text) FROM postgres;
GRANT ALL ON FUNCTION extensions.pgp_pub_decrypt_bytea(bytea, bytea, text) TO postgres WITH GRANT OPTION;
GRANT ALL ON FUNCTION extensions.pgp_pub_decrypt_bytea(bytea, bytea, text) TO dashboard_user;


--
-- Name: FUNCTION pgp_pub_decrypt_bytea(bytea, bytea, text, text); Type: ACL; Schema: extensions; Owner: postgres
--

REVOKE ALL ON FUNCTION extensions.pgp_pub_decrypt_bytea(bytea, bytea, text, text) FROM postgres;
GRANT ALL ON FUNCTION extensions.pgp_pub_decrypt_bytea(bytea, bytea, text, text) TO postgres WITH GRANT OPTION;
GRANT ALL ON FUNCTION extensions.pgp_pub_decrypt_bytea(bytea, bytea, text, text) TO dashboard_user;


--
-- Name: FUNCTION pgp_pub_encrypt(text, bytea); Type: ACL; Schema: extensions; Owner: postgres
--

REVOKE ALL ON FUNCTION extensions.pgp_pub_encrypt(text, bytea) FROM postgres;
GRANT ALL ON FUNCTION extensions.pgp_pub_encrypt(text, bytea) TO postgres WITH GRANT OPTION;
GRANT ALL ON FUNCTION extensions.pgp_pub_encrypt(text, bytea) TO dashboard_user;


--
-- Name: FUNCTION pgp_pub_encrypt(text, bytea, text); Type: ACL; Schema: extensions; Owner: postgres
--

REVOKE ALL ON FUNCTION extensions.pgp_pub_encrypt(text, bytea, text) FROM postgres;
GRANT ALL ON FUNCTION extensions.pgp_pub_encrypt(text, bytea, text) TO postgres WITH GRANT OPTION;
GRANT ALL ON FUNCTION extensions.pgp_pub_encrypt(text, bytea, text) TO dashboard_user;


--
-- Name: FUNCTION pgp_pub_encrypt_bytea(bytea, bytea); Type: ACL; Schema: extensions; Owner: postgres
--

REVOKE ALL ON FUNCTION extensions.pgp_pub_encrypt_bytea(bytea, bytea) FROM postgres;
GRANT ALL ON FUNCTION extensions.pgp_pub_encrypt_bytea(bytea, bytea) TO postgres WITH GRANT OPTION;
GRANT ALL ON FUNCTION extensions.pgp_pub_encrypt_bytea(bytea, bytea) TO dashboard_user;


--
-- Name: FUNCTION pgp_pub_encrypt_bytea(bytea, bytea, text); Type: ACL; Schema: extensions; Owner: postgres
--

REVOKE ALL ON FUNCTION extensions.pgp_pub_encrypt_bytea(bytea, bytea, text) FROM postgres;
GRANT ALL ON FUNCTION extensions.pgp_pub_encrypt_bytea(bytea, bytea, text) TO postgres WITH GRANT OPTION;
GRANT ALL ON FUNCTION extensions.pgp_pub_encrypt_bytea(bytea, bytea, text) TO dashboard_user;


--
-- Name: FUNCTION pgp_sym_decrypt(bytea, text); Type: ACL; Schema: extensions; Owner: postgres
--

REVOKE ALL ON FUNCTION extensions.pgp_sym_decrypt(bytea, text) FROM postgres;
GRANT ALL ON FUNCTION extensions.pgp_sym_decrypt(bytea, text) TO postgres WITH GRANT OPTION;
GRANT ALL ON FUNCTION extensions.pgp_sym_decrypt(bytea, text) TO dashboard_user;


--
-- Name: FUNCTION pgp_sym_decrypt(bytea, text, text); Type: ACL; Schema: extensions; Owner: postgres
--

REVOKE ALL ON FUNCTION extensions.pgp_sym_decrypt(bytea, text, text) FROM postgres;
GRANT ALL ON FUNCTION extensions.pgp_sym_decrypt(bytea, text, text) TO postgres WITH GRANT OPTION;
GRANT ALL ON FUNCTION extensions.pgp_sym_decrypt(bytea, text, text) TO dashboard_user;


--
-- Name: FUNCTION pgp_sym_decrypt_bytea(bytea, text); Type: ACL; Schema: extensions; Owner: postgres
--

REVOKE ALL ON FUNCTION extensions.pgp_sym_decrypt_bytea(bytea, text) FROM postgres;
GRANT ALL ON FUNCTION extensions.pgp_sym_decrypt_bytea(bytea, text) TO postgres WITH GRANT OPTION;
GRANT ALL ON FUNCTION extensions.pgp_sym_decrypt_bytea(bytea, text) TO dashboard_user;


--
-- Name: FUNCTION pgp_sym_decrypt_bytea(bytea, text, text); Type: ACL; Schema: extensions; Owner: postgres
--

REVOKE ALL ON FUNCTION extensions.pgp_sym_decrypt_bytea(bytea, text, text) FROM postgres;
GRANT ALL ON FUNCTION extensions.pgp_sym_decrypt_bytea(bytea, text, text) TO postgres WITH GRANT OPTION;
GRANT ALL ON FUNCTION extensions.pgp_sym_decrypt_bytea(bytea, text, text) TO dashboard_user;


--
-- Name: FUNCTION pgp_sym_encrypt(text, text); Type: ACL; Schema: extensions; Owner: postgres
--

REVOKE ALL ON FUNCTION extensions.pgp_sym_encrypt(text, text) FROM postgres;
GRANT ALL ON FUNCTION extensions.pgp_sym_encrypt(text, text) TO postgres WITH GRANT OPTION;
GRANT ALL ON FUNCTION extensions.pgp_sym_encrypt(text, text) TO dashboard_user;


--
-- Name: FUNCTION pgp_sym_encrypt(text, text, text); Type: ACL; Schema: extensions; Owner: postgres
--

REVOKE ALL ON FUNCTION extensions.pgp_sym_encrypt(text, text, text) FROM postgres;
GRANT ALL ON FUNCTION extensions.pgp_sym_encrypt(text, text, text) TO postgres WITH GRANT OPTION;
GRANT ALL ON FUNCTION extensions.pgp_sym_encrypt(text, text, text) TO dashboard_user;


--
-- Name: FUNCTION pgp_sym_encrypt_bytea(bytea, text); Type: ACL; Schema: extensions; Owner: postgres
--

REVOKE ALL ON FUNCTION extensions.pgp_sym_encrypt_bytea(bytea, text) FROM postgres;
GRANT ALL ON FUNCTION extensions.pgp_sym_encrypt_bytea(bytea, text) TO postgres WITH GRANT OPTION;
GRANT ALL ON FUNCTION extensions.pgp_sym_encrypt_bytea(bytea, text) TO dashboard_user;


--
-- Name: FUNCTION pgp_sym_encrypt_bytea(bytea, text, text); Type: ACL; Schema: extensions; Owner: postgres
--

REVOKE ALL ON FUNCTION extensions.pgp_sym_encrypt_bytea(bytea, text, text) FROM postgres;
GRANT ALL ON FUNCTION extensions.pgp_sym_encrypt_bytea(bytea, text, text) TO postgres WITH GRANT OPTION;
GRANT ALL ON FUNCTION extensions.pgp_sym_encrypt_bytea(bytea, text, text) TO dashboard_user;


--
-- Name: FUNCTION pgrst_ddl_watch(); Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON FUNCTION extensions.pgrst_ddl_watch() TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION pgrst_drop_watch(); Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON FUNCTION extensions.pgrst_drop_watch() TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION set_graphql_placeholder(); Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON FUNCTION extensions.set_graphql_placeholder() TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION sign(payload json, secret text, algorithm text); Type: ACL; Schema: extensions; Owner: postgres
--

REVOKE ALL ON FUNCTION extensions.sign(payload json, secret text, algorithm text) FROM postgres;
GRANT ALL ON FUNCTION extensions.sign(payload json, secret text, algorithm text) TO postgres WITH GRANT OPTION;
GRANT ALL ON FUNCTION extensions.sign(payload json, secret text, algorithm text) TO dashboard_user;


--
-- Name: FUNCTION try_cast_double(inp text); Type: ACL; Schema: extensions; Owner: postgres
--

REVOKE ALL ON FUNCTION extensions.try_cast_double(inp text) FROM postgres;
GRANT ALL ON FUNCTION extensions.try_cast_double(inp text) TO postgres WITH GRANT OPTION;
GRANT ALL ON FUNCTION extensions.try_cast_double(inp text) TO dashboard_user;


--
-- Name: FUNCTION url_decode(data text); Type: ACL; Schema: extensions; Owner: postgres
--

REVOKE ALL ON FUNCTION extensions.url_decode(data text) FROM postgres;
GRANT ALL ON FUNCTION extensions.url_decode(data text) TO postgres WITH GRANT OPTION;
GRANT ALL ON FUNCTION extensions.url_decode(data text) TO dashboard_user;


--
-- Name: FUNCTION url_encode(data bytea); Type: ACL; Schema: extensions; Owner: postgres
--

REVOKE ALL ON FUNCTION extensions.url_encode(data bytea) FROM postgres;
GRANT ALL ON FUNCTION extensions.url_encode(data bytea) TO postgres WITH GRANT OPTION;
GRANT ALL ON FUNCTION extensions.url_encode(data bytea) TO dashboard_user;


--
-- Name: FUNCTION uuid_generate_v1(); Type: ACL; Schema: extensions; Owner: postgres
--

REVOKE ALL ON FUNCTION extensions.uuid_generate_v1() FROM postgres;
GRANT ALL ON FUNCTION extensions.uuid_generate_v1() TO postgres WITH GRANT OPTION;
GRANT ALL ON FUNCTION extensions.uuid_generate_v1() TO dashboard_user;


--
-- Name: FUNCTION uuid_generate_v1mc(); Type: ACL; Schema: extensions; Owner: postgres
--

REVOKE ALL ON FUNCTION extensions.uuid_generate_v1mc() FROM postgres;
GRANT ALL ON FUNCTION extensions.uuid_generate_v1mc() TO postgres WITH GRANT OPTION;
GRANT ALL ON FUNCTION extensions.uuid_generate_v1mc() TO dashboard_user;


--
-- Name: FUNCTION uuid_generate_v3(namespace uuid, name text); Type: ACL; Schema: extensions; Owner: postgres
--

REVOKE ALL ON FUNCTION extensions.uuid_generate_v3(namespace uuid, name text) FROM postgres;
GRANT ALL ON FUNCTION extensions.uuid_generate_v3(namespace uuid, name text) TO postgres WITH GRANT OPTION;
GRANT ALL ON FUNCTION extensions.uuid_generate_v3(namespace uuid, name text) TO dashboard_user;


--
-- Name: FUNCTION uuid_generate_v4(); Type: ACL; Schema: extensions; Owner: postgres
--

REVOKE ALL ON FUNCTION extensions.uuid_generate_v4() FROM postgres;
GRANT ALL ON FUNCTION extensions.uuid_generate_v4() TO postgres WITH GRANT OPTION;
GRANT ALL ON FUNCTION extensions.uuid_generate_v4() TO dashboard_user;


--
-- Name: FUNCTION uuid_generate_v5(namespace uuid, name text); Type: ACL; Schema: extensions; Owner: postgres
--

REVOKE ALL ON FUNCTION extensions.uuid_generate_v5(namespace uuid, name text) FROM postgres;
GRANT ALL ON FUNCTION extensions.uuid_generate_v5(namespace uuid, name text) TO postgres WITH GRANT OPTION;
GRANT ALL ON FUNCTION extensions.uuid_generate_v5(namespace uuid, name text) TO dashboard_user;


--
-- Name: FUNCTION uuid_nil(); Type: ACL; Schema: extensions; Owner: postgres
--

REVOKE ALL ON FUNCTION extensions.uuid_nil() FROM postgres;
GRANT ALL ON FUNCTION extensions.uuid_nil() TO postgres WITH GRANT OPTION;
GRANT ALL ON FUNCTION extensions.uuid_nil() TO dashboard_user;


--
-- Name: FUNCTION uuid_ns_dns(); Type: ACL; Schema: extensions; Owner: postgres
--

REVOKE ALL ON FUNCTION extensions.uuid_ns_dns() FROM postgres;
GRANT ALL ON FUNCTION extensions.uuid_ns_dns() TO postgres WITH GRANT OPTION;
GRANT ALL ON FUNCTION extensions.uuid_ns_dns() TO dashboard_user;


--
-- Name: FUNCTION uuid_ns_oid(); Type: ACL; Schema: extensions; Owner: postgres
--

REVOKE ALL ON FUNCTION extensions.uuid_ns_oid() FROM postgres;
GRANT ALL ON FUNCTION extensions.uuid_ns_oid() TO postgres WITH GRANT OPTION;
GRANT ALL ON FUNCTION extensions.uuid_ns_oid() TO dashboard_user;


--
-- Name: FUNCTION uuid_ns_url(); Type: ACL; Schema: extensions; Owner: postgres
--

REVOKE ALL ON FUNCTION extensions.uuid_ns_url() FROM postgres;
GRANT ALL ON FUNCTION extensions.uuid_ns_url() TO postgres WITH GRANT OPTION;
GRANT ALL ON FUNCTION extensions.uuid_ns_url() TO dashboard_user;


--
-- Name: FUNCTION uuid_ns_x500(); Type: ACL; Schema: extensions; Owner: postgres
--

REVOKE ALL ON FUNCTION extensions.uuid_ns_x500() FROM postgres;
GRANT ALL ON FUNCTION extensions.uuid_ns_x500() TO postgres WITH GRANT OPTION;
GRANT ALL ON FUNCTION extensions.uuid_ns_x500() TO dashboard_user;


--
-- Name: FUNCTION verify(token text, secret text, algorithm text); Type: ACL; Schema: extensions; Owner: postgres
--

REVOKE ALL ON FUNCTION extensions.verify(token text, secret text, algorithm text) FROM postgres;
GRANT ALL ON FUNCTION extensions.verify(token text, secret text, algorithm text) TO postgres WITH GRANT OPTION;
GRANT ALL ON FUNCTION extensions.verify(token text, secret text, algorithm text) TO dashboard_user;


--
-- Name: FUNCTION comment_directive(comment_ text); Type: ACL; Schema: graphql; Owner: supabase_admin
--

GRANT ALL ON FUNCTION graphql.comment_directive(comment_ text) TO postgres;
GRANT ALL ON FUNCTION graphql.comment_directive(comment_ text) TO anon;
GRANT ALL ON FUNCTION graphql.comment_directive(comment_ text) TO authenticated;
GRANT ALL ON FUNCTION graphql.comment_directive(comment_ text) TO service_role;


--
-- Name: FUNCTION exception(message text); Type: ACL; Schema: graphql; Owner: supabase_admin
--

GRANT ALL ON FUNCTION graphql.exception(message text) TO postgres;
GRANT ALL ON FUNCTION graphql.exception(message text) TO anon;
GRANT ALL ON FUNCTION graphql.exception(message text) TO authenticated;
GRANT ALL ON FUNCTION graphql.exception(message text) TO service_role;


--
-- Name: FUNCTION get_schema_version(); Type: ACL; Schema: graphql; Owner: supabase_admin
--

GRANT ALL ON FUNCTION graphql.get_schema_version() TO postgres;
GRANT ALL ON FUNCTION graphql.get_schema_version() TO anon;
GRANT ALL ON FUNCTION graphql.get_schema_version() TO authenticated;
GRANT ALL ON FUNCTION graphql.get_schema_version() TO service_role;


--
-- Name: FUNCTION increment_schema_version(); Type: ACL; Schema: graphql; Owner: supabase_admin
--

GRANT ALL ON FUNCTION graphql.increment_schema_version() TO postgres;
GRANT ALL ON FUNCTION graphql.increment_schema_version() TO anon;
GRANT ALL ON FUNCTION graphql.increment_schema_version() TO authenticated;
GRANT ALL ON FUNCTION graphql.increment_schema_version() TO service_role;


--
-- Name: FUNCTION graphql("operationName" text, query text, variables jsonb, extensions jsonb); Type: ACL; Schema: graphql_public; Owner: supabase_admin
--

GRANT ALL ON FUNCTION graphql_public.graphql("operationName" text, query text, variables jsonb, extensions jsonb) TO postgres;
GRANT ALL ON FUNCTION graphql_public.graphql("operationName" text, query text, variables jsonb, extensions jsonb) TO anon;
GRANT ALL ON FUNCTION graphql_public.graphql("operationName" text, query text, variables jsonb, extensions jsonb) TO authenticated;
GRANT ALL ON FUNCTION graphql_public.graphql("operationName" text, query text, variables jsonb, extensions jsonb) TO service_role;


--
-- Name: FUNCTION http_get(url text, params jsonb, headers jsonb, timeout_milliseconds integer); Type: ACL; Schema: net; Owner: supabase_admin
--

REVOKE ALL ON FUNCTION net.http_get(url text, params jsonb, headers jsonb, timeout_milliseconds integer) FROM PUBLIC;
GRANT ALL ON FUNCTION net.http_get(url text, params jsonb, headers jsonb, timeout_milliseconds integer) TO supabase_functions_admin;
GRANT ALL ON FUNCTION net.http_get(url text, params jsonb, headers jsonb, timeout_milliseconds integer) TO postgres;
GRANT ALL ON FUNCTION net.http_get(url text, params jsonb, headers jsonb, timeout_milliseconds integer) TO anon;
GRANT ALL ON FUNCTION net.http_get(url text, params jsonb, headers jsonb, timeout_milliseconds integer) TO authenticated;
GRANT ALL ON FUNCTION net.http_get(url text, params jsonb, headers jsonb, timeout_milliseconds integer) TO service_role;


--
-- Name: FUNCTION http_post(url text, body jsonb, params jsonb, headers jsonb, timeout_milliseconds integer); Type: ACL; Schema: net; Owner: supabase_admin
--

REVOKE ALL ON FUNCTION net.http_post(url text, body jsonb, params jsonb, headers jsonb, timeout_milliseconds integer) FROM PUBLIC;
GRANT ALL ON FUNCTION net.http_post(url text, body jsonb, params jsonb, headers jsonb, timeout_milliseconds integer) TO supabase_functions_admin;
GRANT ALL ON FUNCTION net.http_post(url text, body jsonb, params jsonb, headers jsonb, timeout_milliseconds integer) TO postgres;
GRANT ALL ON FUNCTION net.http_post(url text, body jsonb, params jsonb, headers jsonb, timeout_milliseconds integer) TO anon;
GRANT ALL ON FUNCTION net.http_post(url text, body jsonb, params jsonb, headers jsonb, timeout_milliseconds integer) TO authenticated;
GRANT ALL ON FUNCTION net.http_post(url text, body jsonb, params jsonb, headers jsonb, timeout_milliseconds integer) TO service_role;


--
-- Name: FUNCTION lo_export(oid, text); Type: ACL; Schema: pg_catalog; Owner: supabase_admin
--

REVOKE ALL ON FUNCTION pg_catalog.lo_export(oid, text) FROM postgres;
GRANT ALL ON FUNCTION pg_catalog.lo_export(oid, text) TO supabase_admin;


--
-- Name: FUNCTION lo_import(text); Type: ACL; Schema: pg_catalog; Owner: supabase_admin
--

REVOKE ALL ON FUNCTION pg_catalog.lo_import(text) FROM postgres;
GRANT ALL ON FUNCTION pg_catalog.lo_import(text) TO supabase_admin;


--
-- Name: FUNCTION lo_import(text, oid); Type: ACL; Schema: pg_catalog; Owner: supabase_admin
--

REVOKE ALL ON FUNCTION pg_catalog.lo_import(text, oid) FROM postgres;
GRANT ALL ON FUNCTION pg_catalog.lo_import(text, oid) TO supabase_admin;


--
-- Name: FUNCTION get_auth(p_usename text); Type: ACL; Schema: pgbouncer; Owner: postgres
--

REVOKE ALL ON FUNCTION pgbouncer.get_auth(p_usename text) FROM PUBLIC;
GRANT ALL ON FUNCTION pgbouncer.get_auth(p_usename text) TO pgbouncer;


--
-- Name: FUNCTION crypto_aead_det_decrypt(message bytea, additional bytea, key_uuid uuid, nonce bytea); Type: ACL; Schema: pgsodium; Owner: pgsodium_keymaker
--

GRANT ALL ON FUNCTION pgsodium.crypto_aead_det_decrypt(message bytea, additional bytea, key_uuid uuid, nonce bytea) TO service_role;


--
-- Name: FUNCTION crypto_aead_det_encrypt(message bytea, additional bytea, key_uuid uuid, nonce bytea); Type: ACL; Schema: pgsodium; Owner: pgsodium_keymaker
--

GRANT ALL ON FUNCTION pgsodium.crypto_aead_det_encrypt(message bytea, additional bytea, key_uuid uuid, nonce bytea) TO service_role;


--
-- Name: FUNCTION crypto_aead_det_keygen(); Type: ACL; Schema: pgsodium; Owner: supabase_admin
--

GRANT ALL ON FUNCTION pgsodium.crypto_aead_det_keygen() TO service_role;


--
-- Name: FUNCTION get_platform_from_url(url text); Type: ACL; Schema: public; Owner: postgres
--

GRANT ALL ON FUNCTION public.get_platform_from_url(url text) TO anon;
GRANT ALL ON FUNCTION public.get_platform_from_url(url text) TO authenticated;
GRANT ALL ON FUNCTION public.get_platform_from_url(url text) TO service_role;


--
-- Name: FUNCTION update_platform_on_insert(); Type: ACL; Schema: public; Owner: postgres
--

GRANT ALL ON FUNCTION public.update_platform_on_insert() TO anon;
GRANT ALL ON FUNCTION public.update_platform_on_insert() TO authenticated;
GRANT ALL ON FUNCTION public.update_platform_on_insert() TO service_role;


--
-- Name: FUNCTION apply_rls(wal jsonb, max_record_bytes integer); Type: ACL; Schema: realtime; Owner: supabase_admin
--

GRANT ALL ON FUNCTION realtime.apply_rls(wal jsonb, max_record_bytes integer) TO postgres;
GRANT ALL ON FUNCTION realtime.apply_rls(wal jsonb, max_record_bytes integer) TO dashboard_user;
GRANT ALL ON FUNCTION realtime.apply_rls(wal jsonb, max_record_bytes integer) TO anon;
GRANT ALL ON FUNCTION realtime.apply_rls(wal jsonb, max_record_bytes integer) TO authenticated;
GRANT ALL ON FUNCTION realtime.apply_rls(wal jsonb, max_record_bytes integer) TO service_role;
GRANT ALL ON FUNCTION realtime.apply_rls(wal jsonb, max_record_bytes integer) TO supabase_realtime_admin;


--
-- Name: FUNCTION build_prepared_statement_sql(prepared_statement_name text, entity regclass, columns realtime.wal_column[]); Type: ACL; Schema: realtime; Owner: supabase_admin
--

GRANT ALL ON FUNCTION realtime.build_prepared_statement_sql(prepared_statement_name text, entity regclass, columns realtime.wal_column[]) TO postgres;
GRANT ALL ON FUNCTION realtime.build_prepared_statement_sql(prepared_statement_name text, entity regclass, columns realtime.wal_column[]) TO dashboard_user;
GRANT ALL ON FUNCTION realtime.build_prepared_statement_sql(prepared_statement_name text, entity regclass, columns realtime.wal_column[]) TO anon;
GRANT ALL ON FUNCTION realtime.build_prepared_statement_sql(prepared_statement_name text, entity regclass, columns realtime.wal_column[]) TO authenticated;
GRANT ALL ON FUNCTION realtime.build_prepared_statement_sql(prepared_statement_name text, entity regclass, columns realtime.wal_column[]) TO service_role;
GRANT ALL ON FUNCTION realtime.build_prepared_statement_sql(prepared_statement_name text, entity regclass, columns realtime.wal_column[]) TO supabase_realtime_admin;


--
-- Name: FUNCTION "cast"(val text, type_ regtype); Type: ACL; Schema: realtime; Owner: supabase_admin
--

GRANT ALL ON FUNCTION realtime."cast"(val text, type_ regtype) TO postgres;
GRANT ALL ON FUNCTION realtime."cast"(val text, type_ regtype) TO dashboard_user;
GRANT ALL ON FUNCTION realtime."cast"(val text, type_ regtype) TO anon;
GRANT ALL ON FUNCTION realtime."cast"(val text, type_ regtype) TO authenticated;
GRANT ALL ON FUNCTION realtime."cast"(val text, type_ regtype) TO service_role;
GRANT ALL ON FUNCTION realtime."cast"(val text, type_ regtype) TO supabase_realtime_admin;


--
-- Name: FUNCTION channel_name(); Type: ACL; Schema: realtime; Owner: supabase_realtime_admin
--

GRANT ALL ON FUNCTION realtime.channel_name() TO postgres;
GRANT ALL ON FUNCTION realtime.channel_name() TO dashboard_user;


--
-- Name: FUNCTION check_equality_op(op realtime.equality_op, type_ regtype, val_1 text, val_2 text); Type: ACL; Schema: realtime; Owner: supabase_admin
--

GRANT ALL ON FUNCTION realtime.check_equality_op(op realtime.equality_op, type_ regtype, val_1 text, val_2 text) TO postgres;
GRANT ALL ON FUNCTION realtime.check_equality_op(op realtime.equality_op, type_ regtype, val_1 text, val_2 text) TO dashboard_user;
GRANT ALL ON FUNCTION realtime.check_equality_op(op realtime.equality_op, type_ regtype, val_1 text, val_2 text) TO anon;
GRANT ALL ON FUNCTION realtime.check_equality_op(op realtime.equality_op, type_ regtype, val_1 text, val_2 text) TO authenticated;
GRANT ALL ON FUNCTION realtime.check_equality_op(op realtime.equality_op, type_ regtype, val_1 text, val_2 text) TO service_role;
GRANT ALL ON FUNCTION realtime.check_equality_op(op realtime.equality_op, type_ regtype, val_1 text, val_2 text) TO supabase_realtime_admin;


--
-- Name: FUNCTION is_visible_through_filters(columns realtime.wal_column[], filters realtime.user_defined_filter[]); Type: ACL; Schema: realtime; Owner: supabase_admin
--

GRANT ALL ON FUNCTION realtime.is_visible_through_filters(columns realtime.wal_column[], filters realtime.user_defined_filter[]) TO postgres;
GRANT ALL ON FUNCTION realtime.is_visible_through_filters(columns realtime.wal_column[], filters realtime.user_defined_filter[]) TO dashboard_user;
GRANT ALL ON FUNCTION realtime.is_visible_through_filters(columns realtime.wal_column[], filters realtime.user_defined_filter[]) TO anon;
GRANT ALL ON FUNCTION realtime.is_visible_through_filters(columns realtime.wal_column[], filters realtime.user_defined_filter[]) TO authenticated;
GRANT ALL ON FUNCTION realtime.is_visible_through_filters(columns realtime.wal_column[], filters realtime.user_defined_filter[]) TO service_role;
GRANT ALL ON FUNCTION realtime.is_visible_through_filters(columns realtime.wal_column[], filters realtime.user_defined_filter[]) TO supabase_realtime_admin;


--
-- Name: FUNCTION list_changes(publication name, slot_name name, max_changes integer, max_record_bytes integer); Type: ACL; Schema: realtime; Owner: supabase_admin
--

GRANT ALL ON FUNCTION realtime.list_changes(publication name, slot_name name, max_changes integer, max_record_bytes integer) TO postgres;
GRANT ALL ON FUNCTION realtime.list_changes(publication name, slot_name name, max_changes integer, max_record_bytes integer) TO dashboard_user;
GRANT ALL ON FUNCTION realtime.list_changes(publication name, slot_name name, max_changes integer, max_record_bytes integer) TO anon;
GRANT ALL ON FUNCTION realtime.list_changes(publication name, slot_name name, max_changes integer, max_record_bytes integer) TO authenticated;
GRANT ALL ON FUNCTION realtime.list_changes(publication name, slot_name name, max_changes integer, max_record_bytes integer) TO service_role;
GRANT ALL ON FUNCTION realtime.list_changes(publication name, slot_name name, max_changes integer, max_record_bytes integer) TO supabase_realtime_admin;


--
-- Name: FUNCTION quote_wal2json(entity regclass); Type: ACL; Schema: realtime; Owner: supabase_admin
--

GRANT ALL ON FUNCTION realtime.quote_wal2json(entity regclass) TO postgres;
GRANT ALL ON FUNCTION realtime.quote_wal2json(entity regclass) TO dashboard_user;
GRANT ALL ON FUNCTION realtime.quote_wal2json(entity regclass) TO anon;
GRANT ALL ON FUNCTION realtime.quote_wal2json(entity regclass) TO authenticated;
GRANT ALL ON FUNCTION realtime.quote_wal2json(entity regclass) TO service_role;
GRANT ALL ON FUNCTION realtime.quote_wal2json(entity regclass) TO supabase_realtime_admin;


--
-- Name: FUNCTION subscription_check_filters(); Type: ACL; Schema: realtime; Owner: supabase_admin
--

GRANT ALL ON FUNCTION realtime.subscription_check_filters() TO postgres;
GRANT ALL ON FUNCTION realtime.subscription_check_filters() TO dashboard_user;
GRANT ALL ON FUNCTION realtime.subscription_check_filters() TO anon;
GRANT ALL ON FUNCTION realtime.subscription_check_filters() TO authenticated;
GRANT ALL ON FUNCTION realtime.subscription_check_filters() TO service_role;
GRANT ALL ON FUNCTION realtime.subscription_check_filters() TO supabase_realtime_admin;


--
-- Name: FUNCTION to_regrole(role_name text); Type: ACL; Schema: realtime; Owner: supabase_admin
--

GRANT ALL ON FUNCTION realtime.to_regrole(role_name text) TO postgres;
GRANT ALL ON FUNCTION realtime.to_regrole(role_name text) TO dashboard_user;
GRANT ALL ON FUNCTION realtime.to_regrole(role_name text) TO anon;
GRANT ALL ON FUNCTION realtime.to_regrole(role_name text) TO authenticated;
GRANT ALL ON FUNCTION realtime.to_regrole(role_name text) TO service_role;
GRANT ALL ON FUNCTION realtime.to_regrole(role_name text) TO supabase_realtime_admin;


--
-- Name: FUNCTION http_request(); Type: ACL; Schema: supabase_functions; Owner: supabase_functions_admin
--

REVOKE ALL ON FUNCTION supabase_functions.http_request() FROM PUBLIC;
GRANT ALL ON FUNCTION supabase_functions.http_request() TO postgres;
GRANT ALL ON FUNCTION supabase_functions.http_request() TO anon;
GRANT ALL ON FUNCTION supabase_functions.http_request() TO authenticated;
GRANT ALL ON FUNCTION supabase_functions.http_request() TO service_role;


--
-- Name: TABLE audit_log_entries; Type: ACL; Schema: auth; Owner: supabase_auth_admin
--

GRANT ALL ON TABLE auth.audit_log_entries TO dashboard_user;
GRANT ALL ON TABLE auth.audit_log_entries TO postgres;


--
-- Name: TABLE flow_state; Type: ACL; Schema: auth; Owner: supabase_auth_admin
--

GRANT ALL ON TABLE auth.flow_state TO postgres;
GRANT ALL ON TABLE auth.flow_state TO dashboard_user;


--
-- Name: TABLE identities; Type: ACL; Schema: auth; Owner: supabase_auth_admin
--

GRANT ALL ON TABLE auth.identities TO postgres;
GRANT ALL ON TABLE auth.identities TO dashboard_user;


--
-- Name: TABLE instances; Type: ACL; Schema: auth; Owner: supabase_auth_admin
--

GRANT ALL ON TABLE auth.instances TO dashboard_user;
GRANT ALL ON TABLE auth.instances TO postgres;


--
-- Name: TABLE mfa_amr_claims; Type: ACL; Schema: auth; Owner: supabase_auth_admin
--

GRANT ALL ON TABLE auth.mfa_amr_claims TO postgres;
GRANT ALL ON TABLE auth.mfa_amr_claims TO dashboard_user;


--
-- Name: TABLE mfa_challenges; Type: ACL; Schema: auth; Owner: supabase_auth_admin
--

GRANT ALL ON TABLE auth.mfa_challenges TO postgres;
GRANT ALL ON TABLE auth.mfa_challenges TO dashboard_user;


--
-- Name: TABLE mfa_factors; Type: ACL; Schema: auth; Owner: supabase_auth_admin
--

GRANT ALL ON TABLE auth.mfa_factors TO postgres;
GRANT ALL ON TABLE auth.mfa_factors TO dashboard_user;


--
-- Name: TABLE refresh_tokens; Type: ACL; Schema: auth; Owner: supabase_auth_admin
--

GRANT ALL ON TABLE auth.refresh_tokens TO dashboard_user;
GRANT ALL ON TABLE auth.refresh_tokens TO postgres;


--
-- Name: SEQUENCE refresh_tokens_id_seq; Type: ACL; Schema: auth; Owner: supabase_auth_admin
--

GRANT ALL ON SEQUENCE auth.refresh_tokens_id_seq TO dashboard_user;
GRANT ALL ON SEQUENCE auth.refresh_tokens_id_seq TO postgres;


--
-- Name: TABLE saml_providers; Type: ACL; Schema: auth; Owner: supabase_auth_admin
--

GRANT ALL ON TABLE auth.saml_providers TO postgres;
GRANT ALL ON TABLE auth.saml_providers TO dashboard_user;


--
-- Name: TABLE saml_relay_states; Type: ACL; Schema: auth; Owner: supabase_auth_admin
--

GRANT ALL ON TABLE auth.saml_relay_states TO postgres;
GRANT ALL ON TABLE auth.saml_relay_states TO dashboard_user;


--
-- Name: TABLE schema_migrations; Type: ACL; Schema: auth; Owner: supabase_auth_admin
--

GRANT ALL ON TABLE auth.schema_migrations TO dashboard_user;
GRANT ALL ON TABLE auth.schema_migrations TO postgres;


--
-- Name: TABLE sessions; Type: ACL; Schema: auth; Owner: supabase_auth_admin
--

GRANT ALL ON TABLE auth.sessions TO postgres;
GRANT ALL ON TABLE auth.sessions TO dashboard_user;


--
-- Name: TABLE sso_domains; Type: ACL; Schema: auth; Owner: supabase_auth_admin
--

GRANT ALL ON TABLE auth.sso_domains TO postgres;
GRANT ALL ON TABLE auth.sso_domains TO dashboard_user;


--
-- Name: TABLE sso_providers; Type: ACL; Schema: auth; Owner: supabase_auth_admin
--

GRANT ALL ON TABLE auth.sso_providers TO postgres;
GRANT ALL ON TABLE auth.sso_providers TO dashboard_user;


--
-- Name: TABLE users; Type: ACL; Schema: auth; Owner: supabase_auth_admin
--

GRANT ALL ON TABLE auth.users TO dashboard_user;
GRANT ALL ON TABLE auth.users TO postgres;


--
-- Name: TABLE pg_stat_statements; Type: ACL; Schema: extensions; Owner: postgres
--

REVOKE ALL ON TABLE extensions.pg_stat_statements FROM postgres;
GRANT ALL ON TABLE extensions.pg_stat_statements TO postgres WITH GRANT OPTION;
GRANT ALL ON TABLE extensions.pg_stat_statements TO dashboard_user;


--
-- Name: TABLE pg_stat_statements_info; Type: ACL; Schema: extensions; Owner: postgres
--

REVOKE ALL ON TABLE extensions.pg_stat_statements_info FROM postgres;
GRANT ALL ON TABLE extensions.pg_stat_statements_info TO postgres WITH GRANT OPTION;
GRANT ALL ON TABLE extensions.pg_stat_statements_info TO dashboard_user;


--
-- Name: SEQUENCE seq_schema_version; Type: ACL; Schema: graphql; Owner: supabase_admin
--

GRANT ALL ON SEQUENCE graphql.seq_schema_version TO postgres;
GRANT ALL ON SEQUENCE graphql.seq_schema_version TO anon;
GRANT ALL ON SEQUENCE graphql.seq_schema_version TO authenticated;
GRANT ALL ON SEQUENCE graphql.seq_schema_version TO service_role;


--
-- Name: TABLE decrypted_key; Type: ACL; Schema: pgsodium; Owner: supabase_admin
--

GRANT ALL ON TABLE pgsodium.decrypted_key TO pgsodium_keyholder;


--
-- Name: TABLE masking_rule; Type: ACL; Schema: pgsodium; Owner: supabase_admin
--

GRANT ALL ON TABLE pgsodium.masking_rule TO pgsodium_keyholder;


--
-- Name: TABLE mask_columns; Type: ACL; Schema: pgsodium; Owner: supabase_admin
--

GRANT ALL ON TABLE pgsodium.mask_columns TO pgsodium_keyholder;


--
-- Name: TABLE anchors; Type: ACL; Schema: public; Owner: postgres
--

GRANT ALL ON TABLE public.anchors TO anon;
GRANT ALL ON TABLE public.anchors TO authenticated;
GRANT ALL ON TABLE public.anchors TO service_role;


--
-- Name: TABLE file; Type: ACL; Schema: public; Owner: postgres
--

GRANT ALL ON TABLE public.file TO anon;
GRANT ALL ON TABLE public.file TO authenticated;
GRANT ALL ON TABLE public.file TO service_role;


--
-- Name: TABLE user_subscriptions; Type: ACL; Schema: public; Owner: postgres
--

GRANT ALL ON TABLE public.user_subscriptions TO anon;
GRANT ALL ON TABLE public.user_subscriptions TO authenticated;
GRANT ALL ON TABLE public.user_subscriptions TO service_role;


--
-- Name: TABLE users; Type: ACL; Schema: public; Owner: postgres
--

GRANT ALL ON TABLE public.users TO anon;
GRANT ALL ON TABLE public.users TO authenticated;
GRANT ALL ON TABLE public.users TO service_role;


--
-- Name: SEQUENCE users_user_id_seq; Type: ACL; Schema: public; Owner: postgres
--

GRANT ALL ON SEQUENCE public.users_user_id_seq TO anon;
GRANT ALL ON SEQUENCE public.users_user_id_seq TO authenticated;
GRANT ALL ON SEQUENCE public.users_user_id_seq TO service_role;


--
-- Name: TABLE broadcasts; Type: ACL; Schema: realtime; Owner: supabase_realtime_admin
--

GRANT ALL ON TABLE realtime.broadcasts TO postgres;
GRANT ALL ON TABLE realtime.broadcasts TO dashboard_user;
GRANT SELECT,INSERT,UPDATE ON TABLE realtime.broadcasts TO anon;
GRANT SELECT,INSERT,UPDATE ON TABLE realtime.broadcasts TO authenticated;
GRANT SELECT,INSERT,UPDATE ON TABLE realtime.broadcasts TO service_role;


--
-- Name: SEQUENCE broadcasts_id_seq; Type: ACL; Schema: realtime; Owner: supabase_realtime_admin
--

GRANT ALL ON SEQUENCE realtime.broadcasts_id_seq TO postgres;
GRANT ALL ON SEQUENCE realtime.broadcasts_id_seq TO dashboard_user;
GRANT USAGE ON SEQUENCE realtime.broadcasts_id_seq TO anon;
GRANT USAGE ON SEQUENCE realtime.broadcasts_id_seq TO authenticated;
GRANT USAGE ON SEQUENCE realtime.broadcasts_id_seq TO service_role;


--
-- Name: TABLE channels; Type: ACL; Schema: realtime; Owner: supabase_realtime_admin
--

GRANT ALL ON TABLE realtime.channels TO postgres;
GRANT ALL ON TABLE realtime.channels TO dashboard_user;
GRANT SELECT,INSERT,DELETE,UPDATE ON TABLE realtime.channels TO anon;
GRANT SELECT,INSERT,DELETE,UPDATE ON TABLE realtime.channels TO authenticated;
GRANT SELECT,INSERT,DELETE,UPDATE ON TABLE realtime.channels TO service_role;


--
-- Name: SEQUENCE channels_id_seq; Type: ACL; Schema: realtime; Owner: supabase_realtime_admin
--

GRANT ALL ON SEQUENCE realtime.channels_id_seq TO postgres;
GRANT ALL ON SEQUENCE realtime.channels_id_seq TO dashboard_user;
GRANT USAGE ON SEQUENCE realtime.channels_id_seq TO anon;
GRANT USAGE ON SEQUENCE realtime.channels_id_seq TO authenticated;
GRANT USAGE ON SEQUENCE realtime.channels_id_seq TO service_role;


--
-- Name: TABLE presences; Type: ACL; Schema: realtime; Owner: supabase_realtime_admin
--

GRANT ALL ON TABLE realtime.presences TO postgres;
GRANT ALL ON TABLE realtime.presences TO dashboard_user;
GRANT SELECT,INSERT,UPDATE ON TABLE realtime.presences TO anon;
GRANT SELECT,INSERT,UPDATE ON TABLE realtime.presences TO authenticated;
GRANT SELECT,INSERT,UPDATE ON TABLE realtime.presences TO service_role;


--
-- Name: SEQUENCE presences_id_seq; Type: ACL; Schema: realtime; Owner: supabase_realtime_admin
--

GRANT ALL ON SEQUENCE realtime.presences_id_seq TO postgres;
GRANT ALL ON SEQUENCE realtime.presences_id_seq TO dashboard_user;
GRANT USAGE ON SEQUENCE realtime.presences_id_seq TO anon;
GRANT USAGE ON SEQUENCE realtime.presences_id_seq TO authenticated;
GRANT USAGE ON SEQUENCE realtime.presences_id_seq TO service_role;


--
-- Name: TABLE schema_migrations; Type: ACL; Schema: realtime; Owner: supabase_admin
--

GRANT ALL ON TABLE realtime.schema_migrations TO postgres;
GRANT ALL ON TABLE realtime.schema_migrations TO dashboard_user;
GRANT SELECT ON TABLE realtime.schema_migrations TO anon;
GRANT SELECT ON TABLE realtime.schema_migrations TO authenticated;
GRANT SELECT ON TABLE realtime.schema_migrations TO service_role;
GRANT ALL ON TABLE realtime.schema_migrations TO supabase_realtime_admin;


--
-- Name: TABLE subscription; Type: ACL; Schema: realtime; Owner: supabase_admin
--

GRANT ALL ON TABLE realtime.subscription TO postgres;
GRANT ALL ON TABLE realtime.subscription TO dashboard_user;
GRANT SELECT ON TABLE realtime.subscription TO anon;
GRANT SELECT ON TABLE realtime.subscription TO authenticated;
GRANT SELECT ON TABLE realtime.subscription TO service_role;
GRANT ALL ON TABLE realtime.subscription TO supabase_realtime_admin;


--
-- Name: SEQUENCE subscription_id_seq; Type: ACL; Schema: realtime; Owner: supabase_admin
--

GRANT ALL ON SEQUENCE realtime.subscription_id_seq TO postgres;
GRANT ALL ON SEQUENCE realtime.subscription_id_seq TO dashboard_user;
GRANT USAGE ON SEQUENCE realtime.subscription_id_seq TO anon;
GRANT USAGE ON SEQUENCE realtime.subscription_id_seq TO authenticated;
GRANT USAGE ON SEQUENCE realtime.subscription_id_seq TO service_role;
GRANT ALL ON SEQUENCE realtime.subscription_id_seq TO supabase_realtime_admin;


--
-- Name: TABLE buckets; Type: ACL; Schema: storage; Owner: supabase_storage_admin
--

GRANT ALL ON TABLE storage.buckets TO anon;
GRANT ALL ON TABLE storage.buckets TO authenticated;
GRANT ALL ON TABLE storage.buckets TO service_role;
GRANT ALL ON TABLE storage.buckets TO postgres;


--
-- Name: TABLE migrations; Type: ACL; Schema: storage; Owner: supabase_storage_admin
--

GRANT ALL ON TABLE storage.migrations TO anon;
GRANT ALL ON TABLE storage.migrations TO authenticated;
GRANT ALL ON TABLE storage.migrations TO service_role;
GRANT ALL ON TABLE storage.migrations TO postgres;


--
-- Name: TABLE objects; Type: ACL; Schema: storage; Owner: supabase_storage_admin
--

GRANT ALL ON TABLE storage.objects TO anon;
GRANT ALL ON TABLE storage.objects TO authenticated;
GRANT ALL ON TABLE storage.objects TO service_role;
GRANT ALL ON TABLE storage.objects TO postgres;


--
-- Name: TABLE hooks; Type: ACL; Schema: supabase_functions; Owner: supabase_functions_admin
--

GRANT ALL ON TABLE supabase_functions.hooks TO postgres;
GRANT ALL ON TABLE supabase_functions.hooks TO anon;
GRANT ALL ON TABLE supabase_functions.hooks TO authenticated;
GRANT ALL ON TABLE supabase_functions.hooks TO service_role;


--
-- Name: SEQUENCE hooks_id_seq; Type: ACL; Schema: supabase_functions; Owner: supabase_functions_admin
--

GRANT ALL ON SEQUENCE supabase_functions.hooks_id_seq TO postgres;
GRANT ALL ON SEQUENCE supabase_functions.hooks_id_seq TO anon;
GRANT ALL ON SEQUENCE supabase_functions.hooks_id_seq TO authenticated;
GRANT ALL ON SEQUENCE supabase_functions.hooks_id_seq TO service_role;


--
-- Name: TABLE migrations; Type: ACL; Schema: supabase_functions; Owner: supabase_functions_admin
--

GRANT ALL ON TABLE supabase_functions.migrations TO postgres;
GRANT ALL ON TABLE supabase_functions.migrations TO anon;
GRANT ALL ON TABLE supabase_functions.migrations TO authenticated;
GRANT ALL ON TABLE supabase_functions.migrations TO service_role;


--
-- Name: DEFAULT PRIVILEGES FOR SEQUENCES; Type: DEFAULT ACL; Schema: auth; Owner: supabase_auth_admin
--

ALTER DEFAULT PRIVILEGES FOR ROLE supabase_auth_admin IN SCHEMA auth GRANT ALL ON SEQUENCES  TO postgres;
ALTER DEFAULT PRIVILEGES FOR ROLE supabase_auth_admin IN SCHEMA auth GRANT ALL ON SEQUENCES  TO dashboard_user;


--
-- Name: DEFAULT PRIVILEGES FOR FUNCTIONS; Type: DEFAULT ACL; Schema: auth; Owner: supabase_auth_admin
--

ALTER DEFAULT PRIVILEGES FOR ROLE supabase_auth_admin IN SCHEMA auth GRANT ALL ON FUNCTIONS  TO postgres;
ALTER DEFAULT PRIVILEGES FOR ROLE supabase_auth_admin IN SCHEMA auth GRANT ALL ON FUNCTIONS  TO dashboard_user;


--
-- Name: DEFAULT PRIVILEGES FOR TABLES; Type: DEFAULT ACL; Schema: auth; Owner: supabase_auth_admin
--

ALTER DEFAULT PRIVILEGES FOR ROLE supabase_auth_admin IN SCHEMA auth GRANT ALL ON TABLES  TO postgres;
ALTER DEFAULT PRIVILEGES FOR ROLE supabase_auth_admin IN SCHEMA auth GRANT ALL ON TABLES  TO dashboard_user;


--
-- Name: DEFAULT PRIVILEGES FOR SEQUENCES; Type: DEFAULT ACL; Schema: extensions; Owner: supabase_admin
--

ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA extensions GRANT ALL ON SEQUENCES  TO postgres WITH GRANT OPTION;


--
-- Name: DEFAULT PRIVILEGES FOR FUNCTIONS; Type: DEFAULT ACL; Schema: extensions; Owner: supabase_admin
--

ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA extensions GRANT ALL ON FUNCTIONS  TO postgres WITH GRANT OPTION;


--
-- Name: DEFAULT PRIVILEGES FOR TABLES; Type: DEFAULT ACL; Schema: extensions; Owner: supabase_admin
--

ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA extensions GRANT ALL ON TABLES  TO postgres WITH GRANT OPTION;


--
-- Name: DEFAULT PRIVILEGES FOR SEQUENCES; Type: DEFAULT ACL; Schema: graphql; Owner: supabase_admin
--

ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA graphql GRANT ALL ON SEQUENCES  TO postgres;
ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA graphql GRANT ALL ON SEQUENCES  TO anon;
ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA graphql GRANT ALL ON SEQUENCES  TO authenticated;
ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA graphql GRANT ALL ON SEQUENCES  TO service_role;


--
-- Name: DEFAULT PRIVILEGES FOR FUNCTIONS; Type: DEFAULT ACL; Schema: graphql; Owner: supabase_admin
--

ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA graphql GRANT ALL ON FUNCTIONS  TO postgres;
ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA graphql GRANT ALL ON FUNCTIONS  TO anon;
ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA graphql GRANT ALL ON FUNCTIONS  TO authenticated;
ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA graphql GRANT ALL ON FUNCTIONS  TO service_role;


--
-- Name: DEFAULT PRIVILEGES FOR TABLES; Type: DEFAULT ACL; Schema: graphql; Owner: supabase_admin
--

ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA graphql GRANT ALL ON TABLES  TO postgres;
ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA graphql GRANT ALL ON TABLES  TO anon;
ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA graphql GRANT ALL ON TABLES  TO authenticated;
ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA graphql GRANT ALL ON TABLES  TO service_role;


--
-- Name: DEFAULT PRIVILEGES FOR SEQUENCES; Type: DEFAULT ACL; Schema: graphql_public; Owner: supabase_admin
--

ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA graphql_public GRANT ALL ON SEQUENCES  TO postgres;
ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA graphql_public GRANT ALL ON SEQUENCES  TO anon;
ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA graphql_public GRANT ALL ON SEQUENCES  TO authenticated;
ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA graphql_public GRANT ALL ON SEQUENCES  TO service_role;


--
-- Name: DEFAULT PRIVILEGES FOR FUNCTIONS; Type: DEFAULT ACL; Schema: graphql_public; Owner: supabase_admin
--

ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA graphql_public GRANT ALL ON FUNCTIONS  TO postgres;
ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA graphql_public GRANT ALL ON FUNCTIONS  TO anon;
ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA graphql_public GRANT ALL ON FUNCTIONS  TO authenticated;
ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA graphql_public GRANT ALL ON FUNCTIONS  TO service_role;


--
-- Name: DEFAULT PRIVILEGES FOR TABLES; Type: DEFAULT ACL; Schema: graphql_public; Owner: supabase_admin
--

ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA graphql_public GRANT ALL ON TABLES  TO postgres;
ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA graphql_public GRANT ALL ON TABLES  TO anon;
ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA graphql_public GRANT ALL ON TABLES  TO authenticated;
ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA graphql_public GRANT ALL ON TABLES  TO service_role;


--
-- Name: DEFAULT PRIVILEGES FOR SEQUENCES; Type: DEFAULT ACL; Schema: pgsodium; Owner: supabase_admin
--

ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA pgsodium GRANT ALL ON SEQUENCES  TO pgsodium_keyholder;


--
-- Name: DEFAULT PRIVILEGES FOR TABLES; Type: DEFAULT ACL; Schema: pgsodium; Owner: supabase_admin
--

ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA pgsodium GRANT ALL ON TABLES  TO pgsodium_keyholder;


--
-- Name: DEFAULT PRIVILEGES FOR SEQUENCES; Type: DEFAULT ACL; Schema: pgsodium_masks; Owner: supabase_admin
--

ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA pgsodium_masks GRANT ALL ON SEQUENCES  TO pgsodium_keyiduser;


--
-- Name: DEFAULT PRIVILEGES FOR FUNCTIONS; Type: DEFAULT ACL; Schema: pgsodium_masks; Owner: supabase_admin
--

ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA pgsodium_masks GRANT ALL ON FUNCTIONS  TO pgsodium_keyiduser;


--
-- Name: DEFAULT PRIVILEGES FOR TABLES; Type: DEFAULT ACL; Schema: pgsodium_masks; Owner: supabase_admin
--

ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA pgsodium_masks GRANT ALL ON TABLES  TO pgsodium_keyiduser;


--
-- Name: DEFAULT PRIVILEGES FOR SEQUENCES; Type: DEFAULT ACL; Schema: public; Owner: postgres
--

ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA public GRANT ALL ON SEQUENCES  TO postgres;
ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA public GRANT ALL ON SEQUENCES  TO anon;
ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA public GRANT ALL ON SEQUENCES  TO authenticated;
ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA public GRANT ALL ON SEQUENCES  TO service_role;


--
-- Name: DEFAULT PRIVILEGES FOR SEQUENCES; Type: DEFAULT ACL; Schema: public; Owner: supabase_admin
--

ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA public GRANT ALL ON SEQUENCES  TO postgres;
ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA public GRANT ALL ON SEQUENCES  TO anon;
ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA public GRANT ALL ON SEQUENCES  TO authenticated;
ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA public GRANT ALL ON SEQUENCES  TO service_role;


--
-- Name: DEFAULT PRIVILEGES FOR FUNCTIONS; Type: DEFAULT ACL; Schema: public; Owner: postgres
--

ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA public GRANT ALL ON FUNCTIONS  TO postgres;
ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA public GRANT ALL ON FUNCTIONS  TO anon;
ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA public GRANT ALL ON FUNCTIONS  TO authenticated;
ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA public GRANT ALL ON FUNCTIONS  TO service_role;


--
-- Name: DEFAULT PRIVILEGES FOR FUNCTIONS; Type: DEFAULT ACL; Schema: public; Owner: supabase_admin
--

ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA public GRANT ALL ON FUNCTIONS  TO postgres;
ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA public GRANT ALL ON FUNCTIONS  TO anon;
ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA public GRANT ALL ON FUNCTIONS  TO authenticated;
ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA public GRANT ALL ON FUNCTIONS  TO service_role;


--
-- Name: DEFAULT PRIVILEGES FOR TABLES; Type: DEFAULT ACL; Schema: public; Owner: postgres
--

ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA public GRANT ALL ON TABLES  TO postgres;
ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA public GRANT ALL ON TABLES  TO anon;
ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA public GRANT ALL ON TABLES  TO authenticated;
ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA public GRANT ALL ON TABLES  TO service_role;


--
-- Name: DEFAULT PRIVILEGES FOR TABLES; Type: DEFAULT ACL; Schema: public; Owner: supabase_admin
--

ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA public GRANT ALL ON TABLES  TO postgres;
ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA public GRANT ALL ON TABLES  TO anon;
ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA public GRANT ALL ON TABLES  TO authenticated;
ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA public GRANT ALL ON TABLES  TO service_role;


--
-- Name: DEFAULT PRIVILEGES FOR SEQUENCES; Type: DEFAULT ACL; Schema: realtime; Owner: supabase_admin
--

ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA realtime GRANT ALL ON SEQUENCES  TO postgres;
ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA realtime GRANT ALL ON SEQUENCES  TO dashboard_user;


--
-- Name: DEFAULT PRIVILEGES FOR FUNCTIONS; Type: DEFAULT ACL; Schema: realtime; Owner: supabase_admin
--

ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA realtime GRANT ALL ON FUNCTIONS  TO postgres;
ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA realtime GRANT ALL ON FUNCTIONS  TO dashboard_user;


--
-- Name: DEFAULT PRIVILEGES FOR TABLES; Type: DEFAULT ACL; Schema: realtime; Owner: supabase_admin
--

ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA realtime GRANT ALL ON TABLES  TO postgres;
ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA realtime GRANT ALL ON TABLES  TO dashboard_user;


--
-- Name: DEFAULT PRIVILEGES FOR SEQUENCES; Type: DEFAULT ACL; Schema: storage; Owner: postgres
--

ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA storage GRANT ALL ON SEQUENCES  TO postgres;
ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA storage GRANT ALL ON SEQUENCES  TO anon;
ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA storage GRANT ALL ON SEQUENCES  TO authenticated;
ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA storage GRANT ALL ON SEQUENCES  TO service_role;


--
-- Name: DEFAULT PRIVILEGES FOR FUNCTIONS; Type: DEFAULT ACL; Schema: storage; Owner: postgres
--

ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA storage GRANT ALL ON FUNCTIONS  TO postgres;
ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA storage GRANT ALL ON FUNCTIONS  TO anon;
ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA storage GRANT ALL ON FUNCTIONS  TO authenticated;
ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA storage GRANT ALL ON FUNCTIONS  TO service_role;


--
-- Name: DEFAULT PRIVILEGES FOR TABLES; Type: DEFAULT ACL; Schema: storage; Owner: postgres
--

ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA storage GRANT ALL ON TABLES  TO postgres;
ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA storage GRANT ALL ON TABLES  TO anon;
ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA storage GRANT ALL ON TABLES  TO authenticated;
ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA storage GRANT ALL ON TABLES  TO service_role;


--
-- Name: DEFAULT PRIVILEGES FOR SEQUENCES; Type: DEFAULT ACL; Schema: supabase_functions; Owner: supabase_admin
--

ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA supabase_functions GRANT ALL ON SEQUENCES  TO postgres;
ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA supabase_functions GRANT ALL ON SEQUENCES  TO anon;
ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA supabase_functions GRANT ALL ON SEQUENCES  TO authenticated;
ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA supabase_functions GRANT ALL ON SEQUENCES  TO service_role;


--
-- Name: DEFAULT PRIVILEGES FOR FUNCTIONS; Type: DEFAULT ACL; Schema: supabase_functions; Owner: supabase_admin
--

ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA supabase_functions GRANT ALL ON FUNCTIONS  TO postgres;
ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA supabase_functions GRANT ALL ON FUNCTIONS  TO anon;
ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA supabase_functions GRANT ALL ON FUNCTIONS  TO authenticated;
ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA supabase_functions GRANT ALL ON FUNCTIONS  TO service_role;


--
-- Name: DEFAULT PRIVILEGES FOR TABLES; Type: DEFAULT ACL; Schema: supabase_functions; Owner: supabase_admin
--

ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA supabase_functions GRANT ALL ON TABLES  TO postgres;
ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA supabase_functions GRANT ALL ON TABLES  TO anon;
ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA supabase_functions GRANT ALL ON TABLES  TO authenticated;
ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA supabase_functions GRANT ALL ON TABLES  TO service_role;


--
-- Name: issue_graphql_placeholder; Type: EVENT TRIGGER; Schema: -; Owner: supabase_admin
--

CREATE EVENT TRIGGER issue_graphql_placeholder ON sql_drop
         WHEN TAG IN ('DROP EXTENSION')
   EXECUTE FUNCTION extensions.set_graphql_placeholder();


ALTER EVENT TRIGGER issue_graphql_placeholder OWNER TO supabase_admin;

--
-- Name: issue_pg_cron_access; Type: EVENT TRIGGER; Schema: -; Owner: supabase_admin
--

CREATE EVENT TRIGGER issue_pg_cron_access ON ddl_command_end
         WHEN TAG IN ('CREATE EXTENSION')
   EXECUTE FUNCTION extensions.grant_pg_cron_access();


ALTER EVENT TRIGGER issue_pg_cron_access OWNER TO supabase_admin;

--
-- Name: issue_pg_graphql_access; Type: EVENT TRIGGER; Schema: -; Owner: supabase_admin
--

CREATE EVENT TRIGGER issue_pg_graphql_access ON ddl_command_end
         WHEN TAG IN ('CREATE FUNCTION')
   EXECUTE FUNCTION extensions.grant_pg_graphql_access();


ALTER EVENT TRIGGER issue_pg_graphql_access OWNER TO supabase_admin;

--
-- Name: issue_pg_net_access; Type: EVENT TRIGGER; Schema: -; Owner: postgres
--

CREATE EVENT TRIGGER issue_pg_net_access ON ddl_command_end
         WHEN TAG IN ('CREATE EXTENSION')
   EXECUTE FUNCTION extensions.grant_pg_net_access();


ALTER EVENT TRIGGER issue_pg_net_access OWNER TO postgres;

--
-- Name: pgrst_ddl_watch; Type: EVENT TRIGGER; Schema: -; Owner: supabase_admin
--

CREATE EVENT TRIGGER pgrst_ddl_watch ON ddl_command_end
   EXECUTE FUNCTION extensions.pgrst_ddl_watch();


ALTER EVENT TRIGGER pgrst_ddl_watch OWNER TO supabase_admin;

--
-- Name: pgrst_drop_watch; Type: EVENT TRIGGER; Schema: -; Owner: supabase_admin
--

CREATE EVENT TRIGGER pgrst_drop_watch ON sql_drop
   EXECUTE FUNCTION extensions.pgrst_drop_watch();


ALTER EVENT TRIGGER pgrst_drop_watch OWNER TO supabase_admin;

--
-- PostgreSQL database dump complete
--

--
-- PostgreSQL database cluster dump complete
--


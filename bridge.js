#!/usr/bin/env node
// -*- coding: utf-8 -*-
/**
 * Telegram Bot Bridge for Supabase Integration
 * 
 * This service acts as a bridge between Telegram and Supabase:
 * 1. Listens for message changes in Supabase 'does' table and forwards to Telegram
 * 2. Receives commands from Telegram and updates the 'command' field in Supabase
 * 
 * Author: Cascade AI
 * Date: 2025-01-09
 */

import { Telegraf } from 'telegraf';
import { createClient } from '@supabase/supabase-js';

// 硬编码配置
const CONFIG = {
  telegram: {
    bot_token: '**********************************************',
    chat_id: 235196660
  },
  supabase: {
    url: 'https://wjanjmsywbydjbfrdkaz.supabase.co',
    key: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6IndqYW5qbXN5d2J5ZGpiZnJka2F6Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3MjAxMjc1OTEsImV4cCI6MjAzNTcwMzU5MX0.ny-61EeDBsVRUr6BdWiamt9oV-6z2TC_f01FJff-9fE',
    does_table_id: 1
  }
};

// Telegram配置
const BOT_TOKEN = CONFIG.telegram.bot_token;
const CHAT_ID = CONFIG.telegram.chat_id;
const AUTHORIZED_USERS = [parseInt(CHAT_ID)]; // 只允许特定用户使用命令

// Supabase配置
const SUPABASE_URL = CONFIG.supabase.url;
const SUPABASE_KEY = CONFIG.supabase.key;
const DOES_TABLE_ID = CONFIG.supabase.does_table_id;

// 日志记录器
class Logger {
  constructor(name = 'telegram-bot-bridge') {
    this.name = name;
  }

  _format(level, message) {
    const timestamp = new Date().toISOString();
    return `${timestamp} - ${level} - ${message}`;
  }

  info(message) {
    console.log(this._format('INFO', message));
  }

  error(message) {
    console.error(this._format('ERROR', message));
  }

  warning(message) {
    console.warn(this._format('WARNING', message));
  }

  debug(message) {
    if (process.env.DEBUG) {
      console.log(this._format('DEBUG', message));
    }
  }
}

const logger = new Logger();

// 初始化Supabase客户端，配置重连策略
const supabase = createClient(SUPABASE_URL, SUPABASE_KEY, {
  realtime: {
    params: {
      eventsPerSecond: 10
    },
    reconnectAfterMs: (attempts) => {
      // 指数退避：1s, 2s, 4s, 8s, 16s, 最多30s
      const baseDelay = 1000;
      const maxDelay = 30000;
      const delay = Math.min(baseDelay * Math.pow(2, attempts - 1), maxDelay);
      logger.info(`Supabase重连尝试 ${attempts}，等待 ${delay}ms`);
      return delay;
    }
  }
});
logger.info("Supabase客户端已初始化");

// 初始化Telegram Bot
const bot = new Telegraf(BOT_TOKEN);
logger.info("Telegram Bot已初始化");

// 跟踪上次消息内容，避免重复发送
let lastMessage = null;
let messageQueue = [];
let isProcessingQueue = false;

// 消息发送队列处理
async function processMessageQueue() {
  if (isProcessingQueue || messageQueue.length === 0) {
    return;
  }

  isProcessingQueue = true;
  
  while (messageQueue.length > 0) {
    const message = messageQueue.shift();
    try {
      await bot.telegram.sendMessage(CHAT_ID, message, { 
        parse_mode: 'HTML',
        disable_web_page_preview: true 
      });
      logger.info(`消息已发送到Telegram: ${message}`);
      
      // 避免发送过快
      await new Promise(resolve => setTimeout(resolve, 1000));
    } catch (error) {
      logger.error(`发送Telegram消息失败: ${error.message}`);
      // 如果发送失败，将消息放回队列前面
      messageQueue.unshift(message);
      // 等待更长时间后重试
      await new Promise(resolve => setTimeout(resolve, 5000));
    }
  }
  
  isProcessingQueue = false;
}

// 当前活动的频道引用
let currentChannel = null;
let reconnectAttempts = 0;
let reconnectTimer = null;

// 计算重连延迟（指数退避）
function calculateReconnectDelay(attempt) {
  const baseDelay = 1000; // 1秒
  const maxDelay = 30000; // 30秒
  const delay = Math.min(baseDelay * Math.pow(2, attempt - 1), maxDelay);
  return delay;
}

// 清理并重新订阅频道
async function reconnectChannel() {
  reconnectAttempts++;
  const delay = calculateReconnectDelay(reconnectAttempts);
  
  logger.warning(`准备重连 Supabase 频道，第 ${reconnectAttempts} 次尝试，延迟 ${delay}ms`);
  
  // 清理现有频道
  if (currentChannel) {
    try {
      await supabase.removeChannel(currentChannel);
      logger.info("已移除旧频道");
    } catch (e) {
      logger.error(`移除频道失败: ${e}`);
    }
    currentChannel = null;
  }
  
  // 延迟后重新连接
  reconnectTimer = setTimeout(() => {
    setupSupabaseListener();
  }, delay);
}

// 设置Supabase消息监听
async function setupSupabaseListener() {
  // 清理重连定时器
  if (reconnectTimer) {
    clearTimeout(reconnectTimer);
    reconnectTimer = null;
  }
  
  // 首先获取当前消息值
  try {
    const { data, error } = await supabase
      .from('does')
      .select('message')
      .eq('id', DOES_TABLE_ID)
      .single();

    if (!error && data && data.message) {
      try {
        const messageData = JSON.parse(data.message);
        lastMessage = messageData.message;
        logger.info(`初始消息值已获取`);
      } catch (e) {
        logger.error(`解析初始消息失败: ${e}`);
      }
    }
  } catch (e) {
    logger.error(`获取初始消息值失败: ${e}`);
  }

  // 设置实时监听
  currentChannel = supabase
    .channel('message-changes')
    .on(
      'postgres_changes',
      {
        event: 'UPDATE',
        schema: 'public',
        table: 'does',
        filter: `id=eq.${DOES_TABLE_ID}`
      },
      async (payload) => {
        try {
          if (payload.new && payload.new.message) {
            const messageData = JSON.parse(payload.new.message);
            const newMessage = messageData.message;
            
            // 检查是否是新消息
            if (newMessage && newMessage !== lastMessage) {
              logger.info(`检测到新消息: ${newMessage}`);
              lastMessage = newMessage;
              
              // 格式化消息为HTML
              const formattedMessage = formatMessageForTelegram(newMessage);
              
              // 添加到消息队列
              messageQueue.push(formattedMessage);
              processMessageQueue();
            }
          }
        } catch (e) {
          logger.error(`处理消息更新失败: ${e}`);
        }
      }
    )
    .subscribe((status, err) => {
      if (err) {
        logger.error(`订阅错误: ${err.message}`);
      } else {
        logger.info(`频道订阅状态: ${status}`);
        
        if (status === 'SUBSCRIBED') {
          // 成功订阅，重置重连计数
          reconnectAttempts = 0;
          logger.info("Supabase消息监听器已成功订阅");
        }
      }
    });
  
  // 监听频道状态变化
  currentChannel.on('system', {}, (payload) => {
    logger.info(`频道系统事件: ${JSON.stringify(payload)}`);
    
    // 处理错误状态
    if (payload.extension === 'system' && 
        ['CHANNEL_ERROR', 'TIMED_OUT', 'CLOSED'].includes(payload.status)) {
      logger.error(`频道异常状态: ${payload.status}`);
      reconnectChannel();
    }
  });

  logger.info("Supabase消息监听器已设置");
}

// 格式化消息为Telegram HTML格式
function formatMessageForTelegram(message) {
  // 将Markdown格式转换为HTML
  let formatted = message
    .replace(/\*\*(.*?)\*\*/g, '<b>$1</b>') // 粗体
    .replace(/\*(.*?)\*/g, '<i>$1</i>') // 斜体
    .replace(/`(.*?)`/g, '<code>$1</code>') // 代码
    .replace(/```(.*?)```/gs, '<pre>$1</pre>'); // 代码块
  
  return formatted;
}

// 设置Telegram命令处理
function setupTelegramCommands() {
  // 中间件：检查授权用户
  bot.use(async (ctx, next) => {
    // 过滤没有from字段的消息（系统消息等）
    if (!ctx.from) {
      return;
    }
    
    // 过滤机器人消息
    if (ctx.from.is_bot) {
      return;
    }
    
    const userId = ctx.from.id;
    if (!AUTHORIZED_USERS.includes(userId)) {
      logger.warning(`未授权用户尝试使用命令: ${userId} (@${ctx.from.username || '无用户名'})`);
      return;
    }
    await next();
  });

  // /start 命令
  bot.start((ctx) => {
    const chatId = ctx.chat.id;
    const userId = ctx.from.id;
    
    ctx.replyWithHTML(`
👋 欢迎使用集成监控服务Bot！

您的Chat ID: <code>${chatId}</code>
您的User ID: <code>${userId}</code>

此Bot用于接收监控服务的通知并执行控制命令。
使用 /help 查看可用命令。
    `);
  });

  // /help 命令
  bot.help((ctx) => {
    ctx.replyWithHTML(`
<b>可用命令：</b>

/sub1 - 切换到订阅链接1
/sub2 - 切换到订阅链接2
/switch - 切换到自动模式（交替使用订阅）
/status - 查看当前状态
/help - 显示此帮助信息

<i>所有命令会同步到监控服务执行</i>
    `);
  });

  // 处理具体命令
  const commands = ['sub1', 'sub2', 'switch', 'status'];
  
  commands.forEach(cmd => {
    bot.command(cmd, async (ctx) => {
      const userId = ctx.from.id;
      logger.info(`收到命令: /${cmd} from ${userId}`);
      
      try {
        const { data, error } = await supabase
          .from('does')
          .update({ command: cmd })
          .eq('id', DOES_TABLE_ID);

        if (error) {
          logger.error(`更新命令失败: ${error.message}`);
          await ctx.reply(`❌ 命令执行失败: ${error.message}`);
        } else {
          logger.info(`命令已更新到Supabase: ${cmd}`);
          await ctx.reply(`✅ 命令已发送: ${cmd}\n\n监控服务将在几秒内执行此命令。`);
        }
      } catch (e) {
        logger.error(`更新命令时出错: ${e}`);
        await ctx.reply(`❌ 命令执行失败: ${e.message}`);
      }
    });
  });

  logger.info("Telegram命令处理器已设置");
}

// 发送启动通知
async function sendStartupNotification() {
  const startupMessage = `
🚀 <b>Telegram Bot Bridge 已启动</b>

时间: ${new Date().toLocaleString('zh-CN')}
状态: 正在监听Supabase消息更新

此服务将:
• 转发监控服务的消息到Telegram
• 接收Telegram命令并同步到监控服务

使用 /help 查看可用命令
  `;
  
  try {
    await bot.telegram.sendMessage(CHAT_ID, startupMessage, { parse_mode: 'HTML' });
    logger.info("启动通知已发送");
  } catch (e) {
    logger.error(`发送启动通知失败: ${e}`);
  }
}

// 错误处理
bot.catch((err, ctx) => {
  logger.error(`Bot错误: ${err}`);
});

// 优雅退出
async function gracefulShutdown() {
  logger.info("正在关闭服务...");
  
  // 清理重连定时器
  if (reconnectTimer) {
    clearTimeout(reconnectTimer);
  }
  
  // 清理Supabase频道
  if (currentChannel) {
    try {
      await supabase.removeChannel(currentChannel);
      logger.info("已清理Supabase频道");
    } catch (e) {
      logger.error(`清理频道失败: ${e}`);
    }
  }
  
  try {
    // 发送关闭通知
    await bot.telegram.sendMessage(CHAT_ID, "🛑 Telegram Bot Bridge 服务已停止", { parse_mode: 'HTML' });
  } catch (e) {
    logger.error(`发送关闭通知失败: ${e}`);
  }
  
  // 停止bot
  bot.stop();
  
  // 等待消息队列处理完成
  while (messageQueue.length > 0) {
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
  
  logger.info("服务已关闭");
  process.exit(0);
}

// 处理退出信号
process.once('SIGINT', gracefulShutdown);
process.once('SIGTERM', gracefulShutdown);

// 主函数
async function main() {
  try {
    logger.info("========================================");
    logger.info("Telegram Bot Bridge 服务启动");
    logger.info("========================================");
    logger.info(`Telegram Bot Token: ${BOT_TOKEN.substring(0, 10)}...`);
    logger.info(`Chat ID: ${CHAT_ID}`);
    logger.info(`Supabase URL: ${SUPABASE_URL}`);
    logger.info(`监听表ID: ${DOES_TABLE_ID}`);
    
    // 设置Supabase监听
    await setupSupabaseListener();
    
    // 设置Telegram命令处理
    setupTelegramCommands();
    
    // 发送启动通知
    await sendStartupNotification();
    
    // 定期处理消息队列
    setInterval(() => {
      if (messageQueue.length > 0) {
        processMessageQueue();
      }
    }, 5000);
    
    // 启动bot
    bot.launch();
    logger.info("服务启动完成，正在运行...");
    
  } catch (e) {
    logger.error(`主程序错误: ${e}`);
    process.exit(1);
  }
}

// 启动服务
main();
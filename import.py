from pyrogram import Client
import asyncio
import os
import logging
from queue import Queue
import redis
import psycopg2
from psycopg2 import pool

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Use your own values for API ID, API Hash, and Bot Token
api_id = '25432929'
api_hash = '965c5d22f0b9d1d0326e84bbb2bb18c1'
bot_token = '6885885686:AAFzTrTl7nerNV9_m59TT9SQ_YKlrK5bKMM'
username = '@juaer' # 发送文件 ID 的目标用户

file_queue = Queue()

# 连接到Redis
r = redis.Redis(host='localhost', port=6379, db=0)

# 连接到Supabase数据库
pool = psycopg2.pool.SimpleConnectionPool(
    minconn=2,
    maxconn=30,
    host='aws-0-ap-northeast-1.pooler.supabase.com',
    port=5432,
    dbname='postgres',
    user='postgres.nathkbbkyfjthxpdqocu',
    password='4gyp2MJp84zxW.F',
    connect_timeout=60
)

async def send_mp4_file(file_path, anchor_id, width, height, duration, thumb_path):
    try:
        logger.info(f"Uploading MP4 file: {file_path}")
        
        # 检查文件是否存在
        if not os.path.exists(file_path):
            logger.error(f"File does not exist: {file_path}")
            return
        
        # 检查缩略图文件是否存在
        if thumb_path is not None and not os.path.exists(thumb_path):
            logger.warning(f"Thumbnail file does not exist: {thumb_path}")
            thumb_path = None
        
        # 创建 Pyrogram 客户端
        async with Client("file1", api_id=api_id, api_hash=api_hash, bot_token=bot_token) as app:
            # 获取文件名作为视频的标题
            file_name = os.path.basename(file_path)
            
            # 打印即将使用的参数
            logger.info(f"Parameters for sending video:")
            logger.info(f"  chat_id: {username}")
            logger.info(f"  video: {file_path}")
            logger.info(f"  caption: {file_name}")
            logger.info(f"  duration: {duration}")
            logger.info(f"  width: {width}")
            logger.info(f"  height: {height}")
            logger.info(f"  thumb: {thumb_path}")
            logger.info(f"  file_name: {file_name}")
            logger.info(f"  supports_streaming: True")
            
            # 上传视频文件
            sent_message = await app.send_video(
                chat_id=username,
                video=file_path,
                caption=file_name,
                duration=duration,
                width=width,
                height=height,
                thumb=thumb_path,
                supports_streaming=True,
                progress=progress_callback,
                progress_args=(file_path,)
            )

            mp4id = sent_message.video.file_id
            logger.info(f"MP4 file uploaded successfully: {file_path}")
            logger.info(f"MP4 ID: {mp4id}")
            logger.info(f"Anchor ID: {anchor_id}")
            
            # 将MP4 ID发送给Telegram
            await app.send_message(username, f"MP4 ID: {mp4id}")
            
            # 更新anchors表中对应anchor_id的mp4id字段
            conn = pool.getconn()
            with conn.cursor() as cursor:
                update_query = "UPDATE anchors SET mp4id = %s WHERE anchor_id = %s"
                cursor.execute(update_query, (mp4id, anchor_id))
            conn.commit()
            pool.putconn(conn)
        
        # 删除本地文件
        if os.path.exists(file_path):
            os.remove(file_path)
            os.remove(thumb_path)
            logger.info(f"Deleted MP4 file: {file_path}")
        else:
            logger.warning(f"MP4 file not found: {file_path}")
    
    except Exception as e:
        logger.error(f"Error uploading MP4 file: {file_path}. Error: {str(e)}")

async def send_other_file(file_path, anchor_id):
    try:
        logger.info(f"Uploading other file: {file_path}")
        
        # 检查文件是否存在
        if not os.path.exists(file_path):
            logger.error(f"File does not exist: {file_path}")
            return
        
        # 创建 Pyrogram 客户端
        async with Client("file1", api_id=api_id, api_hash=api_hash, bot_token=bot_token) as app:
            # 发送其他文件
            sent_message = await app.send_document(username, document=file_path, progress=progress_callback, progress_args=(file_path,))
            tsid = sent_message.document.file_id
            logger.info(f"Other file uploaded successfully: {file_path}")
            logger.info(f"TS ID: {tsid}")
            logger.info(f"Anchor ID: {anchor_id}")
            # 将TS ID发送给Telegram
            await app.send_message(username, f"TS ID: {tsid}")
            
            # 更新anchors表中对应anchor_id的tsid字段
            conn = pool.getconn()
            with conn.cursor() as cursor:
                update_query = "UPDATE anchors SET tsid = %s WHERE anchor_id = %s"
                cursor.execute(update_query, (tsid, anchor_id))
            conn.commit()
            pool.putconn(conn)
            
            # 删除本地文件
            if os.path.exists(file_path):
                os.remove(file_path)
                logger.info(f"Deleted other file: {file_path}")
            else:
                logger.warning(f"Other file not found: {file_path}")
    except Exception as e:
        logger.error(f"Error uploading other file: {file_path}. Error: {str(e)}")

async def progress_callback(current, total, file_path):
    logger.info(f"Uploaded {current} bytes out of {total} bytes for file: {file_path}")

def process_message(message):
    data = message['data'].decode('utf-8')
    logger.info(f"Received message from Redis: {data}")
    try:
        fields = data.split(',')
        if len(fields) == 6:
            anchor_id, output_file_mp4, width, height, duration, thumb_path = fields
            file_queue.put(('mp4', output_file_mp4, anchor_id, int(width), int(height), int(duration), thumb_path))
        elif len(fields) == 2:
            anchor_id, output_file_ts = fields
            file_queue.put(('other', output_file_ts, anchor_id))
        else:
            logger.warning(f"Invalid message format: {data}")
    except ValueError as e:
        logger.error(f"Error processing message: {data}. Error: {str(e)}")

async def main():
    pubsub = r.pubsub()
    pubsub.subscribe('file1')

    while True:
        message = pubsub.get_message(ignore_subscribe_messages=True)
        if message:
            process_message(message)
        
        if not file_queue.empty():
            file_type, file_path, anchor_id, *extra_args = file_queue.get()
            if file_type == 'mp4':
                width, height, duration, thumb_path = extra_args
                await send_mp4_file(file_path, anchor_id, width, height, duration, thumb_path)
            else:
                await send_other_file(file_path, anchor_id)
            file_queue.task_done()
        
        await asyncio.sleep(0.1)

    pool.closeall()

if __name__ == '__main__':
    asyncio.run(main())
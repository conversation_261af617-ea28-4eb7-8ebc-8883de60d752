import redis
import psycopg2
from psycopg2 import pool
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 连接到Redis
r = redis.Redis(host='localhost', port=6379, db=0)

# 创建数据库连接池
pool = pool.SimpleConnectionPool(
    minconn=2,
    maxconn=30,
    host='aws-0-ap-northeast-1.pooler.supabase.com',
    port=5432,
    dbname='postgres',
    user='postgres.nathkbbkyfjthxpdqocu',
    password='4gyp2MJp84zxW.F',
    connect_timeout=60
)

def update_recording_status(anchor_id, is_recording):
    conn = pool.getconn()
    try:
        with conn.cursor() as cur:
            update_query = "UPDATE anchors SET is_recording = %s WHERE anchor_id = %s"
            cur.execute(update_query, (is_recording, anchor_id))
            logger.info(f"Updated recording status for Anchor ID: {anchor_id}. Is recording: {is_recording}")
            conn.commit()
    finally:
        pool.putconn(conn)

def process_message(message):
    try:
        data = message['data'].decode('utf-8')
        logger.info(f"Received message from Redis: {data}")
        fields = data.split(',')
        
        if fields[0] == 'start' and len(fields) == 2:
            anchor_id = fields[1]
            update_recording_status(anchor_id, True)
        elif fields[0] == 'end' and len(fields) == 2:
            anchor_id = fields[1]
            update_recording_status(anchor_id, False)
        elif len(fields) == 3:
            anchor_id, file_id, id_type = fields
            conn = pool.getconn()
            try:
                with conn.cursor() as cur:
                    if id_type == 'mp4id':
                        update_query = "UPDATE anchors SET mp4id = %s WHERE anchor_id = %s"
                        cur.execute(update_query, (file_id, anchor_id))
                        logger.info(f"Updated MP4ID for Anchor ID: {anchor_id}")
                    elif id_type == 'tsid':
                        update_query = "UPDATE anchors SET tsid = %s WHERE anchor_id = %s"
                        cur.execute(update_query, (file_id, anchor_id))
                        logger.info(f"Updated TSID for Anchor ID: {anchor_id}")
                    else:
                        logger.warning(f"Invalid ID type: {id_type}")
                    conn.commit()
            finally:
                pool.putconn(conn)
        else:
            logger.warning(f"Invalid message format: {data}")
    except Exception as e:
        logger.error(f"Error processing message: {data}. Error: {str(e)}")

def main():
    pubsub = r.pubsub()
    pubsub.subscribe('id')
    logger.info("Starting database update process...")
    for message in pubsub.listen():
        if message['type'] == 'message':
            process_message(message)

if __name__ == '__main__':
    main()
import re
import json
import urllib.request
import redis
import time
import threading

no_proxy_handler = urllib.request.ProxyHandler({})
opener = urllib.request.build_opener(no_proxy_handler)

from spider import (
    get_douyin_stream_data,
    get_tiktok_stream_data,
)

def get_platform(url):
    if re.search(r'tiktok\.com', url):
        return 'tiktok'
    elif re.search(r'douyin\.com', url):
        return 'douyin'
    else:
        return None
    
def get_record_url(url, platform, anchor_id, anchor_name):
    try:
        stream_info = get_stream_info(url, platform)
        if stream_info['is_live']:
            stream_info['anchor_id'] = anchor_id
            stream_info['anchor_name'] = anchor_name
            return stream_info
        else:
            return None
    except Exception as e:
        print(f"获取直播流信息失败：{url}, 错误信息：{str(e)}")
        return None

def get_req(
    url: str,
    headers: dict = None,
    timeout: int = 20,
    abroad: bool = False
) -> str:

    if headers is None:
        headers = {}
    try:
        req = urllib.request.Request(url, headers=headers)
        try:
            if abroad:
                with urllib.request.urlopen(req, timeout=timeout) as response:
                    resp_str = response.read().decode('utf-8')
            else:
                with opener.open(req, timeout=timeout) as response:
                    resp_str = response.read().decode('utf-8')
        except Exception as e:
            print("An error occurred:", e)
            raise
    except Exception as e:
        resp_str = str(e)

    return resp_str

def get_tiktok_stream_url(json_data: dict) -> dict:
    if not json_data:
        return {"anchor_name": None, "is_live": False}

    live_room = json_data['LiveRoom']['liveRoomUserInfo']
    user = live_room['user'] 
    anchor_name = f"{user['nickname']}-{user['uniqueId']}"
    status = user.get("status", 4)

    result = {
        "anchor_name": anchor_name,
        "is_live": False,
    }

    if status == 2:
        stream_data = live_room.get('liveRoom', {}).get('streamData', {}).get('pull_data', {}).get('stream_data', '{}')
        stream_data = json.loads(stream_data).get('data', {})

        quality_order = ['origin', 'uhd', 'hd', 'sd']
        quality_list = sorted(stream_data.keys(), key=lambda x: quality_order.index(x) if x in quality_order else len(quality_order))

        for quality_key in quality_list:
            video_quality_urls = {
                'hls': re.sub(r'_\w+\.m3u8', '_or4.m3u8', re.sub("https", "http", stream_data[quality_key]['main']['hls'])),
                'flv': re.sub(r'_\w+\.flv', '_or4.flv', re.sub("https", "http", stream_data[quality_key]['main']['flv'])),
            }

            if video_quality_urls['flv']:
                try:
                    response = urllib.request.urlopen(video_quality_urls['flv'])
                    if response.status == 200 and response.read(3) == b'FLV':
                        result['flv_url'] = video_quality_urls['flv']
                        result['m3u8_url'] = video_quality_urls['hls']
                        result['is_live'] = True
                        result['record_url'] = result['flv_url']
                        break
                except:
                    pass

            if video_quality_urls['hls']:
                try:
                    response = urllib.request.urlopen(video_quality_urls['hls'])
                    if response.status == 200 and response.read(7) == b'#EXTM3U':
                        result['flv_url'] = video_quality_urls['flv']
                        result['m3u8_url'] = video_quality_urls['hls']
                        result['is_live'] = True
                        result['record_url'] = result['m3u8_url']
                        break  
                except:
                    pass

    return result

def get_douyin_stream_url(json_data: dict) -> dict:
    anchor_name = json_data.get('anchor_name', None)

    result = {
        "anchor_name": anchor_name,
        "is_live": False,
    }

    status = json_data.get("status", 4)  # 直播状态 2 是正在直播、4 是未开播

    if status == 2:
        stream_url = json_data['stream_url']
        flv_url_list = stream_url['flv_pull_url'] 
        m3u8_url_list = stream_url['hls_pull_url_map']

        quality_list: list = list(m3u8_url_list.keys())
        while len(quality_list) < 4:
            quality_list.append(quality_list[-1])

        quality_key = quality_list[0]  # 默认选择原画画质
        m3u8_url = re.sub(r'_\w+\.m3u8', '_or4.m3u8', m3u8_url_list.get(quality_key))
        flv_url = re.sub(r'_\w+\.flv', '_or4.flv', flv_url_list.get(quality_key))

        result['m3u8_url'] = m3u8_url
        result['flv_url'] = flv_url
        result['is_live'] = True
        result['record_url'] = m3u8_url  # 使用 m3u8 链接进行录制
    return result

def get_stream_info(url: str, platform: str) -> dict:
    """
    获取直播流信息的函数 
    :param url: 直播间链接
    :param platform: 平台名称,可选值为 'tiktok' 或 'douyin'  
    :return: 包含直播流信息的字典
    """
    if platform == 'tiktok':
        try:
            json_data = get_tiktok_stream_data(url)
            stream_info = get_tiktok_stream_url(json_data)
        except IndexError:
            print(f"Failed to parse JSON data from {url}. Retrying in 10 seconds...")
            time.sleep(10)
            try:
                json_data = get_tiktok_stream_data(url)
                stream_info = get_tiktok_stream_url(json_data)
            except IndexError:
                print(f"Failed to parse JSON data from {url} after retry. Adding to failed list.")
                with open('failed_list.json', 'r') as f:
                    failed_list = json.load(f)
                failed_list.append(url)
                with open('failed_list.json', 'w') as f:
                    json.dump(failed_list, f)
                return {"anchor_name": None, "is_live": False}
    elif platform == 'douyin':
        json_data = get_douyin_stream_data(url)
        stream_info = get_douyin_stream_url(json_data)
    else:
        raise ValueError(f"不支持的平台: {platform}")
    
    return stream_info

def parse_live_stream(anchor_id, anchor_name, new_url):
    platform = get_platform(new_url)

    if platform:
        stream_info = get_record_url(new_url, platform, anchor_id, anchor_name)

        if stream_info:
            # 构建要发送的消息
            message = f"start,{anchor_id},{anchor_name},{stream_info['record_url']}"
            
            # 打印要传递的消息
            print(f"要传递的消息: {message}")
            
            # 将运行结果发布到 Redis 频道 "record"
            r.publish('record', message)
        else:
            print(f"无法获取直播流信息: {new_url}")
    else:  
        print(f"不支持的平台: {new_url}")

def handle_task(data):
    if data.startswith('parse'):
        _, anchor_id, anchor_name, new_url = data.split(',')
        anchor_name = anchor_name.replace('/', '')  # 将anchor name中的斜杠替换为空字符串
        parse_live_stream(anchor_id, anchor_name, new_url)
        
def retry_failed_urls():
    while True:
        time.sleep(3600)  # 每小时重试一次
        with open('failed_list.json', 'r') as f:
            failed_list = json.load(f)
        
        for url in failed_list:
            platform = get_platform(url)
            if platform:
                stream_info = get_stream_info(url, platform)
                if stream_info['is_live']:
                    failed_list.remove(url)
                    print(f"Successfully parsed {url} after retry.")
            else:
                failed_list.remove(url)
                print(f"Unsupported platform: {url}")
        
        with open('failed_list.json', 'w') as f:
            json.dump(failed_list, f)

if __name__ == "__main__":
    # 连接到 Redis 服务器
    r = redis.Redis(host='localhost', port=6379, db=0)

    # 订阅 Redis 频道,接收解析任务
    pubsub = r.pubsub()
    pubsub.subscribe('parse_task')

    # 创建一个新的线程用于重试失败的链接
    retry_thread = threading.Thread(target=retry_failed_urls)
    retry_thread.start()

    # 监听 Redis 消息
    for message in pubsub.listen():
        if message['type'] == 'message':
            data = message['data'].decode('utf-8')
            if data.startswith('parse'):
                _, anchor_id, anchor_name, new_url = data.split(',')
                anchor_name = anchor_name.replace('/', '')  # 将anchor name中的斜杠替换为空字符串
                parse_live_stream(anchor_id, anchor_name, new_url)

    # 取消订阅 Redis 频道
    pubsub.unsubscribe('parse_task')
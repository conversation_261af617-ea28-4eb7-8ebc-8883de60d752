import oci
import time
import datetime
import pytz

# 创建默认配置
config = oci.config.from_file()

# 初始化服务客户端
compute_client = oci.core.ComputeClient(config)
network_client = oci.core.VirtualNetworkClient(config)

# 设置中国时区
china_tz = pytz.timezone('Asia/Shanghai')

def get_china_time():
    return datetime.datetime.now(china_tz)

def rotate_instance_public_ip(instance_id):
    try:
        # 获取实例的VNIC附件
        vnic_attachments = compute_client.list_vnic_attachments(
            compartment_id=config["tenancy"],
            instance_id=instance_id
        ).data

        for vnic_attachment in vnic_attachments:
            # 获取VNIC详情
            vnic = network_client.get_vnic(vnic_attachment.vnic_id).data
            
            # 获取主要私有IP
            private_ips = network_client.list_private_ips(vnic_id=vnic.id).data
            primary_private_ip = next((ip for ip in private_ips if ip.is_primary), None)
            
            if not primary_private_ip:
                print(f"实例 {instance_id}: 未找到主要私有IP，无法进行IP轮换")
                return

            # 准备创建新的临时公共IP
            create_public_ip_details = oci.core.models.CreatePublicIpDetails(
                compartment_id=config["tenancy"],
                lifetime="EPHEMERAL",
                private_ip_id=primary_private_ip.id
            )

            # 记录开始时间
            start_time = time.time()

            if vnic.public_ip:
                print(f"实例 {instance_id}: 当前公共IP: {vnic.public_ip}")
                
                # 获取公共IP的OCID
                public_ip = network_client.get_public_ip_by_ip_address(
                    oci.core.models.GetPublicIpByIpAddressDetails(
                        ip_address=vnic.public_ip
                    )
                ).data

                # 删除旧的公共IP
                network_client.delete_public_ip(public_ip_id=public_ip.id)
                print(f"实例 {instance_id}: 已删除旧的公共IP: {vnic.public_ip}")

            # 立即创建新的公共IP
            new_public_ip = network_client.create_public_ip(create_public_ip_details).data
            print(f"实例 {instance_id}: 正在分配新的临时公共IP: {new_public_ip.ip_address}")

            # 等待新的公共IP完全分配，最多等待5秒
            max_wait_seconds = 5
            succeeded = False
            while time.time() - start_time < max_wait_seconds:
                get_public_ip = network_client.get_public_ip(new_public_ip.id).data
                if get_public_ip.lifecycle_state == "AVAILABLE":
                    succeeded = True
                    break
                time.sleep(0.5)

            end_time = time.time()
            total_time = end_time - start_time

            if succeeded:
                print(f"实例 {instance_id}: 新的公共IP {new_public_ip.ip_address} 已成功分配")
                print(f"实例 {instance_id}: IP轮换总耗时: {total_time:.2f} 秒")
            else:
                print(f"实例 {instance_id}: 公共IP分配未在5秒内完成，但进程已启动。总耗时: {total_time:.2f} 秒")

    except Exception as e:
        print(f"实例 {instance_id}: 轮换公共IP时发生错误: {str(e)}")

# 多个实例的OCID列表
instance_ids = [
    "ocid1.instance.oc1.ap-osaka-1.anvwsljryp2xbqic3vg3bczbfzi6hrvw44t6b6vmrsxkr7d5bfavidvu3xra",
    "ocid1.instance.oc1.ap-osaka-1.anvwsljryp2xbqicihs4mk5wmxy6meb4mcel45ajwlfvsrun3i4yvkimxcvq"
]

print("IP轮换程序已启动，将每2小时执行一次...")

# 主循环
while True:
    current_time = get_china_time()
    print(f"开始执行IP轮换，当前中国时间: {current_time.strftime('%Y-%m-%d %H:%M:%S')}")
    
    for instance_id in instance_ids:
        rotate_instance_public_ip(instance_id)
    
    # 计算下一次执行时间
    next_run = current_time + datetime.timedelta(hours=2)
    next_run = next_run.replace(minute=0, second=0, microsecond=0)
    sleep_seconds = (next_run - get_china_time()).total_seconds()
    print(f"下次执行时间 (中国时间): {next_run.strftime('%Y-%m-%d %H:%M:%S')}")
    time.sleep(sleep_seconds)
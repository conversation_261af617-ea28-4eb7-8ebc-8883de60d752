const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

// 设置基础变量
const streamUrl = "https://video-weaver.lax03.hls.ttvnw.net/v1/playlist/CoAFh1xRpnlxjzLDf04ak8qnMwOU3eT3HteiEcoAMzUk6oDTrxq-vfXUx2mD8qRBUP2sj1-tblGoX-6_4MyJoH0ip18gGg5WWbKUwBPjYUTgne1eMSGJWlTP3AiJuwTZSDTt9-5eeRJUDzDJGoF8jV08dYsRKG9Smw4jsMh_eRiF9RSDSe595Y3_O53d_Gvdag0uogY6jGe2cWv1oR2pBcUEZTiaxMcKlZfcSOztMNcWmk-x8O3vUs_OtmgRudW_oy0E014Vc7Y2zebFy2Fo5WIrck724PSNjS9FXgv9aGgCwiWbfoHgm_5gUir6Eto96cGEXRB9LLAUJsREYznJn6mTJBDBba-XGAa2cj67V2hxXhK3OmtJHHPFOOW4SM9G3NW61d6WYJRjvveiBTkijWfDjultSZorin24wIE4KIs4rUHS7Ml8LCGvq0RYEyAPOBXdCbz2XdB_7Q5mjWRP1CtjqBZJ64C75OW2tuL4sjlHkY5xrowwkaXMCTfvsbmm1nXwY3k4KBhdV0ofTHEPE_VxmCC7oiVTEu1YiQXpjVWv8S3Cas8rR4ial6MOy6Qtxo0RNcLukQ_eN8Kk9y_1Evv9mWuK8APJfZWfDJcpVzEBQFS1U3cWvF-2NlYbywA9OE3qh6ZG69pF2I16BbxudspQTNnOou2GsFTWEP1OJ0vRzCVYHyqLr97YChc3RCfm4kAv0I6BTWJm_8Zo1MK9O_2wUoiH1Mu6qJpr3kRc-1d2RK0Xud5M33d4V35r-fd5zorAruyPI65ZvUF8ixiHGJXwBiEymIf-dW_8AxYVdJdJo-5vmpN6nmGYN6CqyKxAR3YVtfZHY3h2blHstnZJcVDuphoMSm3jY9fR2aZ3l6siIAEqCXVzLXdlc3QtMjCHCw.m3u8";
const anchorId = "test123";
const recordTime = new Date().toISOString();
const outputFile = path.join(__dirname, `${Date.now()}.ts`);

// 构建 ffmpeg 参数
const ffmpegArgs = [
  '-hide_banner',
  '-loglevel', 'error',
  '-user_agent', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
  '-headers', 'Referer: https://www.twitch.tv/\r\nOrigin: https://www.twitch.tv',
  '-err_detect', 'ignore_err',
  '-rw_timeout', '180000000',
  '-i', streamUrl,
  '-c', 'copy',
  '-f', 'mpegts',
  '-fs', '2G',
  '-t', '21600',
  '-reconnect', '1',
  '-reconnect_at_eof', '1',
  '-reconnect_streamed', '1',
  '-reconnect_delay_max', '2',
  '-metadata', `anchor_id=${anchorId}`,
  '-metadata', `recordtime=${recordTime}`,
  '-y',
  outputFile,
];

console.log('Starting recording...');
console.log('Output file:', outputFile);

// 启动 ffmpeg 进程
const ffmpegProcess = spawn('ffmpeg', ffmpegArgs);

// 错误输出处理
ffmpegProcess.stderr.on('data', (data) => {
  console.error('ffmpeg error:', data.toString());
});

// 标准输出处理
ffmpegProcess.stdout.on('data', (data) => {
  console.log('ffmpeg output:', data.toString());
});

// 进程结束处理
ffmpegProcess.on('close', (code) => {
  console.log('ffmpeg process exited with code', code);
});

// 进程错误处理
ffmpegProcess.on('error', (err) => {
  console.error('Failed to start ffmpeg process:', err);
});

// 优雅退出处理
process.on('SIGINT', () => {
  console.log('\nReceived SIGINT. Stopping ffmpeg...');
  ffmpegProcess.kill('SIGINT');
});

console.log('Process started. Press Ctrl+C to stop recording.');